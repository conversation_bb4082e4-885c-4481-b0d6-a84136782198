const webpack = require("webpack");
const path = require("path");
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const productionGzipExtensions = ['js', 'css'];
const fs = require('fs')
const { VantResolver } = require('unplugin-vue-components/resolvers');
const ComponentsPlugin = require('unplugin-vue-components/webpack');

function genIgnoreReg(all,list){
    let idx;
    list.forEach(module =>{
        idx = all.indexOf(module);
        if(idx !== -1) all.splice(idx,1);
    })
    return new RegExp(`(${all.join('|').replace(/\|$/,'')})`);
}

const routeList = fs.readdirSync('./src/router');

// 动态打包模块
const modules = process.env.VUE_APP_MODULES || 'all';
let ignoreReg = null;
if (typeof modules === 'string') {
    if (modules !== 'all') {
        ignoreReg = genIgnoreReg(routeList,[modules]);
    }
} else if (modules instanceof Array) {
    ignoreReg = genIgnoreReg(routeList,modules);
}

const myversion = new Date().getTime()
module.exports = {
    // devServer: {
    //     proxy: {
    //         '/Insurance': {
    //             target: 'http://*************:8090', //目标地址--api路径
    //             ws: true, //// 是否启用websockets
    //             changeOrigin: true, //开启代理：在本地会创建一个虚拟服务端，然后发送请求的数据，并同时接收请求的数据，这样服务端和服务端进行数据的交互就不会有跨域问题
    //             pathRewrite: { '^/Insurance': '' } //这里重写路径--vue端口
    //         }
    //     }
    // },
    devServer: {
        disableHostCheck: true,
        port: 80, // 端口
        // https: true,
    },
    publicPath: process.env.VUE_APP_URL || '/marketfront/insurance/',    //测试环境域名
    assetsDir: "static", //静态资源目录名称
    lintOnSave: false,
    productionSourceMap:false,//打包时不要map文件
    css: {
        loaderOptions: {
            less: {
                javascriptEnabled: true
            }
        }
    },
    configureWebpack: {
        //引入jquery
        plugins: ignoreReg? [
            new webpack.DefinePlugin({
                'process.env.VUE_APP_VERSION': myversion
            }),
            new webpack.ProvidePlugin({
                $: "jquery",
                jQuery: "jquery",
                "windows.jQuery": "jquery"
            }),
            new CompressionWebpackPlugin({
                filename: '[path].gzip[query]',   // 提示compression-webpack-plugin@2.0.0的话filename改为asset
                algorithm: 'gzip',
                test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
                threshold: 10240,
                minRatio: 0.8,
                deleteOriginalAssets:false,
            }),
            ComponentsPlugin({
              resolvers: [VantResolver()],
            }),
            new webpack.IgnorePlugin(ignoreReg,/router$/)
        ] : [
            new webpack.DefinePlugin({
                'process.env.VUE_APP_VERSION': myversion
            }),
            new webpack.ProvidePlugin({
                $: "jquery",
                jQuery: "jquery",
                "windows.jQuery": "jquery"
            }),
            new CompressionWebpackPlugin({
                filename: '[path].gzip[query]',   // 提示compression-webpack-plugin@2.0.0的话filename改为asset
                algorithm: 'gzip',
                test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
                threshold: 10240,
                minRatio: 0.8,
                deleteOriginalAssets:false,
            }),
            ComponentsPlugin({
              resolvers: [VantResolver()],
            }),

            // ignoreReg ? new webpack.IgnorePlugin(ignoreReg,/router$/) : undefined
        ],
    },
    chainWebpack: (config) => {
      const dir = path.resolve(__dirname, 'src/assets/icons') // 需要查找的路径
      const svgRule = config.module.rule('svg') // 找到svg-loader
      svgRule.uses.clear() // 清除已有的loader, 如果不这样做会添加在此loader之后
      svgRule.exclude.add(/node_modules/) // 正则匹配排除node_modules目录
      svgRule // 添加svg新的loader处理
        .test(/\.svg$/)
        .use('svg-sprite-loader')
        .loader('svg-sprite-loader')
        .options({
          symbolId: 'icon-[name]',
        })
        /* 添加分析工具*/
        if (process.env.NODE_ENV === 'production') {
            if (process.env.npm_config_report) {
                config
                    .plugin('webpack-bundle-analyzer')
                    .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)
                    .end();
                config.plugins.delete('prefetch')
            }
        }

        // 移除prefetch插件
        config.plugins.delete('prefetch');

        // 修改它的选项
        // config.plugin('prefetch').tap((options) => {
        //     options[0].fileBlacklist = options[0].fileBlacklist || [];
        //     options[0].fileBlacklist.push(/myasyncRoute(.)+?\.js$/);
        //     return options;
        // });
        const imagesRule = config.module.rule('images');
        imagesRule.uses.clear()        //清除原本的images loader配置
        imagesRule
            .test(/\.(jpg|gif|png|svg|jpeg)$/)
            .exclude
            .add(path.join(__dirname,"../node_modules"))   //不对node_modules里的图片转base64
            .add(dir).end()
            .use('url-loader')
            .loader('url-loader')
            .options({name:"img/[name].[hash:8].[ext]",limit: 1})
    },
    // chainWebpack: config => {
    //     config
    //         .module
    //         .rule("images")
    //         .test(/\.(png|jpe?g|gif|webp)(\?.*)?$/)
    //         .use('url-loader')
    //         .loader('url-loader')
    //         .options({
    //             limit:10,
    //             // 以下配置项用于配置file-loader
    //             // 根据环境使用cdn或相对路径
    //             publicPath: process.env.NODE_ENV === 'production' ? 'http://zsxn.cb.hj99.ltd/marketfront/insurance/static/img' : './',
    //             // 将图片打包到dist/img文件夹下, 不配置则打包到dist文件夹下
    //             // outputPath: 'img',
    //             // 配置打包后图片文件名
    //             // name: '[name].[ext]',
    //             // name: `[name]${1==1 ? '.[hash:8]}' : ''}.[ext]`,
    //         })
    //         .end();
    // }
};
