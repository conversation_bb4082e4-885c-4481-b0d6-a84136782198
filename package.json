{"name": "api", "version": "0.1.0", "private": true, "type": "commonjs", "scripts": {"local": "vue-cli-service serve --mode local", "serve": "vue-cli-service serve --mode serve", "build": "vue-cli-service build --mode build", "alpha": "vue-cli-service build --max-old-space-size=1024 --mode alpha", "test": "vue-cli-service build --mode test", "prod": "vue-cli-service build --mode prod", "iybprod": "vue-cli-service build --mode iybprod", "kdprod": "vue-cli-service build --mode kdprod", "yhprod": "vue-cli-service build --mode yhprod", "lint": "vue-cli-service lint"}, "dependencies": {"@fingerprintjs/fingerprintjs": "^4.6.2", "@vant/area-data": "^1.4.0", "axios": "^0.19.0", "clipboard": "^2.0.6", "core-js": "^2.6.5", "crypto-js": "^4.1.1", "janus-gateway": "1.3.1", "jquery": "^3.4.1", "less": "^3.10.1", "less-loader": "^5.0.0", "libpag-lite": "^0.0.7", "lz-string": "^1.5.0", "mint-ui": "^2.2.13", "moment": "^2.29.1", "qs": "^6.9.3", "rrweb": "^2.0.0-alpha.4", "style-loader": "^1.0.0", "svga": "^2.1.1", "vant": "^2.12.48", "vconsole": "^3.15.1", "vue": "^2.6.10", "vue-clipboard2": "^0.3.3", "vue-lazyload": "^1.3.5", "vue-router": "^3.0.3", "vue-wechat-title": "^2.0.5", "webpack-bundle-analyzer": "^4.1.0", "webrtc-adapter": "^8.2.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.11.0", "@vue/cli-plugin-eslint": "^3.11.0", "@vue/cli-service": "^3.11.0", "babel-eslint": "^10.0.1", "compression-webpack-plugin": "^4.0.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "svg-loader": "^0.0.2", "svg-sprite-loader": "^6.0.11", "unplugin-vue-components": "^0.20.1", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}