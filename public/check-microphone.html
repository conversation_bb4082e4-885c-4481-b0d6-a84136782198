<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>检测麦克风</title>
  <style>
    /*当前页面全局颜色变量*/
    :root {
      --blue-dark: #337ECC;
      --blue-normal: #409EFF;
      --blue-light: #A0CFFF;
      --green-dark: #529B2E;
      --green-normal: #67C23A;
      --green-light: #B3E19D;
      --orange-dark: #B88230;
      --orange-normal: #E6A23C;
      --orange-light: #F3D19E;
      --red-dark: #C45656;
      --red-normal: #F56C6C;
      --red-light: #FAB6B6;
      --gray-dark: #73767A;
      --gray-normal: #909399;
      --gray-light: #C8C9CC;
      --black: #303133;
      --white: #F0F2F5;
    }
    /*通用默认样式*/
    *,
    *::before,
    *::after {
      box-sizing: border-box;
      position: relative;
      margin: 0;
      padding: 0;
      border: 0;
      outline: 0;
      text-decoration: none;
    }
    html {
      width: 100%;
      height: 100%;
      font-size: 16px;
    }
    body {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 16px;
    }
    /*标题栏*/
    .header {
      width: 100%;
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }
    .header .title {
      font-weight: bold;
    }
    .header .subtitle {
      margin-top: 8px;
      font-size: 14px;
    }
    /*按钮栏*/
    .button-box {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 16px;
      gap: 20px;
    }
    .button-box .button {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 8px 12px;
      border-radius: 4px;
      color: var(--white);
      line-height: 1;
      user-select: none;
      cursor: pointer;
    }
    /*按钮在不同状态下的样式*/
    .open-button {
      background-color: var(--blue-normal);
    }
    .open-button:hover {
      background-color: var(--blue-light);
    }
    .open-button:active {
      background-color: var(--blue-dark);
    }
    .close-button {
      background-color: var(--orange-normal);
    }
    .close-button:hover {
      background-color: var(--orange-light);
    }
    .close-button:active {
      background-color: var(--orange-dark);
    }
    .pending-button {
      background-color: var(--gray-normal);
    }
    .pending-button:hover {
      background-color: var(--gray-light);
    }
    .pending-button:active {
      background-color: var(--gray-dark);
    }
    /*状态栏*/
    .status-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-top: 16px;
      gap: 8px;
    }
    .status-box .status-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 8px;
    }
    .status-box .status-title {
      font-weight: bold;
    }
  </style>
</head>
<body>
<!--标题栏-->
<div class="header">
  <div class="title">
    检测麦克风
  </div>
  <div class="subtitle">
    v1.0.0 - 20250616
  </div>
</div>

<!--按钮栏-->
<div class="button-box">
  <div class="button open-button" id="microphone-button">打开麦克风</div>
</div>

<!--状态栏-->
<div class="status-box">
  <div class="status-item">
    <div class="status-title">
      麦克风状态
    </div>
    <div class="status-content" id="microphone-result" style="color: var(--black);">
      未知
    </div>
  </div>
  <div class="status-item">
    <div class="status-title">
      媒体设备列表
    </div>
    <div class="status-content" id="audio-device-list">
      未知
    </div>
  </div>
</div>

<script>
  // 按钮展示文本枚举
  const buttonEnum = {
    pending: '正在切换',
    close: '关闭麦克风',
    open: '打开麦克风',
  };
  // 状态展示文本枚举
  const statusEnum = {
    unknown: '未知',
    pending: '加载中',
    closed: '已关闭',
    open: '已打开',
    fail: '失败',
  };

  // 需要用到的HTML DOM
  const buttonDom = document.getElementById('microphone-button');
  const statusDom = document.getElementById('microphone-result');
  const listDom = document.getElementById('audio-device-list');

  // 初始状态
  let buttonText = buttonEnum.open;
  let statusText = statusEnum.unknown;
  // 音频流
  let stream = null;

  /**
   * 更新按钮展示文本
   * 并根据文本更新按钮样式
   * @param text {string} 按钮展示文本
   */
  const updateButton = (text = '') => {
    buttonText = text;
    buttonDom.innerHTML = text;
    buttonDom.className = 'button ' + {
      [buttonEnum.open]: 'open-button',
      [buttonEnum.close]: 'close-button',
      [buttonEnum.pending]: 'pending-button',
    }[text];
  };

  /**
   * 更新状态展示文本
   * 并根据文本更新状态样式
   * @param text {string} 状态展示文本
   */
  const updateStatus = (text = '') => {
    statusText = text;
    statusDom.innerHTML = text;
    const colorMap = {
      [statusEnum.fail]: 'var(--red-normal)',
      [statusEnum.open]: 'var(--green-normal)',
      [statusEnum.closed]: 'var(--orange-normal)',
      default: 'var(--black)',
    };
    statusDom.style.color = colorMap[text] || colorMap.default;
  };

  /**
   * 延迟函数
   * @param ms {number} 延迟时间，单位毫秒
   * @returns {Promise<void>}
   */
  const delay = async (ms = 200) => {
    await new Promise(resolve => setTimeout(resolve, ms));
  };

  /**
   * 枚举媒体设备
   * @returns {Promise<void>}
   */
  const enumMediaDevices = async () => {
    try {
      // 获取媒体设备列表，将媒体设备名字汇总整理，展示在页面的列表中
      const list = await navigator.mediaDevices.enumerateDevices();
      if (Array.isArray(list)) {
        listDom.innerHTML = list.reduce((previousValue, currentValue, currentIndex) => {
          return previousValue + (currentIndex + 1) + '. ' + (currentValue.label || '未知') + '<br>';
        }, '');
      }
      listDom.style.textAlign = 'left';
    } catch (e) {
      console.error('enumMediaDevices', e);
      // alert('获取媒体设备列表时出错，请重试。');
    }
  };

  /**
   * 执行操作
   * @param action {function} 操作函数
   * @param button {string} 操作完成后按钮展示文本
   * @param status {string} 操作完成后状态展示文本
   * @param callback {function} 操作完成后回调函数
   * @returns {Promise<void>}
   */
  const performAction = async (action, button, status, callback) => {
    updateButton(buttonEnum.pending);
    updateStatus(statusEnum.pending);
    try {
      await action();
      await delay();
      updateButton(button);
      updateStatus(status);
      if (callback) {
        await callback();
      }
    } catch (e) {
      console.error('performAction', e);
      updateButton(buttonEnum.open);
      updateStatus(statusEnum.fail);
      // 根据错误类型，给出不同的提示
      if (e.name === 'NotAllowedError') {
        alert('请授予麦克风访问权限。');
      } else if (e.name === 'NotFoundError') {
        alert('未找到麦克风设备，请检查设备连接。');
      } else {
        alert('操作出错，请重试。');
      }
    }
  };

  /**
   * 关闭麦克风
   * 关闭音频流
   * @returns {Promise<void>}
   */
  const closeMicrophone = async () => {
    await performAction(
      () => {
        stream.getTracks().forEach((track) => {
          track.stop && track.stop();
        });
      },
      buttonEnum.open,
      statusEnum.closed,
      null,
    );
  };

  /**
   * 打开麦克风
   * 获取音频流
   * @returns {Promise<void>}
   */
  const openMicrophone = async () => {
    await performAction(
      async () => {
        stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: false,
        });
      },
      buttonEnum.close,
      statusEnum.open,
      enumMediaDevices,
    );
  };

  /**
   * 点击按钮事件
   * 切换按钮状态
   * @returns {Promise<void>}
   */
  const onClickButton = () => {
    if (buttonText === buttonEnum.close) {
      closeMicrophone();
    } else {
      openMicrophone();
    }
  };

  // 初始化
  buttonDom.innerHTML = buttonEnum.open;
  buttonDom.addEventListener('click', onClickButton);
  statusDom.innerHTML = statusEnum.unknown;
  enumMediaDevices()

  // 页面隐藏、最小化，或者离开、刷新页面，取消点击事件监听器
  const cancelClickListener = () => {
    buttonDom.removeEventListener('click', onClickButton);
  };
  window.addEventListener('beforeunload', cancelClickListener);
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      cancelClickListener();
    }
  });
</script>
</body>
</html>
