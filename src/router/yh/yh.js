export default  [
    {
        path: '/ZYBX/TK35/Index1', // 泰康新重疾魔方-蚂蚁链
        name: 'TK35Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK35/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK35/Index2', //
        name: 'TK35Index2',
        component: () => import('@/views/ZYBX/TaiKang/TK35/Index2'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK35/Index3', //
        name: 'TK35Index3',
        redirect: '/ZYBX/TK35/Index1',
    },
    {
        path: '/ZYBX/TK35/PayAgain',
        name: 'TK35PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK35/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK35/Upgrade',
        name: 'TK35Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK35/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK35/Result',
        name: 'TK35Result',
        component: () => import('@/views/ZYBX/TaiKang/TK35/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK35/Promotion',
        name: 'TK35Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK35/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级中间页
        },
    },
    {
        path: '/ZYBX/TK35/Promotion1',
        name: 'TK35Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK35/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
]
