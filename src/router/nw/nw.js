export default [
     /**
     * 暖哇路由
     */
     {
        path: '/ZYBX/NWQW/Index1',
        name: 'NWQWIndex1',
        component: () => import('@/views/ZYBX/NW/NWQW/Index1'),
        meta: {
            title: '', // 暖哇企微中间页
        },
    },
    {
        path: '/ZYBX/IYBNWQW/Index2',
        name: 'NWQWIndex2',
        component: () => import('@/views/ZYBX/NW/NWQW/Index2'),
        meta: {
            title: '', // 暖哇企微中间页
        },
    },
    {
        path: '/ZYBX/IYBNWQW/Index3',
        name: 'NWQWIndex3',
        component: () => import('@/views/ZYBX/NW/NWQW/Index3'),
        meta: {
            title: '', // 暖哇企微中间页
        },
    },
    {
        path: '/ZYBX/NWZA01/Index1',
        name: 'NWZA01Index1',
        redirect: '/ZYBX/NWZA02/Index1',
    },
    {
        path: '/ZYBX/NWZA01/Index2',
        name: 'NWZA01Index2',
        redirect: '/ZYBX/NWZA02/Index2',
    },
    {
        path: '/ZYBX/NWZA02/PreUW/:id', // 众安康重疾险-预核保
        component: () => import('@/views/ZYBX/NW/NWZA02/Index'),
    },
    {
        path: '/ZYBX/NWZA02/Index1',
        name: 'NWZA02Index1',
        component: () => import('@/views/ZYBX/NW/NWZA02/Index1'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-投保页
        },
    },
    {
        path: '/ZYBX/NWZA02/Index2',
        name: 'NWZA02Index2',
        component: () => import('@/views/ZYBX/NW/NWZA02/Index2'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-投保页
        },
    },
    {
        path: '/ZYBX/NWZA02/Index3',
        name: 'NWZA02Index3',
        component: () => import('@/views/ZYBX/NW/NWZA02/Index3'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-投保页
        },
    },
    {
        path: '/ZYBX/NWZA02/Index4',
        name: 'NWZA02Index4',
        component: () => import('@/views/ZYBX/NW/NWZA02/Index4'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-投保页
        },
    },
    {
        path: '/ZYBX/NWZA02/Cashier',
        name: 'NWZA02Cashier',
        component: () => import('@/views/ZYBX/NW/NWZA02/Cashier'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-升级页
        },
    },
    {
        path: '/ZYBX/NWZA02/Upgrade',
        name: 'NWZA02Upgrade',
        component: () => import('@/views/ZYBX/NW/NWZA02/Upgrade'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-升级页
        },
    },
    {
        path: '/ZYBX/NWZA02/PayAgain',
        name: 'NWZA02PayAgain',
        component: () => import('@/views/ZYBX/NW/NWZA02/PayAgain'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWZA02/Promotion',
        name: 'NWZA02Promotion',
        component: () => import('@/views/ZYBX/NW/NWZA02/Promotion'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-待升级促活
        },
    },
    {
        path: '/ZYBX/NWZA02/Promotion1',
        name: 'NWZA02Promotion1',
        component: () => import('@/views/ZYBX/NW/NWZA02/Promotion1'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-待支付促活
        },
    },
    {
        path: '/ZYBX/NWZA02/Result',
        name: 'NWZA02Result',
        component: () => import('@/views/ZYBX/NW/NWZA02/Result'),
        meta: {
            title: '众安康重疾险', // 众安康重疾险-结果页
        },
    },
    {
        path: '/ZYBX/NWZA03/PreUW/:id', // 众安享·百万医疗惠民版-预核保
        component: () => import('@/views/ZYBX/NW/NWZA03/Index'),
    },
    {
        path: '/ZYBX/NWZA03/Index1',
        name: 'NWZA03Index1',
        component: () => import('@/views/ZYBX/NW/NWZA03/Index1'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-投保页
        },
    },
    {
        path: '/ZYBX/NWZA03/Index2',
        name: 'NWZA03Index2',
        component: () => import('@/views/ZYBX/NW/NWZA06/Index1'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-投保页
        },
    },
    {
        path: '/ZYBX/NWZA03/Upgrade',
        name: 'NWZA03Upgrade',
        component: () => import('@/views/ZYBX/NW/NWZA03/Upgrade'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-升级页
        },
    },
    {
        path: '/ZYBX/NWZA03/PayAgain',
        name: 'NWZA03PayAgain',
        component: () => import('@/views/ZYBX/NW/NWZA03/PayAgain'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWZA03/Promotion',
        name: 'NWZA03Promotion',
        component: () => import('@/views/ZYBX/NW/NWZA03/Promotion'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-待升级促活
        },
    },
    {
        path: '/ZYBX/NWZA03/Promotion1',
        name: 'NWZA03Promotion1',
        component: () => import('@/views/ZYBX/NW/NWZA03/Promotion1'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-待支付促活
        },
    },
    {
        path: '/ZYBX/NWZA03/Result',
        name: 'NWZA03Result',
        component: () => import('@/views/ZYBX/NW/NWZA03/Result'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-结果页
        },
    },
    {
        path: '/ZYBX/NWZA06/PreUW/:id', // 众安享·百万医疗惠民版-预核保
        component: () => import('@/views/ZYBX/NW/NWZA06/Index'),
    },
    {
        path: '/ZYBX/NWZA06/Index1',
        name: 'NWZA06Index1',
        component: () => import('@/views/ZYBX/NW/NWZA06/Index1'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-投保页
        },
    },
    {
        path: '/ZYBX/NWZA06/Index2',
        name: 'NWZA06Index2',
        component: () => import('@/views/ZYBX/NW/NWZA06/Index2'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-投保页
        },
    },
    {
        path: '/ZYBX/NWZA06/Upgrade',
        name: 'NWZA06Upgrade',
        component: () => import('@/views/ZYBX/NW/NWZA06/Upgrade'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-升级页
        },
    },
    {
        path: '/ZYBX/NWZA06/Upgrade1',
        name: 'NWZA06Upgrade1',
        component: () => import('@/views/ZYBX/NW/NWZA06/Upgrade1'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-升级页
        },
    },
    {
        path: '/ZYBX/NWZA06/PayAgain',
        name: 'NWZA06PayAgain',
        component: () => import('@/views/ZYBX/NW/NWZA06/PayAgain'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWZA06/Promotion',
        name: 'NWZA06Promotion',
        component: () => import('@/views/ZYBX/NW/NWZA06/Promotion'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-待升级促活
        },
    },
    {
        path: '/ZYBX/NWZA06/Promotion1',
        name: 'NWZA06Promotion1',
        component: () => import('@/views/ZYBX/NW/NWZA06/Promotion1'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-待支付促活
        },
    },
    {
        path: '/ZYBX/NWZA06/Result',
        name: 'NWZA06Result',
        component: () => import('@/views/ZYBX/NW/NWZA06/Result'),
        meta: {
            title: '众安享·百万医疗惠民版', // 众安享·百万医疗惠民版-结果页
        },
    },
     {
        path: '/ZYBX/NWZA07/PreUW/:id', // 众安享·百万医疗惠民版-预核保
        component: () => import('@/views/ZYBX/NW/NWZA07/Index'),
    },
    {
        path: '/ZYBX/NWZA07/Index1',
        name: 'NWZA07Index1',
        component: () => import('@/views/ZYBX/NW/NWZA07/Index1'),
        meta: {
            title: '众安康基础版', // 众安康-投保页
        },
    },
    {
        path: '/ZYBX/NWZA07/Index2',
        name: 'NWZA07Index2',
        component: () => import('@/views/ZYBX/NW/NWZA07/Index2'),
        meta: {
            title: '众安康基础版', // 众安康-投保页
        },
    },
    {
        path: '/ZYBX/NWZA07/Index3',
        name: 'NWZA07Index3',
        component: () => import('@/views/ZYBX/NW/NWZA07/Index3'),
        meta: {
            title: '众安康基础版', // 众安康-投保页
        },
    },
    {
        path: '/ZYBX/NWZA07/Index4',
        name: 'NWZA07Index4',
        component: () => import('@/views/ZYBX/NW/NWZA07/Index4'),
        meta: {
            title: '众安康基础版', // 众安康-投保页
        },
    },
    {
        path: '/ZYBX/NWZA07/Upgrade',
        name: 'NWZA07Upgrade',
        component: () => import('@/views/ZYBX/NW/NWZA07/Upgrade'),
        meta: {
            title: '众安康基础版', // 众安康-升级页
        },
    },
    {
        path: '/ZYBX/NWZA07/PayAgain',
        name: 'NWZA07PayAgain',
        component: () => import('@/views/ZYBX/NW/NWZA07/PayAgain'),
        meta: {
            title: '众安康基础版', // 众安康-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWZA07/Promotion',
        name: 'NWZA07Promotion',
        component: () => import('@/views/ZYBX/NW/NWZA07/Promotion'),
        meta: {
            title: '众安康基础版', // 众安康-待升级促活
        },
    },
    {
        path: '/ZYBX/NWZA07/Promotion1',
        name: 'NWZA07Promotion1',
        component: () => import('@/views/ZYBX/NW/NWZA07/Promotion1'),
        meta: {
            title: '众安康基础版', // 众安康-待支付促活
        },
    },
    {
        path: '/ZYBX/NWZA07/Result',
        name: 'NWZA07Result',
        component: () => import('@/views/ZYBX/NW/NWZA07/Result'),
        meta: {
            title: '众安康月缴版', // 众安康-结果页
        }, 
    },
    {
        path: '/ZYBX/NWZA08/PreUW/:id', // 众安高价重疾-预核保
        component: () => import('@/views/ZYBX/NW/NWZA08/Index'),
    },
    {
        path: '/ZYBX/NWZA08/Index1',
        name: 'NWZA08Index1',
        component: () => import('@/views/ZYBX/NW/NWZA08/Index1'),
        meta: {
            title: '众安康基础版', // 众安康-投保页
        },
    },
    {
        path: '/ZYBX/NWZA08/Index2',
        name: 'NWZA08Index2',
        component: () => import('@/views/ZYBX/NW/NWZA08/Index2'),
        meta: {
            title: '众安康基础版', // 众安康-投保页
        },
    },
    {
        path: '/ZYBX/NWZA08/Index3',
        name: 'NWZA08Index3',
        redirect: '/ZYBX/NWZA08/Index1',
    },
    {
        path: '/ZYBX/NWZA08/Index4',
        name: 'NWZA08Index4',
        redirect: '/ZYBX/NWZA08/Index1',
    },
    {
        path: '/ZYBX/NWZA08/Upgrade',
        name: 'NWZA08Upgrade',
        component: () => import('@/views/ZYBX/NW/NWZA08/Upgrade'),
        meta: {
            title: '众安康基础版', // 众安康-升级页
        },
    },
    {
        path: '/ZYBX/NWZA08/PayAgain',
        name: 'NWZA08PayAgain',
        component: () => import('@/views/ZYBX/NW/NWZA08/PayAgain'),
        meta: {
            title: '众安康基础版', // 众安康-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWZA08/Promotion',
        name: 'NWZA08Promotion',
        component: () => import('@/views/ZYBX/NW/NWZA08/Promotion'),
        meta: {
            title: '众安康基础版', // 众安康-待升级促活
        },
    },
    {
        path: '/ZYBX/NWZA08/Promotion1',
        name: 'NWZA08Promotion1',
        component: () => import('@/views/ZYBX/NW/NWZA08/Promotion1'),
        meta: {
            title: '众安康基础版', // 众安康-待支付促活
        },
    },
    {
        path: '/ZYBX/NWZA08/Result',
        name: 'NWZA08Result',
        component: () => import('@/views/ZYBX/NW/NWZA08/Result'),
        meta: {
            title: '众安康月缴版', // 众安康-结果页
        }, 
    },
    {
        path: '/ZYBX/NWZA04/Index1',
        name: 'NWZA04Index1',
        redirect: '/ZYBX/NWZA10/Index1',
    },
    {
        path: '/ZYBX/NWZA04/Index2',
        name: 'NWZA04Index2',
        redirect: '/ZYBX/NWZA10/Index1',
    },
    {
        path: '/ZYBX/NWZA05/Index1',
        name: 'NWZA05Index1',
        redirect: '/ZYBX/NWZA10/Index1',
    },
    {
        path: '/ZYBX/NWZA05/Index2',
        name: 'NWZA05Index2',
        redirect: '/ZYBX/NWZA10/Index1',
    },
    {
        path: '/ZYBX/NWZA09/Index1',
        name: 'NWZA09Index1',
        redirect: '/ZYBX/NWZA10/Index1',
    },
    {
        path: '/ZYBX/NWZA09/Index2',
        name: 'NWZA09Index2',
        redirect: '/ZYBX/NWZA10/Index1',
    },
    {
        path: '/ZYBX/ZA15/Home/Index1',
        name: 'ZA15Index1',
        redirect: '/ZYBX/NWZA02/Index1',
    },
    {
        path: '/ZYBX/ZA15/Home/Index2',
        name: 'ZA15Index2',
        redirect: '/ZYBX/NWZA02/Index2',
    },
    {
        path: '/ZYBX/ZA15/Home/Index3',
        name: 'ZA15Index3',
        redirect: '/ZYBX/NWZA02/Index1',
    },
    {
        path: '/ZYBX/ZA15/Home/Index4',
        name: 'ZA15Index4',
        redirect: '/ZYBX/NWZA02/Index2',
    },
    {
        path: '/ZYBX/ZA15/Home/Index5',
        name: 'ZA15Index5',
        redirect: '/ZYBX/NWZA02/Index1',
    },
    {
        path: '/ZYBX/ZA15/Home/Index6',
        name: 'ZA15Index6',
        redirect: '/ZYBX/NWZA02/Index1',
    },
    {
        path: '/ZYBX/ZA15/Home/Index7',
        name: 'ZA15Index7',
        redirect: '/ZYBX/NWZA02/Index2',
    },
    {
        path: '/ZYBX/ZA15/Home/Index8',
        name: 'ZA15Index8',
        redirect: '/ZYBX/NWZA02/Index2',
    },
    {
        path: '/ZYBX/ZA15/Home/Index9',
        name: 'ZA15Index9',
        redirect: '/ZYBX/NWZA02/Index2',
    },
    {
        path: '/ZYBX/ZA16/Home/Index1',
        name: 'ZA16Index1',
        redirect: '/ZYBX/NWTK03/Index1',
    },
    {
        path: '/ZYBX/NWZAZX01/Index1',
        name: 'NWZAZX01Index1',
        component: () => import('@/views/ZYBX/NW/NWZAZX01/Index1'),
        meta: {
            title: '周周免费领', // 暖哇众安赠险
        },
    },
    {
        path: '/ZYBX/NWZAZX01/Index2',
        name: 'NWZAZX01Index2',
        redirect: '/ZYBX/NWZAZX01/Index3',
    },
    {
        path: '/ZYBX/NWZAZX01/Index3',
        name: 'NWZAZX01Index3',
        component: () => import('@/views/ZYBX/NW/NWZAZX01/Index3'),
        meta: {
            title: '周周免费领', // 暖哇众安赠险
        },
    },
    {
        path: '/ZYBX/NWZAZX01/Index',
        name: 'NWZAZX01Index',
        component: () => import('@/views/ZYBX/NW/NWZAZX01/Index'),
        meta: {
            title: '周周免费领', // 暖哇众安赠险
        },
    },
    {
        path: '/ZYBX/NWZAZX03/Index',
        name: 'NWZAZX03Index',
        component: () => import('@/views/ZYBX/NW/NWZAZX03/Index'),
        meta: {
            title: '', // 暖哇众安和太平聚合
        },
    },
    {
        path: '/ZYBX/NWZAZX03/Index1',
        name: 'NWZAZX03Index1',
        component: () => import('@/views/ZYBX/NW/NWZAZX03/Index1'),
        meta: {
            title: '免费领百万保障', // 暖哇众安
        },
    },
    {
        path: '/ZYBX/NWZAZX03/Index2',
        name: 'NWZAZX03Index2',
        component: () => import('@/views/ZYBX/NW/NWZAZX03/Index2'),
        meta: {
            title: '免费领百万保障', // 暖哇众安
        },
    },
    {
        path: '/ZYBX/NWZAZX03/Index3',
        name: 'NWZAZX03Index3',
        component: () => import('@/views/ZYBX/NW/NWZAZX03/Index3'),
        meta: {
            title: '免费领百万保障', // 暖哇众安
        },
    },
    {
        path: '/ZYBX/NWZAZX03/Index4',
        name: 'NWZAZX03Index4',
        component: () => import('@/views/ZYBX/NW/NWZAZX03/Index4'),
        meta: {
            title: '免费领百万保障', // 暖哇众安 - 对比测试
        },
    },
    {
        path: '/ZYBX/NWZAZX03/Index5',
        name: 'NWZAZX03Index5',
        component: () => import('@/views/ZYBX/NW/NWZAZX03/Index5'),
        meta: {
            title: '免费领百万保障', // 暖哇众安 - 对比测试
        },
    },
    {
        path: '/ZYBX/NWZAZX03/Index6',
        name: 'NWZAZX03Index6',
        component: () => import('@/views/ZYBX/NW/NWZAZX03/Index6'),
        meta: {
            title: '免费领百万保障', // 暖哇众安 - 对比测试
        },
    },
    {
        path: '/ZYBX/NWZAZX03/Index7',
        name: 'NWZAZX03Index7',
        component: () => import('@/views/ZYBX/NW/NWZAZX03/Index7'),
        meta: {
            title: '免费领百万保障', // 暖哇众安 - 对比测试
        },
    },
    {
        path: '/ZYBX/NWZAZX03/Index8',
        name: 'NWZAZX03Index8',
        component: () => import('@/views/ZYBX/NW/NWZAZX03/Index8'),
        meta: {
            title: '免费领百万保障', // 暖哇众安 - 对比测试
        },
    },
    {
        path: '/ZYBX/NWZAZX04/Index1',
        name: 'NWZAZX04Index1',
        component: () => import('@/views/ZYBX/NW/NWZAZX04/Index1'),
        meta: {
            title: '免费领百万保障', // 暖哇众安和双太平聚合
        },
    },
    {
        path: '/ZYBX/NWZAZX04/Index2',
        name: 'NWZAZX04Index2',
        component: () => import('@/views/ZYBX/NW/NWZAZX04/Index2'),
        meta: {
            title: '免费领百万保障', // 暖哇众安和双太平聚合
        },
    },

    {
        path: '/ZYBX/NWDD01/Index1',
        name: 'NWDD01Index1',
        component: () => import('@/views/ZYBX/NW/NWDD01/Index1'),
        meta: {
            title: '安心保·百万医疗险', // 安心保·百万医疗险普惠版
        },
    },
    {
        path: '/ZYBX/NWDD01/Index2',
        name: 'NWDD01Index2',
        redirect: '/ZYBX/NWDD01/Index1',
    },
    {
        path: '/ZYBX/NWDD01/Index3',
        name: 'NWDD01Index3',
        redirect: '/ZYBX/NWDD01/Index1',
    },
    {
        path: '/ZYBX/NWDD01/Upgrade',
        name: 'NWDD01Upgrade',
        component: () => import('@/views/ZYBX/NW/NWDD01/Upgrade'),
        meta: {
            title: '安心保·百万医疗险', // 安心保·百万医疗险普惠版-升级页
        },
    },
    {
        path: '/ZYBX/NWDD01/PayAgain',
        name: 'NWDD01PayAgain',
        component: () => import('@/views/ZYBX/NW/NWDD01/PayAgain'),
        meta: {
            title: '安心保·百万医疗险', // 安心保·百万医疗险普惠版-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWDD01/Promotion',
        name: 'NWDD01Promotion',
        component: () => import('@/views/ZYBX/NW/NWDD01/Promotion'),
        meta: {
            title: '安心保·百万医疗险', // 安心保·百万医疗险普惠版-待升级促活
        },
    },
    {
        path: '/ZYBX/NWDD01/Promotion1',
        name: 'NWDD01Promotion1',
        component: () => import('@/views/ZYBX/NW/NWDD01/Promotion1'),
        meta: {
            title: '安心保·百万医疗险', // 安心保·百万医疗险普惠版-待支付促活
        },
    },
    {
        path: '/ZYBX/NWDD01/Result',
        name: 'NWDD01Result',
        component: () => import('@/views/ZYBX/NW/NWDD01/Result'),
        meta: {
            title: '安心保·百万医疗险', // 安心保·百万医疗险普惠版-结果页
        },
    },
    {
        path: '/ZYBX/NWGR01/Index1',
        name: 'NWGR01Index1',
        redirect: '/ZYBX/NWDD01/Index1',
    },
    {
        path: '/ZYBX/NWGR01/Index2',
        name: 'NWGR01Index2',
        redirect: '/ZYBX/NWDD01/Index1',
    },
    {
        path: '/ZYBX/NWZL01/Index1',
        name: 'NWZL01Index1',
        redirect: '/ZYBX/NWDD01/Index1',
    },
    {
        path: '/ZYBX/NWZL01/Index2',
        name: 'NWZL01Index2',
        redirect: '/ZYBX/NWDD01/Index1',
    },
    {
        path: '/ZYBX/NWTK01/Index1',
        name: 'NWTK01Index1',
        component: () => import('@/views/ZYBX/NW/NWTK01/Index1'),
        meta: {
            title: '泰医保·防癌险惠民版', // 泰医保·防癌险惠民版
        },
    },
    {
        path: '/ZYBX/NWTK01/Index2',
        name: 'NWTK01Index2',
        redirect: '/ZYBX/NWTK01/Index1',
    },
    {
        path: '/ZYBX/NWTK01/Index3',
        name: 'NWTK01Index3',
        redirect: '/ZYBX/NWTK01/Index1',
    },
    {
        path: '/ZYBX/NWTK01/Upgrade',
        name: 'NWTK01Upgrade',
        component: () => import('@/views/ZYBX/NW/NWTK01/Upgrade'),
        meta: {
            title: '泰医保·防癌险惠民版', // 泰医保·防癌险惠民版-升级页
        },
    },
    {
        path: '/ZYBX/NWTK01/PayAgain',
        name: 'NWTK01PayAgain',
        component: () => import('@/views/ZYBX/NW/NWTK01/PayAgain'),
        meta: {
            title: '泰医保·防癌险惠民版', // 泰医保·防癌险惠民版-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWTK01/Promotion',
        name: 'NWTK01Promotion',
        component: () => import('@/views/ZYBX/NW/NWTK01/Promotion'),
        meta: {
            title: '泰医保·防癌险惠民版', // 泰医保·防癌险惠民版-待升级促活
        },
    },
    {
        path: '/ZYBX/NWTK01/Promotion1',
        name: 'NWTK01Promotion1',
        component: () => import('@/views/ZYBX/NW/NWTK01/Promotion1'),
        meta: {
            title: '泰医保·防癌险惠民版', // 泰医保·防癌险惠民版-待支付促活
        },
    },
    {
        path: '/ZYBX/NWTK01/Result',
        name: 'NWTK01Result',
        component: () => import('@/views/ZYBX/NW/NWTK01/Result'),
        meta: {
            title: '泰医保·防癌险', // 泰医保·防癌险惠民版-结果页
        },
    },
    {
        path: '/ZYBX/NWTK02/Index1',
        name: 'NWTK02Index1',
        component: () => import('@/views/ZYBX/NW/NWTK02/Index1'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版
        },
    },
    {
        path: '/ZYBX/NWTK02/Index2',
        name: 'NWTK02Index2',
        component: () => import('@/views/ZYBX/NW/NWTK02/Index2'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版 对比测试Index1
        },
    },
    {
        path: '/ZYBX/NWTK02/Index3',
        name: 'NWTK02Index3',
        redirect: '/ZYBX/NWTK02/Index1',
    },
    {
        path: '/ZYBX/NWTK02/Upgrade',
        name: 'NWTK02Upgrade',
        component: () => import('@/views/ZYBX/NW/NWTK02/Upgrade'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-升级页
        },
    },
    {
        path: '/ZYBX/NWTK02/PayAgain',
        name: 'NWTK02PayAgain',
        component: () => import('@/views/ZYBX/NW/NWTK02/PayAgain'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWTK02/Promotion',
        name: 'NWTK02Promotion',
        component: () => import('@/views/ZYBX/NW/NWTK02/Promotion'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-待升级促活
        },
    },
    {
        path: '/ZYBX/NWTK02/Promotion1',
        name: 'NWTK02Promotion1',
        component: () => import('@/views/ZYBX/NW/NWTK02/Promotion1'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-待支付促活
        },
    },
    {
        path: '/ZYBX/NWTK02/Result',
        name: 'NWTK02Result',
        component: () => import('@/views/ZYBX/NW/NWTK02/Result'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗普惠版-结果页
        },
    },
    {
        path: '/ZYBX/NWTK03/PreUW/:id', // 泰康京东支付-预核保
        component: () => import('@/views/ZYBX/NW/NWTK03/Index'),
    },
    {
        path: '/ZYBX/NWTK03/Index1',
        name: 'NWTK03Index1',
        component: () => import('@/views/ZYBX/NW/NWTK03/Index1'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版
        },
    },
    {
        path: '/ZYBX/NWTK03/Index2',
        name: 'NWTK03Index2',
        component: () => import('@/views/ZYBX/NW/NWTK03/Index2'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版 对比测试Index1
        },
    },
    {
        path: '/ZYBX/NWTK03/Index3',
        name: 'NWTK03Index3',
        component: () => import('@/views/ZYBX/NW/NWTK03/Index3'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版 开场动画 旁白音频
        },
    },
    {
        path: '/ZYBX/NWTK03/Index4',
        name: 'NWTK03Index4',
        component: () => import('@/views/ZYBX/NW/NWTK03/Index4'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版 开场动画 旁白音频
        },
    },
    {
        path: '/ZYBX/NWTK03/Index5',
        name: 'NWTK03Index5',
        component: () => import('@/views/ZYBX/NW/NWTK03/Index5'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版 开场动画 旁白音频
        },
    },
    {
        path: '/ZYBX/NWTK03/Index6',
        name: 'NWTK03Index6',
        component: () => import('@/views/ZYBX/NW/NWTK03/Index6'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版 开场动画 旁白音频
        },
    },
    {
        path: '/ZYBX/NWTK03/Index11',
        name: 'NWTK03Index11',
        component: () => import('@/views/ZYBX/NW/NWTK03/Index11'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版
        },
    },
    {
        path: '/ZYBX/NWTK03/Upgrade',
        name: 'NWTK03Upgrade',
        component: () => import('@/views/ZYBX/NW/NWTK03/Upgrade'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-升级页
        },
    },
    {
        path: '/ZYBX/NWTK03/PayAgain',
        name: 'NWTK03PayAgain',
        component: () => import('@/views/ZYBX/NW/NWTK03/PayAgain'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWTK03/Promotion',
        name: 'NWTK03Promotion',
        component: () => import('@/views/ZYBX/NW/NWTK03/Promotion'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-待升级促活
        },
    },
    {
        path: '/ZYBX/NWTK03/Promotion2',
        name: 'NWTK03Promotion2',
        component: () => import('@/views/ZYBX/NW/NWTK03/Promotion2'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-待升级促活
        },
    },
    {
        path: '/ZYBX/NWTK03/Promotion3',
        name: 'NWTK03Promotion3',
        component: () => import('@/views/ZYBX/NW/NWTK03/Promotion3'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-待升级促活
        },
    },
    {
        path: '/ZYBX/NWTK03/Promotion1',
        name: 'NWTK03Promotion1',
        component: () => import('@/views/ZYBX/NW/NWTK03/Promotion1'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-待支付促活
        },
    },
    {
        path: '/ZYBX/NWTK03/Promotion4',
        name: 'NWTK03Promotion4',
        component: () => import('@/views/ZYBX/NW/NWTK03/Promotion4'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-待支付促活
        },
    },
    {
        path: '/ZYBX/NWTK03/Promotion5',
        name: 'NWTK03Promotion5',
        component: () => import('@/views/ZYBX/NW/NWTK03/Promotion5'),
        meta: {
            title: '全能保·百万医疗普惠版', // 全能保·百万医疗普惠版-待支付促活
        },
    },
    {
        path: '/ZYBX/NWTK03/Result',
        name: 'NWTK03Result',
        component: () => import('@/views/ZYBX/NW/NWTK03/Result'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗普惠版-结果页
        },
    },
    {
        path: '/ZYBX/NWTK04/PreUW/:id', // 泰康京东支付-预核保
        component: () => import('@/views/ZYBX/NW/NWTK04/Index'),
    },
    {
        path: '/ZYBX/NWTK04/Index1',
        name: 'NWTK04Index1',
        component: () => import('@/views/ZYBX/NW/NWTK04/Index1'),
        meta: {
            title: '泰爱保·重疾险普惠版', // 泰爱保·重疾险普惠版
        },
    },
    {
        path: '/ZYBX/NWTK04/Upgrade',
        name: 'NWTK04Upgrade',
        component: () => import('@/views/ZYBX/NW/NWTK04/Upgrade'),
        meta: {
            title: '泰爱保·重疾险普惠版', // 泰爱保·重疾险普惠版-升级页
        },
    },
    {
        path: '/ZYBX/NWTK04/PayAgain',
        name: 'NWTK04PayAgain',
        component: () => import('@/views/ZYBX/NW/NWTK04/PayAgain'),
        meta: {
            title: '泰爱保·重疾险普惠版', // 泰爱保·重疾险普惠版-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWTK04/Result',
        name: 'NWTK04Result',
        component: () => import('@/views/ZYBX/NW/NWTK04/Result'),
        meta: {
            title: '泰爱保·重疾险尊享版', // 泰爱保·重疾险尊享版-结果页
        },
    },
    {
        path: '/ZYBX/NWZA10/PreUW/:id', // 百万医疗险普惠版2024-预核保
        redirect: '/ZYBX/NWZA12/PreUW/:id',
    },
    {
        path: '/ZYBX/NWZA10/Index1',
        name: 'NWZA10Index1',
        redirect: '/ZYBX/NWZA12/Index4',
    },
    {
        path: '/ZYBX/NWZA10/Index2',
        name: 'NWZA10Index2',
        redirect: '/ZYBX/NWZA12/Index4',
    },
    {
        path: '/ZYBX/NWZA10/Index3',
        name: 'NWZA10Index3',
        redirect: '/ZYBX/NWZA12/Index4',
    },
    {
        path: '/ZYBX/NWZA11/PreUW/:id', // 百万医疗险普惠版2024-预核保
        redirect: '/ZYBX/NWZA12/PreUW/:id',
    },
    {
        path: '/ZYBX/NWZA11/Index1',
        name: 'NWZA11Index1',
        redirect: '/ZYBX/NWZA12/Index4',
    },
    {
        path: '/ZYBX/NWZA12/PreUW/:id', // 百万医疗险普惠版2024-预核保
        component: () => import('@/views/ZYBX/NW/NWZA12/Index'),
    },
    {
        path: '/ZYBX/NWZA12/Index1',
        name: 'NWZA12Index1',
        component: () => import('@/views/ZYBX/NW/NWZA12/Index1'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页
        },
    },
    {
        path: '/ZYBX/NWZA12/Index2',
        name: 'NWZA12Index2',
        component: () => import('@/views/ZYBX/NW/NWZA12/Index2'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页 对比测试 不同头图
        },
    },
    {
        path: '/ZYBX/NWZA12/Index3',
        name: 'NWZA12Index3',
        component: () => import('@/views/ZYBX/NW/NWZA12/Index3'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页 对比测试 提交保单加载动画
        },
    },
    {
        path: '/ZYBX/NWZA12/Index4',
        name: 'NWZA12Index4',
        component: () => import('@/views/ZYBX/NW/NWZA12/Index4'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页 停留弹窗
        },
    },
    {
        path: '/ZYBX/NWZA12/Index5',
        name: 'NWZA12Index5',
        component: () => import('@/views/ZYBX/NW/NWZA12/Index5'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页 停留弹窗 旁白音频
        },
    },
    {
        path: '/ZYBX/NWZA12/Index6',
        name: 'NWZA12Index6',
        component: () => import('@/views/ZYBX/NW/NWZA12/Index6'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页 停留弹窗
        },
    },
    {
        path: '/ZYBX/NWZA12/Index7',
        name: 'NWZA12Index7',
        component: () => import('@/views/ZYBX/NW/NWZA12/Index7'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页 停留弹窗 旁白音频 新头图
        },
    },
    {
        path: '/ZYBX/NWZA12/Index8',
        name: 'NWZA12Index8',
        component: () => import('@/views/ZYBX/NW/NWZA12/Index8'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页 停留弹窗 旁白音频
        },
    },
    {
        path: '/ZYBX/NWZA12/Index9',
        name: 'NWZA12Index9',
        component: () => import('@/views/ZYBX/NW/NWZA12/Index9'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页 停留弹窗 旁白音频
        },
    },
    {
        path: '/ZYBX/NWZA12/Upgrade',
        name: 'NWZA12Upgrade',
        component: () => import('@/views/ZYBX/NW/NWZA12/Upgrade'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-升级页
        },
    },
    {
        path: '/ZYBX/NWZA12/PayAgain',
        name: 'NWZA12PayAgain',
        component: () => import('@/views/ZYBX/NW/NWZA12/PayAgain'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWZA12/Promotion',
        name: 'NWZA12Promotion',
        component: () => import('@/views/ZYBX/NW/NWZA12/Promotion'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-待升级促活
        },
    },
    {
        path: '/ZYBX/NWZA12/Promotion1',
        name: 'NWZA12Promotion1',
        component: () => import('@/views/ZYBX/NW/NWZA12/Promotion1'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-待支付促活
        },
    },
    {
        path: '/ZYBX/NWZA12/Result',
        name: 'NWZA12Result',
        component: () => import('@/views/ZYBX/NW/NWZA12/Result'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-结果页
        },
    },
    {
        path: '/ZYBX/NWZA13/PreUW/:id', // 百万医疗险普惠版2024-预核保
        redirect: '/ZYBX/NWZA12/PreUW/:id',
    },
    {
        path: '/ZYBX/NWZA13/Index1',
        name: 'NWZA13Index1',
        redirect: '/ZYBX/NWZA12/Index4',
    },
    {
        path: '/ZYBX/NWZA14/PreUW/:id', // 百万医疗险普惠版2024-预核保
        redirect: '/ZYBX/NWZA12/PreUW/:id',
    },
    {
        path: '/ZYBX/NWZA14/Index1',
        name: 'NWZA14Index1',
        redirect: '/ZYBX/NWZA12/Index4',
    },
    {
        path: '/ZYBX/NWZA16/PreUW/:id', // 百万医疗险普惠版2024-预核保
        component: () => import('@/views/ZYBX/NW/NWZA16/Index'),
    },
    {
        path: '/ZYBX/NWZA16/Index1',
        name: 'NWZA16Index1',
        component: () => import('@/views/ZYBX/NW/NWZA16/Index1'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-投保页
        },
    },
    {
        path: '/ZYBX/NWZA16/Upgrade',
        name: 'NWZA16Upgrade',
        component: () => import('@/views/ZYBX/NW/NWZA16/Upgrade'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-升级页
        },
    },
    {
        path: '/ZYBX/NWZA16/PayAgain',
        name: 'NWZA16PayAgain',
        component: () => import('@/views/ZYBX/NW/NWZA16/PayAgain'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-支付挽回页
        },
    },
    {
        path: '/ZYBX/NWZA16/Promotion',
        name: 'NWZA16Promotion',
        component: () => import('@/views/ZYBX/NW/NWZA16/Promotion'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-待升级促活
        },
    },
    {
        path: '/ZYBX/NWZA16/Promotion1',
        name: 'NWZA16Promotion1',
        component: () => import('@/views/ZYBX/NW/NWZA16/Promotion1'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-待支付促活
        },
    },
    {
        path: '/ZYBX/NWZA16/Result',
        name: 'NWZA16Result',
        component: () => import('@/views/ZYBX/NW/NWZA16/Result'),
        meta: {
            title: '百万医疗险普惠版2024', // 百万医疗险普惠版2024-结果页
        },
    },
    {
        path: '/ZYBX/NWTPZX01/Index1',
        name: 'NWTPZX01Index1',
        redirect: '/ZYBX/NWTPZX02/Index1',
    },
    {
        path: '/ZYBX/NWTPZX01/Index10',
        name: 'NWTPZX01Index10',
        redirect: '/ZYBX/NWTPZX02/Index1',
    },
    {
        path: '/ZYBX/NWTPZX02/Index1',
        name: 'NWTPZX02Index1',
        component: () => import('@/views/ZYBX/NW/NWTPZX02/Index10'),
        meta: {
            title: '免费领百万保障', // 免费领百万保障
        },
    },
    {
        path: '/ZYBX/NWTPZX02/Index10',
        name: 'NWTPZX02Index10',
        component: () => import('@/views/ZYBX/NW/NWTPZX02/Index10'),
        meta: {
            title: '免费领百万保障', // 免费领百万保障
        },
    },
    {
        path: '/ZYBX/NWTPYZX01/Index1',
        name: 'NWTPYZX01Index1',
        redirect: '/ZYBX/NWTPZX02/Index1',
    },
    {
        path: '/ZYBX/NWTPYZX01/Index10',
        name: 'NWTPYZX01Index10',
        redirect: '/ZYBX/NWTPZX02/Index1',
    },
    {
        path: '/ZYBX/NWTPYZX01/Index11',
        name: 'NWTPYZX01Index11',
        redirect: '/ZYBX/NWTPZX02/Index1',
    },
    {
        path: '/ZYBX/NWTPYZX01/Index12',
        name: 'NWTPYZX01Index12',
        redirect: '/ZYBX/NWTPZX02/Index1',
    },
    {
        path: '/ZYBX/NWTPY01/Index1',
        name: 'NWTPY01Index1',
        redirect: '/ZYBX/NWDD01/Index1',
    },
    {
        path: '/ZYBX/NWTPY01/Index2',
        name: 'NWTPY01Index2',
        redirect: '/ZYBX/NWDD01/Index1',
    },
    {
        path: '/ZYBX/YBHNZX01/Index1',
        name: 'YBHNZX01Index1',
        component: () => import('@/views/ZYBX/Other/Poll/Index12'),
    },
    {
        path: '/ZYBX/YBHNZX01/Index10',
        name: 'YBHNZX01Index10',
        redirect: '/ZYBX/YBHNZX01/Index12',
    },
    {
        path: '/ZYBX/YBHNZX01/Index11',
        name: 'YBHNZX01Index11',
        redirect: '/ZYBX/YBHNZX01/Index12',
    },
    {
        path: '/ZYBX/YBHNZX01/Index12',
        name: 'YBHNZX01Index12',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX01/Index12'),
        meta: {
            title: '免费领保障', // 凯森众惠华农赠险
        },
    },
    {
        path: '/ZYBX/YBHNZX01/Index13',
        name: 'YBHNZX01Index13',
        redirect: '/ZYBX/YBHNZX01/Index12',
    },
    {
        path: '/ZYBX/YBHNZX01/Index14',
        name: 'YBHNZX01Index14',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX01/Index14'),
        meta: {
            title: '免费领保障', // 凯森众惠华农赠险
        },
    },
    {
        path: '/ZYBX/YBHNZX01/Index15',
        name: 'YBHNZX01Index15',
        redirect: '/ZYBX/YBHNZX01/Index12',
    },
    {
        path: '/ZYBX/YBHNZX02/Index1',
        name: 'YBHNZX02Index1',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX02/Index1'),
    },
    {
        path: '/ZYBX/YBHNZX02/Index10',
        name: 'YBHNZX02Index10',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX02/Index10'),
        meta: {
            title: '免费领保障', // 凯森国任华农赠险
        },
    },
    {
        path: '/ZYBX/YBHNZX02/Index11',
        name: 'YBHNZX02Index11',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX02/Index11'),
        meta: {
            title: '免费领保障', // 凯森国任华农赠险
        },
    },
    {
        path: '/ZYBX/YBHNZX03/Index1',
        name: 'YBHNZX03Index1',
        redirect: '/ZYBX/YBHNZX03/Index10',
    },
    {
        path: '/ZYBX/YBHNZX03/Index10',
        name: 'YBHNZX03Index10',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX03/Index10'),
        meta: {
            title: '免费领保障', // 优保华瑞赠险，旁白音频
        },
    },
    {
        path: '/ZYBX/YBHNZX03/Index11',
        name: 'YBHNZX03Index11',
        redirect: '/ZYBX/YBHNZX03/Index14',
    },
    {
        path: '/ZYBX/YBHNZX03/Index12',
        name: 'YBHNZX03Index12',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX03/Index12'),
        meta: {
            title: '免费领保障', // 优保华瑞赠险，旁白音频
        },
    },
    {
        path: '/ZYBX/YBHNZX03/Index13',
        name: 'YBHNZX03Index13',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX03/Index13'),
        meta: {
            title: '免费领保障', // 优保华瑞赠险，旁白音频
        },
    },
    {
        path: '/ZYBX/YBHNZX03/Index14',
        name: 'YBHNZX03Index14',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX03/Index14'),
        meta: {
            title: '免费领保障', // 优保华瑞赠险，旁白音频，自动填写手机号（反显）
        },
    },
    {
        path: '/ZYBX/YBHNZX03/Index15',
        name: 'YBHNZX03Index15',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX03/Index15'),
        meta: {
            title: '免费领保障', // 优保华瑞赠险，旁白音频，手动填写手机号（不反显）
        },
    },
    {
        path: '/ZYBX/YBHNZX03/Index16',
        name: 'YBHNZX03Index16',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX03/Index16'),
        meta: {
            title: '免费领保障', // 优保华瑞赠险，旁白音频，手动填写手机号（不反显），复制自15，更换头图
        },
    },
    {
        path: '/ZYBX/YBHNZX03/IndexTest',
        name: 'YBHNZX03IndexTest',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX03/IndexTest'),
        meta: {
            title: '免费领保障', // 优保华瑞赠险，测试H5双向语音通话
        },
    },
    {
        path: '/ZYBX/YBHNZX04/Index14',
        name: 'YBHNZX04Index14',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX04/Index14'),
        meta: {
            title: '免费领保障', // 优保华瑞赠险，旁白音频，手动填写手机号
        },
    },
    {
        path: '/ZYBX/YBHNZX05/Index10',
        name: 'YBHNZX05Index10',
        component: () => import('@/views/ZYBX/ZAZY/YBHNZX05/Index10'),
        meta: {
            title: '免费领保障', // 
        },
    },
    {
        path: '/ZYBX/ZAZYZHZX01/Home',
        name: 'YBZHZX01Index',
        redirect: '/ZYBX/YBZHZX01/Home10',
    },
    {
        path: '/ZYBX/ZAZYZHZX01/Home01',
        name: 'YBZHZX01Index01',
        redirect: '/ZYBX/YBZHZX01/Home10',
    },
    {
        path: '/ZYBX/ZAZYZHZX01/Home02',
        name: 'YBZHZX01Index02',
        redirect: '/ZYBX/YBZHZX01/Home10',
    },
    {
        path: '/ZYBX/ZAZYZHZX01/Home10',
        name: 'YBZHZX01Index10',
        component: () => import('@/views/ZYBX/ZAZY/YBZHZX01/Index10'),
        meta: {
            title: '免费领保障', 
        },
    },
    {
        path: '/ZYBX/ZAZYZHZX01/Home13',
        name: 'YBZHZX01Index13',
        component: () => import('@/views/ZYBX/ZAZY/YBZHZX01/Index13'),
        meta: {
            title: '免费领保障', 
        },
    },
    {
        path: '/ZYBX/ZAZYZHZX01/Home14',
        name: 'YBZHZX01Index14',
        component: () => import('@/views/ZYBX/ZAZY/YBZHZX01/Index14'),
        meta: {
            title: '免费领保障',
        },
    },
    {
        path: '/ZYBX/ZAZYZengX05/Index01',
        name: 'ZAZYZengX05Index01',
        redirect: '/ZYBX/RXHHNZX01/Index11',
    },
    {
        path: '/ZYBX/ZAZYZengX04/Index01',
        name: 'ZAZYZengX04Index01',
        redirect: '/ZYBX/RXHHNZX02/Index11',
    },
    {
        path: '/ZYBX/ZAZYZengX04/Index11',
        name: 'ZAZYZengX04Index11',
        redirect: '/ZYBX/RXHHNZX02/Index11',
    },
    {
        path: '/ZYBX/ZAZYZengX04/Index12',
        name: 'ZAZYZengX04Index12',
        redirect: '/ZYBX/RXHHNZX02/Index11',
    },
    {
        path: '/ZYBX/ZAZYZengX03/Index1',
        name: 'ZAZYZengX03Index1',
        redirect: '/ZYBX/YBHNZX03/Index1'
    },
    {
        path: '/ZYBX/ZAZYZengX03/Index2',
        name: 'ZAZYZengX03Index2',
        redirect: '/ZYBX/ZAZYZengX03/Index1'
    },
    {
        path: '/ZYBX/ZAZYZengX03/Index3',
        name: 'ZAZYZengX03Index3',
        redirect: '/ZYBX/ZAZYZengX03/Index1'
    },
    {
        path: '/ZYBX/ZAZYZengX03/Index4',
        name: 'ZAZYZengX03Index4',
        redirect: '/ZYBX/ZAZYZengX03/Index1'
    },
    {
        path: '/ZYBX/ZAZYZengX03/Index5',
        name: 'ZAZYZengX03Index5',
        redirect: '/ZYBX/ZAZYZengX03/Index1'
    },
    {
        path: '/ZYBX/ZAZYZengX03/Index6',
        name: 'ZAZYZengX03Index6',
        redirect: '/ZYBX/ZAZYZengX03/Index1'
    },
    {
        path: '/ZYBX/ZAZYZengX03/Index7',
        name: 'ZAZYZengX03Index7',
        redirect: '/ZYBX/ZAZYZengX03/Index1'
    },
    {
        path: '/ZYBX/ZAZYZengX02/Index1',
        name: 'ZAZYZengX02Index1',
        redirect: '/ZYBX/ZAZYZengX02/Index3'
    },
    {
        path: '/ZYBX/ZAZYZengX02/Index2',
        name: 'ZAZYZengX02Index2',
        redirect: '/ZYBX/ZAZYZengX02/Index3'
    },
    {
        path: '/ZYBX/ZAZYZengX02/Index3',
        name: 'ZAZYZengX02Index3',
        redirect: '/ZYBX/YBHNZX03/Index1'
    },
    {
        path: '/ZYBX/ZAZYZengX01/Index1',
        name: 'ZAZYZengX01Index1',
        component: () => import('@/views/Exception/400'),
        meta: {
            title: '', // 众安联运 -账户安全险
        },
    },
    {
        path: '/ZYBX/ZAZYZengX01/Result',
        name: 'ZAZYZengX01Result',
        component: () => import('@/views/Exception/400'),
        meta: {
            title: '', // 众安联运 -账户安全险
        },
    },
    {
        path: '/ZYBX/ZAZY02/Home/Index1',
        name: 'ZAZY02Index1',
        redirect: '/ZYBX/YBZA08/Index1',
    },
    {
      path: '/ZYBX/ZAZY02/Home/Index2',
      name: 'ZAZY02Index2',
      redirect: '/ZYBX/YBZA08/Index1',
    },
    {
        path: '/ZYBX/ZAZY02/Home/Index3',
        name: 'ZAZY02Index3',
        redirect: '/ZYBX/YBZA08/Index1',
    },
    {
        path: '/ZYBX/ZAZY02/Home/Index4',
        name: 'ZAZY02Index4',
        redirect: '/ZYBX/YBZA08/Index1',
    },
    {
      path: '/ZYBX/ZAZY04/Home/Index1',
      name: 'ZAZY04Index1',
      redirect: '/ZYBX/YBZA08/Index1',
    },
    {
      path: '/ZYBX/ZAZY04/Home/Index2',
      name: 'ZAZY04Index2',
      redirect: '/ZYBX/YBZA08/Index1',
    },
    {
        path: '/ZYBX/ZAZY04/Home/Index3',
        name: 'ZAZY04Index3',
        redirect: '/ZYBX/YBZA08/Index1',
    },
    {
        path: '/ZYBX/ZAZY05/Home/Index1',
        name: 'ZAZY05Index1',
        redirect: '/ZYBX/YBZA08/Index1',
    },
    {
        path: '/ZYBX/ZAZY05/Home/Index2',
        name: 'ZAZY05Index2',
        redirect: '/ZYBX/YBZA08/Index2',
    },
    {
        path: '/ZYBX/ZAZY07/PreUW/:id', // 优保众惠-预核保
        component: () => import('@/views/ZYBX/ZAZY/ZA07/Index'),
    },
    {
        path: '/ZYBX/ZAZY07/Home/Index1',
        name: 'ZAZY07Index1',
        component: () => import('@/views/ZYBX/ZAZY/ZA07/Index1'),
        meta: {
            title: '众惠随心百万医疗', // 众惠随心百万普惠版-投保页-支付宝
        },
    },
    {
        path: '/ZYBX/ZAZY07/Home/Index3',
        name: 'ZAZY07Index3',
        component: () => import('@/views/ZYBX/ZAZY/ZA07/Index3'),
        meta: {
            title: '众惠随心百万医疗', // 众惠随心百万普惠版-投保页-支付宝
        },
    },
    {
        path: '/ZYBX/ZAZY07/Home/Index2',
        name: 'ZAZY07Index2',
        component: () => import('@/views/ZYBX/ZAZY/ZA07/Index2'),
        meta: {
            title: '众惠随心百万医疗', // 众惠随心百万普惠版-投保页-微信
        },
    },
    {
        path: '/ZYBX/ZAZY07/Upgrade',
        name: 'ZAZY07Upgrade',
        component: () => import('@/views/ZYBX/ZAZY/ZA07/Upgrade'),
        meta: {
            title: '众惠随心百万医疗', // 众惠随心百万
        },
    },
    {
        path: '/ZYBX/ZAZY07/Result',
        name: 'ZAZY07Result',
        component: () => import('@/views/ZYBX/ZAZY/ZA07/Result'),
        meta: {
            title: '众惠随心百万医疗', // 众惠随心百万
        },
    },
    {
        path: '/ZYBX/ZAZY07/Promotion',
        name: 'ZAZY07Promotion',
        component: () => import('@/views/ZYBX/ZAZY/ZA07/Promotion'),
        meta: {
            title: '众惠随心百万医疗', // 众惠随心百万-促活页
        },
    },
    {
        path: '/ZYBX/ZAZY07/Promotion1',
        name: 'ZAZY07Promotion1',
        component: () => import('@/views/ZYBX/ZAZY/ZA07/Promotion1'),
        meta: {
            title: '众惠随心百万医疗', // 众惠随心百万-促活页
        },
    },
    {
        path: '/ZYBX/YBZH01/Index1',
        name: 'YBZH01Index1',
        redirect: '/ZYBX/ZAZY07/Home/Index1',
    },
    {
        path: '/ZYBX/YBZH01/Index3',
        name: 'YBZH01Index3',
        redirect: '/ZYBX/ZAZY07/Home/Index3',
    },
    {
        path: '/ZYBX/YBZH01/Index2',
        name: 'YBZH01Index2',
        redirect: '/ZYBX/ZAZY07/Home/Index2',
    },
    {
        path: '/ZYBX/YBZA08/Index1',
        name: 'YBZA08Index1',
        redirect: '/ZYBX/ZAZY07/Home/Index1',
    },
    {
        path: '/ZYBX/YBZA08/Index2',
        name: 'YBZA08Index2',
        redirect: '/ZYBX/ZAZY07/Home/Index1',
    },
    {
        path: '/ZYBX/YBZA08/Index3',
        name: 'YBZA08Index3',
        redirect: '/ZYBX/ZAZY07/Home/Index1',
    },
    {
        path: '/ZYBX/YBZA08/Index4',
        name: 'YBZA08Index4',
        redirect: '/ZYBX/ZAZY07/Home/Index1',
    },
    {
        path: '/ZYBX/YBHN01/Home/Index1',
        name: 'YBHN01Index1',
        redirect: '/ZYBX/YBGR01/Index1',
    },
    {
        path: '/ZYBX/YBHN01/Home/Index2',
        name: 'YBHN01Index2',
        redirect: '/ZYBX/YBGR01/Index1',
    },
    {
        path: '/ZYBX/YBTPZX01/Index1',
        name: 'YBTPZX01Index1',
        redirect: '/ZYBX/YBHNZX03/Index1'
    },
    {
        path: '/ZYBX/YBGRZX01/Index1',
        name: 'YBGRZX01Index1',
        redirect: '/ZYBX/RXHGRZX01/Index11'
    },
    {
        path: '/ZYBX/YBGR01/PreUW/:id', // 百万医疗险普惠版2024-预核保
        component: () => import('@/views/ZYBX/ZAZY/YBGR01/Index'),
    },
    {
        path: '/ZYBX/YBGR01/Index1',
        name: 'YBGR01Index1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR01/Index1'),
        meta: {
            title: '优保关爱·百万医疗险普惠版', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR01/Index2',
        name: 'YBGR01Index2',
        component: () => import('@/views/ZYBX/ZAZY/YBGR01/Index2'),
        meta: {
            title: '优保关爱·百万医疗险普惠版', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR01/Index3',
        name: 'YBGR01Index3',
        component: () => import('@/views/ZYBX/ZAZY/YBGR01/Index3'),
        meta: {
            title: '优保关爱·百万医疗险普惠版', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR01/Upgrade',
        name: 'YBGR01Upgrade',
        component: () => import('@/views/ZYBX/ZAZY/YBGR01/Upgrade'),
        meta: {
            title: '优保关爱·百万医疗险普惠版', // 优保国任-升级页
        },
    },
    {
        path: '/ZYBX/YBGR01/Result',
        name: 'YBGR01Result',
        component: () => import('@/views/ZYBX/ZAZY/YBGR01/Result'),
        meta: {
            title: '优保关爱·百万医疗险普惠版', // 优保国任-结果页
        },
    },
    {
        path: '/ZYBX/YBGR01/Promotion',
        name: 'YBGR01Promotion',
        component: () => import('@/views/ZYBX/ZAZY/YBGR01/Promotion'),
        meta: {
            title: '优保关爱·百万医疗险普惠版', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/YBGR01/Promotion1',
        name: 'YBGR01Promotion1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR01/Promotion1'),
        meta: {
            title: '优保关爱·百万医疗险普惠版', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/YBGR02/PreUW/:id', // 优保国任-预核保
        component: () => import('@/views/ZYBX/ZAZY/YBGR02/Index'),
    },
    {
        path: '/ZYBX/YBGR02/Index1',
        name: 'YBGR02Index1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR02/Index1'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR02/Index2',
        name: 'YBGR02Index2',
        component: () => import('@/views/ZYBX/ZAZY/YBGR02/Index2'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR02/Index3',
        name: 'YBGR02Index3',
        component: () => import('@/views/ZYBX/ZAZY/YBGR02/Index3'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR02/Index4',
        name: 'YBGR02Index4',
        component: () => import('@/views/ZYBX/ZAZY/YBGR02/Index4'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR02/Upgrade',
        name: 'YBGR02Upgrade',
        component: () => import('@/views/ZYBX/ZAZY/YBGR02/Upgrade'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-升级页
        },
    },
    {
        path: '/ZYBX/YBGR02/Result',
        name: 'YBGR02Result',
        component: () => import('@/views/ZYBX/ZAZY/YBGR02/Result'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-结果页
        },
    },
    {
        path: '/ZYBX/YBGR02/Promotion',
        name: 'YBGR02Promotion',
        component: () => import('@/views/ZYBX/ZAZY/YBGR02/Promotion'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/YBGR02/Promotion1',
        name: 'YBGR02Promotion1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR02/Promotion1'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/YBGR03/PreUW/:id', // 百万医疗险普惠版2024-预核保
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Index'),
    },
    {
        path: '/ZYBX/YBGR03/Index1',
        name: 'YBGR03Index1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Index1'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR03/Index2',
        name: 'YBGR03Index2',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Index2'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR03/Index3',
        name: 'YBGR03Index3',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Index3'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR03/Index4',
        name: 'YBGR03Index4',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Index4'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR03/Index5',
        name: 'YBGR03Index5',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Index5'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR03/Index6',
        name: 'YBGR03Index6',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Index6'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR03/Index7',
        name: 'YBGR03Index7',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Index7'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR03/Upgrade',
        name: 'YBGR03Upgrade',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Upgrade'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-升级页
        },
    },
    {
        path: '/ZYBX/YBGR03/Result',
        name: 'YBGR03Result',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Result'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-结果页
        },
    },
    {
        path: '/ZYBX/YBGR03/Promotion',
        name: 'YBGR03Promotion',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Promotion'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/YBGR03/Promotion1',
        name: 'YBGR03Promotion1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR03/Promotion1'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/YBGR04/PreUW/:id', // 百万医疗险普惠版2024-预核保
        component: () => import('@/views/ZYBX/ZAZY/YBGR04/Index'),
    },
    {
        path: '/ZYBX/YBGR04/Index1',
        name: 'YBGR04Index1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR04/Index1'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR04/Index2',
        name: 'YBGR04Index2',
        component: () => import('@/views/ZYBX/ZAZY/YBGR04/Index2'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR04/Upgrade',
        name: 'YBGR04Upgrade',
        component: () => import('@/views/ZYBX/ZAZY/YBGR04/Upgrade'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-升级页
        },
    },
    {
        path: '/ZYBX/YBGR04/Result',
        name: 'YBGR04Result',
        component: () => import('@/views/ZYBX/ZAZY/YBGR04/Result'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-结果页
        },
    },
    {
        path: '/ZYBX/YBGR04/Promotion',
        name: 'YBGR04Promotion',
        component: () => import('@/views/ZYBX/ZAZY/YBGR04/Promotion'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/YBGR04/Promotion1',
        name: 'YBGR04Promotion1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR04/Promotion1'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/YBGR05/PreUW/:id', // 百万医疗险普惠版2024-预核保
        component: () => import('@/views/ZYBX/ZAZY/YBGR05/Index'),
    },
    {
        path: '/ZYBX/YBGR05/Index1',
        name: 'YBGR05Index1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR05/Index1'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-投保页
        },
    },
    {
        path: '/ZYBX/YBGR05/Index2',
        name: 'YBGR05Index2',
        redirect: '/ZYBX/YBGR05/Index1',
    },
    {
        path: '/ZYBX/YBGR05/Upgrade',
        name: 'YBGR05Upgrade',
        component: () => import('@/views/ZYBX/ZAZY/YBGR05/Upgrade'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-升级页
        },
    },
    {
        path: '/ZYBX/YBGR05/Result',
        name: 'YBGR05Result',
        component: () => import('@/views/ZYBX/ZAZY/YBGR05/Result'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-结果页
        },
    },
    {
        path: '/ZYBX/YBGR05/Promotion',
        name: 'YBGR05Promotion',
        component: () => import('@/views/ZYBX/ZAZY/YBGR05/Promotion'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/YBGR05/Promotion1',
        name: 'YBGR05Promotion1',
        component: () => import('@/views/ZYBX/ZAZY/YBGR05/Promotion1'),
        meta: {
            title: '优保关爱·重大疾病保险', // 优保国任-促活页
        },
    },
    {
        path: '/ZYBX/NWGRZX01/Index1',
        name: 'NWGRZX01Index1',
        component: () => import('@/views/ZYBX/NW/NWGRZX01/Index1'),
        meta: {
            title: '免费领百万保障', // 暖哇国任赠险 对比测试
        },
    },
    {
        path: '/ZYBX/NWGRZX01/Index2',
        name: 'NWGRZX01Index2',
        component: () => import('@/views/ZYBX/NW/NWGRZX01/Index2'),
        meta: {
            title: '免费领百万保障', // 暖哇国任赠险 对比测试
        },
    },
    {
        path: '/ZYBX/NWGRZX01/Index3',
        name: 'NWGRZX01Index3',
        redirect: '/ZYBX/NWGRZX01/Index1',
    },
    {
        path: '/ZYBX/YBZA01/PreUW/:id', // 优保众安家财险-预核保
        component: () => import('@/views/ZYBX/ZAZY/YBZA01/Index'),
    },
    {
        path: '/ZYBX/YBZA01/Index1',
        name: 'YBZA01Index1',
        component: () => import('@/views/ZYBX/ZAZY/YBZA01/Index1'),
        meta: {
            title: '全家安心保家庭综合保险', // 优保众安家财险-投保页
        },
    },
    {
        path: '/ZYBX/YBZA01/Upgrade',
        name: 'YBZA01Upgrade',
        component: () => import('@/views/ZYBX/ZAZY/YBZA01/Upgrade'),
        meta: {
            title: '全家安心保家庭综合保险', // 优保众安家财险-升级页
        },
    },
    {
        path: '/ZYBX/YBZA01/Result',
        name: 'YBZA01Result',
        component: () => import('@/views/ZYBX/ZAZY/YBZA01/Result'),
        meta: {
            title: '全家安心保家庭综合保险', // 优保众安家财险-结果页
        },
    },
    {
        path: '/ZYBX/YBZA01/Promotion',
        name: 'YBZA01Promotion',
        component: () => import('@/views/ZYBX/ZAZY/YBZA01/Promotion'),
        meta: {
            title: '全家安心保家庭综合保险', // 优保众安家财险-促活页
        },
    },
    {
        path: '/ZYBX/YBZA01/Promotion1',
        name: 'YBZA01Promotion1',
        component: () => import('@/views/ZYBX/ZAZY/YBZA01/Promotion1'),
        meta: {
            title: '全家安心保家庭综合保险', // 优保众安家财险-促活页
        },
    },
]
