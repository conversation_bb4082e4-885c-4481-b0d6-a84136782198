export default [
    {
        path: '/ZYBX/IYBQW01/Index1',
        name: 'IYBQW01Index1',
        redirect: '/ZYBX/IYBQW01/Index2',
    },
    {
        path: '/ZYBX/IYBQW01/Index2',
        name: 'IYBQW01Index2',
        component: () => import('@/views/ZYBX/IYB/IYBQW/IYBQW01/Index2'),
        meta: {
            title: '免费领取保障', // 企微中间页
        },
    },
    {
        path: '/ZYBX/IYBQW01/Index3',
        name: 'IYBQW01Index3',
        component: () => import('@/views/ZYBX/IYB/IYBQW/IYBQW01/Index3'),
        meta: {
            title: '免费领取保障', // 企微中间页
        },
    },
    {
        path: '/ZYBX/IYBTPY01/Index1', //
        name: 'IYBTPY01Index1',
        redirect: '/ZYBX/IYBTK08/Index1',
    },
    {
        path: '/ZYBX/IYBTPY01/Index2', //
        name: 'IYBTPY01Index2',
        redirect: '/ZYBX/IYBTK08/Index1',
    },
    {
        path: '/ZYBX/IYBTPY01/Index3', //
        name: 'IYBTPY01Index3',
        redirect: '/ZYBX/IYBTK08/Index1',
    },
    {
        path: '/ZYBX/IYBTPY01/Index4', //
        name: 'IYBTPY01Index4',
        redirect: '/ZYBX/IYBTK08/Index1',
    },
    {
        path: '/ZYBX/IYBTPY01/Index5', //
        name: 'IYBTPY01Index5',
        redirect: '/ZYBX/IYBTK08/Index1',
    },
    {
        path: '/ZYBX/IYBGRZX01/Index',
        name: 'IYBGRZX01Index',
        component: () => import('@/views/ZYBX/IYB/IYBGRZX01/Index'),
        meta: {
            title: '免费领取保障', // 国任赠险
        },
    },
    {
        path: '/ZYBX/IYBGRZX01/Index10',
        name: 'IYBGRZX01Index10',
        redirect: '/ZYBX/IYBGRZX01/Index13',
    },
    {
        path: '/ZYBX/IYBGRZX01/Index11',
        name: 'IYBGRZX01Index11',
        redirect: '/ZYBX/IYBGRZX01/Index13',
    },
    {
        path: '/ZYBX/IYBGRZX01/Index12',
        name: 'IYBGRZX01Index12',
        redirect: '/ZYBX/IYBGRZX01/Index13',
    },
    {
        path: '/ZYBX/IYBGRZX01/Index13',
        name: 'IYBGRZX01Index13',
        component: () => import('@/views/ZYBX/IYB/IYBGRZX01/Index13'),
        meta: {
            title: '免费领取保障', // 国任赠险
        },
    },
    {
        path: '/ZYBX/IYBGR01/Index1', // 支付宝
        name: 'IYBGR01Index1',
        redirect: '/ZYBX/IYBGR03/Index1',
    },
    {
        path: '/ZYBX/IYBGR01/Index2', // 微信
        name: 'IYBGR01Index2',
        redirect: '/ZYBX/IYBGR03/Index1',
    },
    {
        path: '/ZYBX/IYBGR02/Index1', // 支付宝
        name: 'IYBGR02Index1',
        redirect: '/ZYBX/IYBGR04/Index1',
    },
    {
        path: '/ZYBX/IYBGR02/Index2', // 加保
        name: 'IYBGR02Index2',
        redirect: '/ZYBX/IYBGR04/Index2',
    },
    {
        path: '/ZYBX/IYBGR02/Index3', // 支付宝
        name: 'IYBGR02Index3',
        redirect: '/ZYBX/IYBGR04/Index1',
    },
    {
        path: '/ZYBX/IYBGR03/PreUW/:id', // 预核保
        component: () => import('@/views/ZYBX/IYB/IYBGR03/Index'),
    },
    {
        path: '/ZYBX/IYBGR03/Index1', // 支付宝
        name: 'IYBGR03Index1',
        component: () => import('@/views/ZYBX/IYB/IYBGR03/Index1'),
        meta: {
            title: '国任优选百万医疗险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBGR03/Index2', // 微信
        name: 'IYBGR03Index2',
        component: () => import('@/views/ZYBX/IYB/IYBGR03/Index2'),
        meta: {
            title: '国任优选百万医疗险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBGR03/Index3', // 微信
        name: 'IYBGR03Index3',
        component: () => import('@/views/ZYBX/IYB/IYBGR03/Index3'),
        meta: {
            title: '国任优选百万医疗险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBGR03/Index4', // 微信
        name: 'IYBGR03Index4',
        component: () => import('@/views/ZYBX/IYB/IYBGR03/Index4'),
        meta: {
            title: '国任优选百万医疗险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBGR03/Upgrade',
        name: 'IYBGR03Upgrade',
        component: () => import('@/views/ZYBX/IYB/IYBGR03/Upgrade'),
        meta: {
            title: '国任优选百万医疗险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBGR03/Promotion',
        name: 'IYBGR03Promotion',
        component: () => import('@/views/ZYBX/IYB/IYBGR03/Promotion'),
        meta: {
            title: '国任优选百万医疗险', // 待升级促活
        },
    },
    {
        path: '/ZYBX/IYBGR03/Promotion1',
        name: 'IYBGR03Promotion1',
        component: () => import('@/views/ZYBX/IYB/IYBGR03/Promotion1'),
        meta: {
            title: '国任优选百万医疗险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBGR03/Result',
        name: 'IYBGR03Result',
        component: () => import('@/views/ZYBX/IYB/IYBGR03/Result'),
        meta: {
            title: '国任优选百万医疗险', // 结果页
        },
    },
    {
        path: '/ZYBX/IYBGR04/PreUW/:id', // 预核保
        component: () => import('@/views/ZYBX/IYB/IYBGR04/Index'),
    },
    {
        path: '/ZYBX/IYBGR04/Index1', // 支付宝
        name: 'IYBGR04Index1',
        component: () => import('@/views/ZYBX/IYB/IYBGR04/Index1'),
        meta: {
            title: '国任优选特定疾病保险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBGR04/Index2', // 加保
        name: 'IYBGR04Index2',
        component: () => import('@/views/ZYBX/IYB/IYBGR04/Index2'),
        meta: {
            title: '国任优选特定疾病保险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBGR04/Index3', // 加保
        name: 'IYBGR04Index3',
        component: () => import('@/views/ZYBX/IYB/IYBGR04/Index3'),
        meta: {
            title: '国任优选特定疾病保险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBGR04/Index4', // 加保
        name: 'IYBGR04Index4',
        component: () => import('@/views/ZYBX/IYB/IYBGR04/Index4'),
        meta: {
            title: '国任优选特定疾病保险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBGR04/Upgrade',
        name: 'IYBGR04Upgrade',
        component: () => import('@/views/ZYBX/IYB/IYBGR04/Upgrade'),
        meta: {
            title: '国任优选特定疾病保险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBGR04/Promotion',
        name: 'IYBGR04Promotion',
        component: () => import('@/views/ZYBX/IYB/IYBGR04/Promotion'),
        meta: {
            title: '国任优选特定疾病保险', // 待升级促活
        },
    },
    {
        path: '/ZYBX/IYBGR04/Promotion1',
        name: 'IYBGR04Promotion1',
        component: () => import('@/views/ZYBX/IYB/IYBGR04/Promotion1'),
        meta: {
            title: '国任优选特定疾病保险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBGR04/Result',
        name: 'IYBGR04Result',
        component: () => import('@/views/ZYBX/IYB/IYBGR04/Result'),
        meta: {
            title: '国任优选特定疾病保险', // 结果页
        },
    },
    {
        path: '/ZYBX/IYBTPZX01/Index',
        name: 'IYBTPZX01Index',
        redirect: '/ZYBX/TKZX02/Index17',
    },
    {
        path: '/ZYBX/IYBTPZX01/Index1',
        name: 'IYBTPZX01Index1',
        redirect: '/ZYBX/TKZX02/Index17',
    },
    {
        path: '/ZYBX/IYBTK01/Index1', // 支付宝
        name: 'IYBTK01Index1',
        redirect: '/ZYBX/IYBTK07/Index1',
    },
    {
        path: '/ZYBX/IYBTK01/Index2', // 加保
        name: 'IYBTK01Index2',
        redirect: '/ZYBX/IYBTK07/Index2',
    },
    {
        path: '/ZYBX/IYBTK01/Index3', // 微信
        name: 'IYBTK01Index3',
        redirect: '/ZYBX/IYBTK07/Index3',
    },
    {
        path: '/ZYBX/IYBTK02/Index1', // 支付宝
        name: 'IYBTK02Index1',
        redirect:'/ZYBX/IYBTK04/Index1',
    },
    {
        path: '/ZYBX/IYBTK02/Index2', // 微信
        name: 'IYBTK02Index2',
        redirect:'/ZYBX/IYBTK04/Index2',
    },
    {
        path: '/ZYBX/IYBTK02/Index3', // 支付宝-语音播报
        name: 'IYBTK02Index3',
        redirect:'/ZYBX/IYBTK04/Index3',
    },
    {
        path: '/ZYBX/IYBTK05/Index1', // 支付宝
        name: 'IYBTK05Index1',
        redirect:'/ZYBX/IYBTK04/Index1',
    },
    {
        path: '/ZYBX/IYBTK05/Index2', // 微信
        name: 'IYBTK05Index2',
        redirect:'/ZYBX/IYBTK04/Index2',
    },
    {
        path: '/ZYBX/IYBTK05/Index3', // 支付宝-语音播报
        name: 'IYBTK05Index3',
        redirect:'/ZYBX/IYBTK04/Index3',
    },
    {
        path: '/ZYBX/IYBTK03/Index1', // 支付宝
        name: 'IYBTK03Index1',
        redirect:'/ZYBX/IYBTK04/Index1',
    },
    {
        path: '/ZYBX/IYBTK03/Index2', // 微信
        name: 'IYBTK03Index2',
        redirect:'/ZYBX/IYBTK04/Index2',
    },
    {
        path: '/ZYBX/IYBTK03/Index3', // 支付宝-语音播报
        name: 'IYBTK03Index3',
        redirect:'/ZYBX/IYBTK04/Index3',
    },
    {
        path: '/ZYBX/IYBTK04/Index1',
        name: 'IYBTK04Index1',
        redirect: '/ZYBX/IYBTK09/Index1',
    },
    {
        path: '/ZYBX/IYBTK04/Index2',
        name: 'IYBTK04Index2',
        redirect: '/ZYBX/IYBTK09/Index1',
    },
    {
        path: '/ZYBX/IYBTK04/Index3',
        name: 'IYBTK04Index3',
        redirect: '/ZYBX/IYBTK09/Index1',
    },
    {
        path: '/ZYBX/IYBTK06/PreUW/:id', // 预核保
        component: () => import('@/views/ZYBX/IYB/IYBTK06/Index'),
    },
    {
        path: '/ZYBX/IYBTK06/Index1', // 支付宝
        name: 'IYBTK06Index1',
        component: () => import('@/views/ZYBX/IYB/IYBTK06/Index1'),
        meta: {
            title: '泰康2024防癌医疗险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBTK06/Upgrade',
        name: 'IYBTK06Upgrade',
        component: () => import('@/views/ZYBX/IYB/IYBTK06/Upgrade'),
        meta: {
            title: '泰康2024防癌医疗险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBTK06/Promotion',
        name: 'IYBTK06Promotion',
        component: () => import('@/views/ZYBX/IYB/IYBTK06/Promotion'),
        meta: {
            title: '泰康2024防癌医疗险', // 待升级促活
        },
    },
    {
        path: '/ZYBX/IYBTK06/Promotion1',
        name: 'IYBTK06Promotion1',
        component: () => import('@/views/ZYBX/IYB/IYBTK06/Promotion1'),
        meta: {
            title: '泰康2024防癌医疗险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBTK06/Result',
        name: 'IYBTK06Result',
        component: () => import('@/views/ZYBX/IYB/IYBTK06/Result'),
        meta: {
            title: '泰康2024防癌医疗险', // 结果页
        },
    },
    {
        path: '/ZYBX/IYBTK07/Index1', // 支付宝
        name: 'IYBTK07Index1',
        redirect: '/ZYBX/IYBTK10/Index1',
    },
    {
        path: '/ZYBX/IYBTK07/Index2', // 加保
        name: 'IYBTK07Index2',
        redirect: '/ZYBX/IYBTK10/Index2',
    },
    {
        path: '/ZYBX/IYBTK07/Index3', // 微信
        name: 'IYBTK07Index3',
        redirect: '/ZYBX/IYBTK10/Index1',
    },
    {
        path: '/ZYBX/IYBTK08/Index1',
        name: 'IYBTK08Index1',
        component: () => import('@/views/ZYBX/IYB/IYBTK08/Index1'),
        meta: {
            title: '泰康2024百万医疗险',
        },
    },
    {
        path: '/ZYBX/IYBTK08/IndexTest', // 用于测试iframe嵌入页
        name: 'IYBTK08IndexTest',
        component: () => import('@/views/ZYBX/IYB/IYBTK08/IndexTest'),
        meta: {
            title: '泰康2024百万医疗险',
        },
    },
    {
        path: '/ZYBX/IYBTK08/Index2',
        name: 'IYBTK08Index2',
        component: () => import('@/views/ZYBX/IYB/IYBTK08/Index2'),
        meta: {
            title: '泰康2024百万医疗险',
        },
    },
    {
        path: '/ZYBX/IYBTK08/Index3',
        name: 'IYBTK08Index3',
        component: () => import('@/views/ZYBX/IYB/IYBTK08/Index3'),
        meta: {
            title: '泰康2024百万医疗险',
        },
    },
    {
        path: '/ZYBX/IYBTK08/Upgrade',
        name: 'IYBTK08Upgrade',
        component: () => import('@/views/ZYBX/IYB/IYBTK08/Upgrade'),
        meta: {
            title: '泰康2024百万医疗险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBTK08/Promotion',
        name: 'IYBTK08Promotion',
        component: () => import('@/views/ZYBX/IYB/IYBTK08/Promotion'),
        meta: {
            title: '泰康2024百万医疗险', // 待升级促活
        },
    },
    {
        path: '/ZYBX/IYBTK08/Promotion1',
        name: 'IYBTK08Promotion1',
        component: () => import('@/views/ZYBX/IYB/IYBTK08/Promotion1'),
        meta: {
            title: '泰康2024百万医疗险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBTK08/Result',
        name: 'IYBTK08Result',
        component: () => import('@/views/ZYBX/IYB/IYBTK08/Result'),
        meta: {
            title: '泰康2024百万医疗险', // 结果页
        },
    },
    {
        path: '/ZYBX/IYBTK09/PreUW/:id', // 预核保
        component: () => import('@/views/ZYBX/IYB/IYBTK09/Index'),
    },
    {
        path: '/ZYBX/IYBTK09/Index1',
        name: 'IYBTK09Index1',
        component: () => import('@/views/ZYBX/IYB/IYBTK09/Index1'),
        meta: {
            title: '泰康2024百万医疗险',
        },
    },
    {
        path: '/ZYBX/IYBTK09/Upgrade',
        name: 'IYBTK09Upgrade',
        component: () => import('@/views/ZYBX/IYB/IYBTK09/Upgrade'),
        meta: {
            title: '泰康2024百万医疗险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBTK09/Promotion',
        name: 'IYBTK09Promotion',
        component: () => import('@/views/ZYBX/IYB/IYBTK09/Promotion'),
        meta: {
            title: '泰康2024百万医疗险', // 待升级促活
        },
    },
    {
        path: '/ZYBX/IYBTK09/Promotion1',
        name: 'IYBTK09Promotion1',
        component: () => import('@/views/ZYBX/IYB/IYBTK09/Promotion1'),
        meta: {
            title: '泰康2024百万医疗险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBTK09/Result',
        name: 'IYBTK09Result',
        component: () => import('@/views/ZYBX/IYB/IYBTK09/Result'),
        meta: {
            title: '泰康2024百万医疗险', // 结果页
        },
    },
    {
        path: '/ZYBX/IYBTK10/PreUW/:id', // 预核保
        component: () => import('@/views/ZYBX/IYB/IYBTK10/Index'),
    },
    {
        path: '/ZYBX/IYBTK10/Index1', // 京东支付
        name: 'IYBTK10Index1',
        component: () => import('@/views/ZYBX/IYB/IYBTK10/Index1'),
        meta: {
            title: '泰康2024重疾险', // 投保页 - 泰康个人重疾
        },
    },
    {
        path: '/ZYBX/IYBTK10/Index2', // 京东支付-加保
        name: 'IYBTK10Index2',
        component: () => import('@/views/ZYBX/IYB/IYBTK10/Index2'),
        meta: {
            title: '泰康2024重疾险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBTK10/Index3', // 京东支付-加保
        name: 'IYBTK10Index3',
        component: () => import('@/views/ZYBX/IYB/IYBTK10/Index3'),
        meta: {
            title: '泰康2024重疾险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBTK10/Index4', // 京东支付-加保
        name: 'IYBTK10Index4',
        component: () => import('@/views/ZYBX/IYB/IYBTK10/Index4'),
        meta: {
            title: '泰康2024重疾险', // 投保页
        },
    },
    {
        path: '/ZYBX/IYBTK10/Upgrade',
        name: 'IYBTK10Upgrade',
        component: () => import('@/views/ZYBX/IYB/IYBTK10/Upgrade'),
        meta: {
            title: '泰康2024重疾险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBTK10/Promotion',
        name: 'IYBTK10Promotion',
        component: () => import('@/views/ZYBX/IYB/IYBTK10/Promotion'),
        meta: {
            title: '泰康2024重疾险', // 待升级促活
        },
    },
    {
        path: '/ZYBX/IYBTK10/Promotion1',
        name: 'IYBTK10Promotion1',
        component: () => import('@/views/ZYBX/IYB/IYBTK10/Promotion1'),
        meta: {
            title: '泰康2024重疾险', // 待支付促活
        },
    },
    {
        path: '/ZYBX/IYBTK10/Result',
        name: 'IYBTK10Result',
        component: () => import('@/views/ZYBX/IYB/IYBTK10/Result'),
        meta: {
            title: '泰康2024重疾险', // 结果页
        },
    },
    {
        path: '/ZYBX/IYBZAZX01/Index1',
        name: 'IYBZAZX01Index1', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index2',
        name: 'IYBZAZX01Index2', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index3',
        name: 'IYBZAZX01Index3', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index4',
        name: 'IYBZAZX01Index4', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index5',
        name: 'IYBZAZX01Index5', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index6',
        name: 'IYBZAZX01Index6', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index7',
        name: 'IYBZAZX01Index7', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index8',
        name: 'IYBZAZX01Index8', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index9',
        name: 'IYBZAZX01Index9', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index10',
        name: 'IYBZAZX01Index10', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index11',
        name: 'IYBZAZX01Index11', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index12',
        name: 'IYBZAZX01Index12', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index13',
        name: 'IYBZAZX01Index13', // I云保众安赠险-周周领-跳众安魔方
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index14',
        name: 'IYBZAZX01Index14', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index15',
        name: 'IYBZAZX01Index15', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index16',
        name: 'IYBZAZX01Index16', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index17',
        name: 'IYBZAZX01Index17', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index18',
        name: 'IYBZAZX01Index18', // I云保太平和众安赠险聚合页面
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index19',
        name: 'IYBZAZX01Index19', // I云保太平和众安周周领赠险聚合页面
        redirect: '/ZYBX/IYBPAZX03/Index8'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index20',
        name: 'IYBZAZX01Index20',
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index21',
        name: 'IYBZAZX01Index21',
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index22',
        name: 'IYBZAZX01Index22',
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    {
        path: '/ZYBX/IYBZAZX01/Index23',
        name: 'IYBZAZX01Index23',
        redirect: '/ZYBX/IYBZAZX01/Index19'
    },
    //泰康相关路由
    {
        path: '/ZYBX/TK/Promotion',
        name: 'TKPromotion',
        component: () => import('@/views/ZYBX/TaiKang/Common/Promotion'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 促活中间页
        },
    },
    {
        path: '/ZYBX/IYBTK14/Home/Index1', // 泰康新重疾魔方-蚂蚁链
        name: 'TK14Index1',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/IYBTK14/Home/Index2', // 微信-信息流
        name: 'TK14Index2',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/IYBTK14/Home/Index3', // 支付宝-信息流
        name: 'TK14Index3',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/IYBTK14/PayAgain',
        name: 'TK14PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK14/PayAgain'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/IYBTK14/Upgrade',
        name: 'TK14Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK14/Upgrade'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 正常版
        },
    },
    {
        path: '/ZYBX/IYBTK14/Result',
        name: 'TK14Result',
        component: () => import('@/views/ZYBX/TaiKang/TK14/Result'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 正常版
        },
    },
    {
        path: '/ZYBX/IYBTK14/Promotion',
        name: 'TK14Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK14/Promotion'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/IYBTK14/Promotion1',
        name: 'TK14Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK14/Promotion1'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/IYBTK14/Promotion2',
        name: 'TK14Promotion2',
        component: () => import('@/views/ZYBX/TaiKang/TK14/Promotion2'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 重签约促活页
        },
    },
    {
        path: '/ZYBX/IYBTK14/Promotion3',
        name: 'TK14Promotion3',
        component: () => import('@/views/ZYBX/TaiKang/TK14/Promotion3'),
        meta: {
            title: '', // 泰医享·全民医疗险 续费促活中间页
        },
    },
    {
        path: '/ZYBX/IYBTK14/Promotion6',
        name: 'TK14Promotion6',
        component: () => import('@/views/ZYBX/TaiKang/TK14/Promotion6'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 续费促活页
        },
    },
    {
        path: '/ZYBX/TK16/Index1', // 泰康新重疾魔方-蚂蚁链
        name: 'TK16Index1',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK16/Index2', //
        name: 'TK16Index2',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK16/Index3', //
        name: 'TK16Index3',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK16/PayAgain',
        name: 'TK16PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK16/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK16/Upgrade',
        name: 'TK16Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK16/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK16/Result',
        name: 'TK16Result',
        component: () => import('@/views/ZYBX/TaiKang/TK16/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK16/Promotion',
        name: 'TK16Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK16/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级中间页
        },
    },
    {
        path: '/ZYBX/TK16/Promotion1',
        name: 'TK16Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK16/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK16/Promotion2',
        name: 'TK16Promotion2',
        component: () => import('@/views/ZYBX/TaiKang/TK16/Promotion2'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK16/Promotion3',
        name: 'TK16Promotion3',
        component: () => import('@/views/ZYBX/TaiKang/TK16/Promotion3'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK16/Promotion4',
        name: 'TK16Promotion4',
        component: () => import('@/views/ZYBX/TaiKang/TK16/Promotion4'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK16/Promotion5',
        name: 'TK16Promotion5',
        component: () => import('@/views/ZYBX/TaiKang/TK16/Promotion5'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK16/Promotion6',
        name: 'TK16Promotion6',
        component: () => import('@/views/ZYBX/TaiKang/TK16/Promotion6'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK19/Index1', // 泰康新重疾魔方
        name: 'TK19Index1',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK19/Index2', //
        name: 'TK19Index2',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK19/Index3', //
        name: 'TK19Index3',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK19/PayAgain',
        name: 'TK19PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK19/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK19/Upgrade',
        name: 'TK19Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK19/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 升级页
        },
    },
    {
        path: '/ZYBX/TK19/Result',
        name: 'TK19Result',
        component: () => import('@/views/ZYBX/TaiKang/TK19/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 结果页
        },
    },
    {
        path: '/ZYBX/TK19/Promotion',
        name: 'TK19Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK19/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK19/Promotion1',
        name: 'TK19Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK19/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK19/Promotion6',
        name: 'TK19Promotion6',
        component: () => import('@/views/ZYBX/TaiKang/TK19/Promotion6'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 续费促活页
        },
    },
    {
        path: '/ZYBX/TK22/Index1', // 泰康新重疾魔方
        name: 'TK22Index1',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK22/Index2', //
        name: 'TK22Index2',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK22/Index3', //
        name: 'TK22Index3',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK22/PayAgain',
        name: 'TK22PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK22/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK22/Upgrade',
        name: 'TK22Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK22/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 升级页
        },
    },
    {
        path: '/ZYBX/TK22/Result',
        name: 'TK22Result',
        component: () => import('@/views/ZYBX/TaiKang/TK22/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 结果页
        },
    },
    {
        path: '/ZYBX/TK22/Promotion',
        name: 'TK22Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK22/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK22/Promotion1',
        name: 'TK22Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK22/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK22/Promotion6',
        name: 'TK22Promotion6',
        component: () => import('@/views/ZYBX/TaiKang/TK22/Promotion6'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 续费促活页
        },
    },
    {
        path: '/ZYBX/TK25/Index1', // 泰康新重疾魔方-骏伯参数
        name: 'TK25Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK25/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK25/PayAgain',
        name: 'TK25PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK25/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK25/Upgrade',
        name: 'TK25Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK25/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK25/Result',
        name: 'TK25Result',
        component: () => import('@/views/ZYBX/TaiKang/TK25/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK25/Promotion',
        name: 'TK25Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK25/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK25/Promotion1',
        name: 'TK25Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK25/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK26/Index1', // 泰康新重疾魔方-骏伯参数
        name: 'TK26Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK26/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK26/PayAgain',
        name: 'TK26PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK26/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK26/Upgrade',
        name: 'TK26Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK26/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK26/Result',
        name: 'TK26Result',
        component: () => import('@/views/ZYBX/TaiKang/TK26/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK26/Promotion',
        name: 'TK26Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK26/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK26/Promotion1',
        name: 'TK26Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK26/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK27/Index1', // 泰康新重疾魔方-骏伯参数
        name: 'TK27Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK27/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK27/PayAgain',
        name: 'TK27PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK27/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK27/Upgrade',
        name: 'TK27Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK27/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK27/Result',
        name: 'TK27Result',
        component: () => import('@/views/ZYBX/TaiKang/TK27/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK27/Promotion',
        name: 'TK27Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK27/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK27/Promotion1',
        name: 'TK27Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK27/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK28/Index1', // 泰康新重疾魔方-骏伯参数
        name: 'TK28Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK28/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK28/PayAgain',
        name: 'TK28PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK28/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK28/Upgrade',
        name: 'TK28Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK28/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK28/Result',
        name: 'TK28Result',
        component: () => import('@/views/ZYBX/TaiKang/TK28/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK28/Promotion',
        name: 'TK28Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK28/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK28/Promotion1',
        name: 'TK28Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK28/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK29/Index1', // 泰康新重疾魔方-蚂蚁链
        name: 'TK29Index1',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK29/Index2', //
        name: 'TK29Index2',
        redirect: '/ZYBX/TK36/Index2',
    },
    {
        path: '/ZYBX/TK29/Index3', //
        name: 'TK29Index3',
        redirect: '/ZYBX/TK29/Index1',
    },
    {
        path: '/ZYBX/TK29/PayAgain',
        name: 'TK29PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK29/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK29/Upgrade',
        name: 'TK29Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK29/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK29/Result',
        name: 'TK29Result',
        component: () => import('@/views/ZYBX/TaiKang/TK29/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK29/Promotion',
        name: 'TK29Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK29/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级中间页
        },
    },
    {
        path: '/ZYBX/TK29/Promotion1',
        name: 'TK29Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK29/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK29/Promotion6',
        name: 'TK29Promotion6',
        component: () => import('@/views/ZYBX/TaiKang/TK29/Promotion6'),
        meta: {
            title: '泰超能·百万医疗险', // 泰医享·全民医疗险 续费促活页
        },
    },
    {
        path: '/ZYBX/TK30/Index1', // 泰康新重疾魔方-骏伯参数
        name: 'TK30Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK30/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK30/PayAgain',
        name: 'TK30PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK30/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK30/Upgrade',
        name: 'TK30Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK30/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK30/Result',
        name: 'TK30Result',
        component: () => import('@/views/ZYBX/TaiKang/TK30/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK30/Promotion',
        name: 'TK30Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK30/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK30/Promotion1',
        name: 'TK30Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK30/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK31/Index1', // 泰康新重疾魔方-骏伯参数
        name: 'TK31Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK31/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK31/PayAgain',
        name: 'TK31PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK31/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK31/Upgrade',
        name: 'TK31Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK31/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK31/Result',
        name: 'TK31Result',
        component: () => import('@/views/ZYBX/TaiKang/TK31/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK31/Promotion',
        name: 'TK31Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK31/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK31/Promotion1',
        name: 'TK31Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK31/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK32/Index1', // 泰康新重疾魔方-骏伯参数
        name: 'TK32Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK32/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK32/PayAgain',
        name: 'TK32PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK32/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK32/Upgrade',
        name: 'TK32Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK32/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK32/Result',
        name: 'TK32Result',
        component: () => import('@/views/ZYBX/TaiKang/TK32/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK32/Promotion',
        name: 'TK32Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK32/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK32/Promotion1',
        name: 'TK32Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK32/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK33/Index1', // 泰康新重疾魔方-骏伯参数
        name: 'TK33Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK33/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK33/PayAgain',
        name: 'TK33PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK33/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK33/Upgrade',
        name: 'TK33Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK33/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK33/Result',
        name: 'TK33Result',
        component: () => import('@/views/ZYBX/TaiKang/TK33/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK33/Promotion',
        name: 'TK33Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK33/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK33/Promotion1',
        name: 'TK33Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK33/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK34/Index1', // 泰康新重疾
        name: 'TK34Index1',
        redirect: '/ZYBX/TK36/Index1',
    },
    {
        path: '/ZYBX/TK34/PayAgain',
        name: 'TK34PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK34/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK34/Result',
        name: 'TK34Result',
        component: () => import('@/views/ZYBX/TaiKang/TK34/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK34/Promotion1',
        name: 'TK34Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK34/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 待支付
        },
    },
    {
        path: '/ZYBX/TK36/PreUW/:id', // 泰康新重疾魔方-预核保
        component: () => import('@/views/ZYBX/TaiKang/TK36/Index'),
    },
    {
        path: '/ZYBX/TK36/Index1', // 泰康新重疾魔方-蚂蚁链
        name: 'TK36Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK36/Index2', //
        name: 'TK36Index2',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Index2'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK36/Index3',
        name: 'TK36Index3',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Index3'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK36/Index4',
        name: 'TK36Index4',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Index4'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK36/Index5',
        name: 'TK36Index5',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Index5'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK36/Index6',
        name: 'TK36Index6',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Index6'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK36/Index7',
        name: 'TK36Index7',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Index7'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK36/PayAgain',
        name: 'TK36PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK36/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK36/Upgrade',
        name: 'TK36Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK36/Result',
        name: 'TK36Result',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK36/Promotion',
        name: 'TK36Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级中间页
        },
    },
    {
        path: '/ZYBX/TK36/Promotion1',
        name: 'TK36Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK36/Promotion2',
        name: 'TK36Promotion2',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion2'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK36/Promotion3',
        name: 'TK36Promotion3',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion3'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK36/Promotion4',
        name: 'TK36Promotion4',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion4'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK36/Promotion5',
        name: 'TK36Promotion5',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion5'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK36/Promotion6',
        name: 'TK36Promotion6',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion6'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 续费促活页
        },
    },
    {
        path: '/ZYBX/TK36/Promotion7',
        name: 'TK36Promotion7',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion7'),
        meta: {
            title: '泰医享·全民医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK36/Promotion8',
        name: 'TK36Promotion8',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion8'),
        meta: {
            title: '泰医享·全民医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK36/Promotion9',
        name: 'TK36Promotion9',
        component: () => import('@/views/ZYBX/TaiKang/TK36/Promotion9'),
        meta: {
            title: '泰医享·全民医疗险', // 泰超能·百万医疗险 促活
        },
    },
    {
        path: '/ZYBX/TK37/PreUW/:id', // 泰康新重疾魔方-预核保
        component: () => import('@/views/ZYBX/TaiKang/TK37/Index'),
    },
    {
        path: '/ZYBX/TK37/Index1', // 泰康新重疾魔方-蚂蚁链
        name: 'TK37Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK37/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK37/Index2', //
        name: 'TK37Index2',
        redirect: '/ZYBX/TK37/Index1',
    },
    {
        path: '/ZYBX/TK37/Index3', //
        name: 'TK37Index3',
        redirect: '/ZYBX/TK37/Index1',
    },
    {
        path: '/ZYBX/TK37/PayAgain',
        name: 'TK37PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK37/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK37/Upgrade',
        name: 'TK37Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK37/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK37/Result',
        name: 'TK37Result',
        component: () => import('@/views/ZYBX/TaiKang/TK37/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK37/Promotion',
        name: 'TK37Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK37/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级中间页
        },
    },
    {
        path: '/ZYBX/TK37/Promotion1',
        name: 'TK37Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK37/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    { 
        path: '/ZYBX/TK37/Promotion6',
        name: 'TK37Promotion6',
        component: () => import('@/views/ZYBX/TaiKang/TK37/Promotion6'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 续费促活页
        },
    },
    {
        path: '/ZYBX/TK38/PreUW/:id', // 泰康新重疾魔方-预核保
        component: () => import('@/views/ZYBX/TaiKang/TK38/Index'),
    },
    {
        path: '/ZYBX/TK38/Index1', // 泰康新重疾魔方-蚂蚁链
        name: 'TK38Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK38/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK38/Index2', //
        name: 'TK38Index2',
        component: () => import('@/views/ZYBX/TaiKang/TK38/Index2'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK38/Index3', //
        name: 'TK38Index3',
        component: () => import('@/views/ZYBX/TaiKang/TK38/Index3'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK38/Index4', //
        name: 'TK38Index4',
        component: () => import('@/views/ZYBX/TaiKang/TK38/Index4'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK38/PayAgain',
        name: 'TK38PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK38/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK38/Upgrade',
        name: 'TK38Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK38/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK38/Result',
        name: 'TK38Result',
        component: () => import('@/views/ZYBX/TaiKang/TK38/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK38/Promotion',
        name: 'TK38Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK38/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级中间页
        },
    },
    {
        path: '/ZYBX/TK38/Promotion1',
        name: 'TK38Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK38/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    { 
        path: '/ZYBX/TK38/Promotion6',
        name: 'TK38Promotion6',
        component: () => import('@/views/ZYBX/TaiKang/TK38/Promotion6'),
        meta: {
            title: '泰医享·全民医疗险', // 泰医享·全民医疗险 续费促活页
        },
    },
    {
        path: '/ZYBX/TK39/PreUW/:id', // 泰康新重疾魔方-预核保
        component: () => import('@/views/ZYBX/TaiKang/TK39/Index'),
    },
    {
        path: '/ZYBX/TK39/Index1', // 泰康新重疾魔方-蚂蚁链
        name: 'TK39Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK39/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK39/Index2', //
        name: 'TK39Index2',
        component: () => import('@/views/ZYBX/TaiKang/TK39/Index2'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK39/Index3', //
        name: 'TK39Index3',
        redirect: '/ZYBX/TK39/Index1',
    },
    {
        path: '/ZYBX/TK39/PayAgain',
        name: 'TK39PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK39/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK39/Upgrade',
        name: 'TK39Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK39/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK39/Result',
        name: 'TK39Result',
        component: () => import('@/views/ZYBX/TaiKang/TK39/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK39/Promotion',
        name: 'TK39Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK39/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级中间页
        },
    },
    {
        path: '/ZYBX/TK39/Promotion1',
        name: 'TK39Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK39/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TKZX01/Index1',
        name: 'TKZX01Index1',
        redirect: '/ZYBX/TKZX02/Index6',
    },
    {
        path: '/ZYBX/TKZX01/Index2',
        name: 'TKZX01Index2',
        redirect: '/ZYBX/TKZX02/Index6',
    },
    {
        path: '/ZYBX/TKZX01/Index3',
        name: 'TKZX01Index3',
        redirect: '/ZYBX/TKZX02/Index6',
    },
    {
        path: '/ZYBX/TKZX01/Index4',
        name: 'TKZX01Index4',
        redirect: '/ZYBX/TKZX02/Index6',
    },
    {
        path: '/ZYBX/TKZX01/Index5',
        name: 'TKZX01Index5',
        redirect: '/ZYBX/TKZX02/Index6',
    },
    {
        path: '/ZYBX/TKZX01/Index6',
        name: 'TKZX01Index6',
        redirect: '/ZYBX/TKZX02/Index6',
    },
    {
        path: '/ZYBX/TKZX01/Index7',
        name: 'TKZX01Index7',
        redirect: '/ZYBX/TKZX02/Index6',
    },
    {
        path: '/ZYBX/TKZX03/Index',
        name: 'TKZX01IndexRandom',
        redirect: '/ZYBX/TKZX02/Index6',
    },

    {
        path: '/ZYBX/TKZX02/Index', // 天彩泰康赠险对外路由
        name: 'TKZX02Index',
        component: () => import('@/views/ZYBX/TaiKang/TKZX02/Index'),
        meta: {
            title: '免费领10万元重疾保障金', // 泰康赠险
        },
    },
    {
        path: '/ZYBX/TKZX02/Index5', // 与Index2一样，带手机号直接到身份证页面
        name: 'TKZX02Index5',
        component: () => import('@/views/ZYBX/TaiKang/TKZX02/Index5'),
        meta: {
            title: '免费领10万元重疾保障金', //
        },
    },
    {
        path: '/ZYBX/TKZX02/Index11', // 泰康心心保通千元重疾太平聚合
        name: 'TKZX02Index11',
        redirect:'/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Index12', // 主泰康，包括天彩泰康+保通千元重疾泰康+保通太平+平安
        name: 'TKZX02Index12',
        redirect:'/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/TKZX02/Index13', // 主泰康，包括天彩泰康+保通千元重疾泰康+平安
        name: 'TKZX02Index13',
        redirect:'/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Index14', // 泰康心心保通千元重疾太平聚合-页面改版
        name: 'TKZX02Index14',
        redirect:'/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Index15', // 泰康千元重疾、飞铁保、太平（主）
        name: 'TKZX02Index15',
        redirect:'/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/TKZX02/Index16', // 主太平，包括天彩泰康+保通千元重疾泰康+保通太平+平安2个
        name: 'TKZX02Index16',
        redirect:'/ZYBX/TKZX02/Index15',
    },
    {
        path: '/ZYBX/TKZX02/Index17', // Index15和Index16的轮询页面
        name: 'TKZX02Index17',
        redirect:'/ZYBX/TKZX02/Index15',
    },
    {
        path: '/ZYBX/TKZX02/Index18', // 页面与Index5一样，用于信息流投放 保通参数77255
        name: 'TKZX02Index18',
        redirect:'/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Index19', // 参照Index12，添加睿薪汇赠险
        name: 'TKZX02Index19',
        redirect:'/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Result',
        name: 'TKZX02Result',
        component: () => import('@/views/ZYBX/TaiKang/TKZX02/Result'),
        meta: {
            title: '免费领10万元重疾保障金', // 泰康赠险
        },
    },
    //    平安赠险转魔方
    {
        path: '/ZYBX/IYBPAZX01/Index1',
        name: 'IYBPAZX01Index1',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX01/Index2',
        name: 'IYBPAZX01Index2',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX02/Index',
        name: 'IYBPAZX02Index',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX02/Index1',
        name: 'IYBPAZX02Index1',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX02/Index2',
        name: 'IYBPAZX02Index2',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX03/Index',
        name: 'IYBPAZX03Index',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX03/Index1',
        name: 'IYBPAZX03Index1',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX03/Index2', // 轮询页面
        name: 'IYBPAZX03Index2',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX03/Index3',
        name: 'IYBPAZX03Index3',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX03/Index4',
        name: 'IYBPAZX03Index4',
        redirect: '/ZYBX/IYBPAZX03/Index8',
    },
    {
        path: '/ZYBX/IYBPAZX03/Index5',
        name: 'IYBPAZX03Index5',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index5'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index6',
        name: 'IYBPAZX03Index6',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index6'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index7',
        name: 'IYBPAZX03Index7',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index7'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index8',
        name: 'IYBPAZX03Index8',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index8'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index9',
        name: 'IYBPAZX03Index9',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index9'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index10',
        name: 'IYBPAZX03Index10',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index10'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index11',
        name: 'IYBPAZX03Index11',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index11'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index12',
        name: 'IYBPAZX03Index12',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index12'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index13',
        name: 'IYBPAZX03Index13',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index13'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index14',
        name: 'IYBPAZX03Index14',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index14'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index15',
        name: 'IYBPAZX03Index15',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index15'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index16',
        name: 'IYBPAZX03Index16',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index16'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index17',
        name: 'IYBPAZX03Index17',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index17'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index20',
        name: 'IYBPAZX03Index20',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index20'),
        meta: {
            title: '免费领取保障', // 保通养老金页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index21',
        name: 'IYBPAZX03Index21',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index21'),
        meta: {
            title: '免费领取保障', // 保通养老金页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index22',
        name: 'IYBPAZX03Index22',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index22'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index23',
        name: 'IYBPAZX03Index23',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index23'),
        meta: {
            title: '免费领取保障', // 保通养老金页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index24',
        name: 'IYBPAZX03Index24',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index24'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPAZX03/Index25',
        name: 'IYBPAZX03Index25',
        component: () => import('@/views/ZYBX/IYB/IYBPA/IYBPAZX03/Index25'),
        meta: {
            title: '免费领取保障', // 保通平安财1000元重疾+100万意外 聚合页面
        },
    },
    {
        path: '/ZYBX/IYBPA04/Index1', // 支付宝
        name: 'IYBPA04Index1',
        redirect: '/ZYBX/IYBTK08/Index1',
    },
    {
        path: '/ZYBX/IYBPA04/Index2', // 加保
        name: 'IYBPA04Index2',
        redirect: '/ZYBX/IYBTK08/Index1',
    },
    {
        path: '/ZYBX/IYBPA04/Index3', // 微信
        name: 'IYBPA04Index3',
        redirect: '/ZYBX/IYBTK08/Index1',
    },
    {
        path: '/ZYBX/IYBPA05/Index1', //
        name: 'IYBPA05Index1',
        redirect: '/ZYBX/IYBPA06/Index4',
    },
    {
        path: '/ZYBX/IYBPA05/Index2', // 加保
        name: 'IYBPA05Index2',
        redirect: '/ZYBX/IYBPA06/Index4',
    },
    {
        path: '/ZYBX/IYBPA05/Index3', //
        name: 'IYBPA05Index3',
        redirect: '/ZYBX/IYBPA06/Index4',
    },
    {
        path: '/ZYBX/IYBPA05/Index4', //
        name: 'IYBPA05Index4',
        redirect: '/ZYBX/IYBPA06/Index4',
    },
    {
        path: '/ZYBX/IYBPA06/Index1', //
        name: 'IYBPA06Index1',
        redirect: '/ZYBX/IYBTK09/Index1',
    },
    {
        path: '/ZYBX/IYBPA06/Index2', //
        name: 'IYBPA06Index2',
        redirect: '/ZYBX/IYBTK09/Index1',
    },
    {
        path: '/ZYBX/IYBPA06/Index3', //
        name: 'IYBPA06Index3',
        redirect: '/ZYBX/IYBTK09/Index1',
    },
    {
        path: '/ZYBX/IYBPA06/Index4', //
        name: 'IYBPA06Index4',
        redirect: '/ZYBX/IYBTK09/Index1',
    },
    {
        path: '/ZYBX/IYBPAZX04/Index1',
        redirect: '/ZYBX/IYBPAZX04/Index10',
    },
    {
        path: '/ZYBX/IYBPAZX04/Index10',
        name: 'IYBPAZX04Index10',
        component: () => import('@/views/ZYBX/IYB/IYBPAZX04/Index10'),
        meta: {
            title: '100万保额免费领', // 保通平安交通意外赠险
        },
    },
    {
        path: '/ZYBX/IYBPAZX04/Index11',
        name: 'IYBPAZX04Index11',
        component: () => import('@/views/ZYBX/IYB/IYBPAZX04/Index11'),
        meta: {
            title: '100万保额免费领', // 保通平安交通意外赠险
        },
    },
    {
        path: '/ZYBX/IYBPAZX04/Index12',
        name: 'IYBPAZX04Index12',
        component: () => import('@/views/ZYBX/IYB/IYBPAZX04/Index12'),
        meta: {
            title: '100万保额免费领', // 保通平安交通意外赠险
        },
    },
    {
        path: '/ZYBX/IYBPA05/Index5', //
        name: 'IYBPA05Index5',
        redirect: '/ZYBX/IYBPA06/Index4',
    },
    {
        path: '/ZYBX/IYBPA05/Index6', //
        name: 'IYBPA05Index6',
        redirect: '/ZYBX/IYBPA06/Index4',
    },
    {
        path: '/ZYBX/IYBPA05/Index7', //
        name: 'IYBPA05Index7',
        redirect: '/ZYBX/IYBPA06/Index4',
    },
    {
        path: '/ZYBX/IYBPA05/Index8', //
        name: 'IYBPA05Index8',
        redirect: '/ZYBX/IYBPA06/Index4',
    },
    {
        path: '/test-link-index',
        name: 'TestLinkIndex1',
        component: () => import('@/views/ZYBX/test'),
        meta: {
            title: '页面测试', // 页面测试
        }
    },
    {
        path: '/ZYBX/Test',
        name: 'Test',
        component: () => import('@/views/Test1/Test3'),
        meta: {
            title: '',
        },
    },
    {
        path: '/ZYBX/Test1',
        name: 'Test1',
        component: () => import('@/views/Test1/Test1'),
        meta: {
            title: '',
        },
    },
    {
        path: '/ZYBX/Test2',
        name: 'Test2',
        component: () => import('@/views/Test1/Test2'),
        meta: {
            title: '',
        },
    },
    {
        path: '/ZYBX/Test4',
        name: 'Test4',
        component: () => import('@/views/Test1/Test4'),
        meta: {
            title: '',
        },
    },
    {
        path: '/ZYBX/TestIframe',
        name: 'TestIframe',
        component: () => import('@/views/Test1/TestIframeAndFs.vue'),
        meta: {
            title: 'TestIframe',
        },
    },
    {
        path: '/ZYBX/CheckMicrophone',
        name: 'CheckMicrophone',
        component: () => import('@/views/Test1/CheckMicrophone'),
        meta: {
            title: '检测麦克风',
        },
    },
    {
        path: '/ZYBX/TKSend/Index1',
        name: 'SendIndex1',
        component: () => import('@/views/ZYBX/Other/Poll/Index1'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/ZASend/Index1',
        name: 'SendIndex2',
        component: () => import('@/views/ZYBX/Other/Poll/Index2'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/PASend/Index1',
        name: 'SendIndex3',
        redirect: '/ZYBX/IYBPASend/Index1',
    },
    {
        path: '/ZYBX/IYBPASend/Index1',
        component: () => import('@/views/ZYBX/Other/Poll/Index3'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/IYBPASend/Index2',
        component: () => import('@/views/ZYBX/Other/Poll/Index7'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/IYBPASend/Index3',
        component: () => import('@/views/ZYBX/Other/Poll/Index8'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/IYBPASend/Index4',
        component: () => import('@/views/ZYBX/Other/Poll/Index9'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/IYBPASend/Index5',
        component: () => import('@/views/ZYBX/Other/Poll/Index10'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/IYBPASend/Index6',
        component: () => import('@/views/ZYBX/Other/Poll/Index16'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/IYBPASend/Index7',
        component: () => import('@/views/ZYBX/Other/Poll/Index17'),
        meta: {
            title: '', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/YBHNSend/Index1',
        component: () => import('@/views/ZYBX/Other/Poll/Index12'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/YBHNSend/Index2',
        component: () => import('@/views/ZYBX/Other/Poll/Index14'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/YBHNSend/Index3',
        component: () => import('@/views/ZYBX/Other/Poll/Index19'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/YBZHSend/Index1',
        component: () => import('@/views/ZYBX/Other/Poll/Index18'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/NWGRSend/Index1',
        component: () => import('@/views/ZYBX/Other/Poll/Index13'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/NWZASend/Index1',
        component: () => import('@/views/ZYBX/Other/Poll/Index4'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/NWZASend/Index2',
        component: () => import('@/views/ZYBX/Other/Poll/Index11'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/NWZASend/Index3',
        component: () => import('@/views/ZYBX/Other/Poll/Index15'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/IYBGRSend/Index1',
        name: 'SendIndex5',
        component: () => import('@/views/ZYBX/Other/Poll/Index5'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/IYBTPYSend/Index1',
        name: 'SendIndex6',
        component: () => import('@/views/ZYBX/Other/Poll/Index6'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/YXTKSend/Index1',
        name: 'YXTKSendIndex1',
        component: () => import('@/views/ZYBX/Other/Poll/Index20'),
        meta: {
            title: '免费领保障金', // 赠险轮询页面
        },
    },
    {
        path: '/ZYBX/TKZX02/Index1',
        name: 'TKZX02Index1',
        redirect: '/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Index2', //
        name: 'TKZX02Index2',
        redirect: '/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Index3', //
        name: 'TKZX02Index3',
        redirect: '/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Index4',//
        name: 'TKZX02Index4',
        redirect: '/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Index6', // 泰康经纪-泰康赠险-轮询页
        name: 'TKZX02Index6',
        component: () => import('@/views/ZYBX/TaiKang/TKZX02/Index6'),
        meta: {
            title: '免费领10万元重疾保障金', //
        },
    },
    {
        path: '/ZYBX/TKZX02/Index7', // 泰康经纪-泰康赠险页面
        name: 'TKZX02Index7',
        component: () => import('@/views/ZYBX/TaiKang/TKZX02/Index7'),
        meta: {
            title: '免费领10万元重疾保障金', //
        },
    },
    {
        path: '/ZYBX/TKZX02/Index8', // 参照Index7 新增睿薪汇赠险
        name: 'TKZX02Index8',
        component: () => import('@/views/ZYBX/TaiKang/TKZX02/Index8'),
        meta: {
            title: '免费领10万元重疾保障金', //
        },
    },
    {
        path: '/ZYBX/TKZX02/Index9', //
        name: 'TKZX02Index9',
        redirect: '/ZYBX/TKZX02/Index12',
    },
    {
        path: '/ZYBX/TKZX02/Index10', // 骏伯
        name: 'TKZX02Index10',
        component: () => import('@/views/ZYBX/TaiKang/TKZX02/Index10'),
        meta: {
            title: '免费领10万元重疾保障金', //
        },
    },
]
