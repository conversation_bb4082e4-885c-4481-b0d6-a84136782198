import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

// 解决重复点击导航路由报错
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err);
}
// 模块路由名称(与文件夹名称相同)
const moduleNameList = [
    "iyb",
    "nw",
    "other",
    "tk",
    "rxh",
    "yh",
    "yx",
];

// 动态路由
export const asyncRoutes = [];

// 固定路由
export const constantRoutes = [
    {
        path: '/400',
        name: 'Exception400', // 404异常页面
        component: () => import('@/views/Exception/400'),
        meta: {
            title: '',
        },
    },
    {
        path: '/404',
        name: 'Exception404', // 404异常页面
        component: () => import('@/views/Exception/404'),
        meta: {
            title: '',
        },
    },
]

const modules = process.env.VUE_APP_MODULES || 'all';

var router = new Router({
    mode: 'history',
    base: process.env.VUE_APP_BASE_URL || '/marketfront/insurance/',
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition
        } else {
            return {x: 0, y: 0}
        }
    },
    routes: [].concat(constantRoutes, asyncRoutes)
})

function genAsyncRoutes(mList) {
    let count = mList.length;
    mList.forEach(moduleName => {
        if (moduleNameList.includes(moduleName)) {
            import(`./${moduleName}/${moduleName}`).then(route => {
                route.default.forEach(val =>{
                    asyncRoutes.push(val);
                })
                if (--count === 0) {
                    router.addRoutes(asyncRoutes);
                }
            });
        }
    });
}

// 处理模块
function disposeModules() {
    if (typeof modules === 'string') {
        if (modules === 'all') {
            genAsyncRoutes(moduleNameList);
        } else if (moduleNameList.includes(modules)) {
            genAsyncRoutes([modules]);
        }
    } else if (modules instanceof RegExp) {
        genAsyncRoutes(moduleNameList.filter(name => modules.test(name)));

    } else if (modules instanceof Array) {
        genAsyncRoutes(modules);
    }
}

disposeModules();

export default router
