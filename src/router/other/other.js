export default [
    {
        path: '/ZYBX/Tools/Index1',
        name: 'ToolsIndex1',
        component: () => import('@/views/ZYBX/Other/Tools/Index1'),
        meta: {
            title: '链接参数解密', // 链接参数解密
        },
    },
    {
        path: '/ZYBX/Tools/Exam',
        component: () => import('@/views/ZYBX/Other/Tools/Exam'),
        meta: {
            title: '', // 链接参数解密
        },
    },
    {
        path: '/ZYBX/SharkShopping/Index1',
        name: 'SharkShoppingIndex1',
        component: () => import('@/views/ZYBX/Other/SharkShopping/Index1'),
        meta: {
            title: '鲨云商城', // 鲨云商城
        },
    },
    {
        path: '/ZYBX/MossKitchen/Index1',
        name: 'MossKitchenIndex1',
        component: () => import('@/views/ZYBX/Other/MossKitchen/Index1'),
        meta: {
            title: '莫士厨房', // 莫士厨房
        },
    },
    {
        path: '/ZYBX/MossKitchen/Index4',
        name: 'MossKitchenIndex4',
        component: () => import('@/views/ZYBX/Other/MossKitchen/Index4'),
        meta: {
            title: '莫士厨房', // 莫士厨房
        },
    },
    {
        path: '/ZYBX/MossKitchen/Index3',
        name: 'MossKitchenIndex3',
        component: () => import('@/views/ZYBX/Other/MossKitchen/Index3'),
        meta: {
            title: '莫士厨房', // 莫士厨房
        },
    },
    {
        path: '/ZYBX/MossKitchen/Index2',
        name: 'MossKitchenIndex2',
        component: () => import('@/views/ZYBX/Other/MossKitchen/Index2'),
        meta: {
            title: '莫士厨房', // 莫士厨房
        },
    },
    {
        path: '/ZYBX/DBH01/Index1',
        name: 'DBH01Index1',
        component: () => import('@/views/ZYBX/Other/DBH/DBH01/Index1'),
        meta: {
            title: '2023年热销「重疾险」精选', // 懂保会重疾
        },
    },
    {
        path: '/ZYBX/DBH01/Index2',
        name: 'DBH01Index2',
        component: () => import('@/views/ZYBX/Other/DBH/DBH01/Index1'),
        meta: {
            title: '2023年热销「重疾险」精选', // 懂保会重疾
        },
    },
    {
        path: '/ZYBX/DBH01/Index3',
        name: 'DBH01Index3',
        component: () => import('@/views/ZYBX/Other/DBH/DBH01/Index2'),
        meta: {
            title: '2023年热销「重疾险」精选', // 懂保会重疾
        },
    },
    {
        path: '/ZYBX/CarT/Index1',
        name: 'CarTIndex1',
        component: () => import('@/views/ZYBX/Other/DBH/DBH01/Index1'),
        meta: {
            title: '2023年热销「重疾险」精选', // 懂保会重疾
        },
    },
    {
        path: '/testLocation',
        name: 'testLocation',
        component: () => import('@/views/TestLocation'),
        meta: {
            title: '地理位置测试',
            keepAlive: true,
        },
    },
    {
        path: '/rrwebplayer',
        component: () => import('@/views/rrweb/rrwebplayer'),
        meta: {
            title: '',
        },
    },
    {
        path: '/rrwebrecorder',
        component: () => import('@/views/rrweb/rrwebrecorder'),
        meta: {
            title: '',
        },
    },
    {
      path: '/test-link',
      name: 'TestLInk',
      component: () => import('@/views/TestLink'),
      meta: {
          title: '',
      },
    },

    {
        path: '/Cashier',
        component: () => import('@/views/common/Cashier/Index1'),
        meta: {
            title: '收银台', //收银台
        },
    },
    {
        path: '/CheckFile',
        component: () => import('@/views/ZYBX/Other/Poll/CheckFile'),
        meta: {
            title: '配置文件检查', // 配置文件检查
        },
    },
]
