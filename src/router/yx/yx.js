export default [
    {
        path: '/ZYBX/YXTKZX01/Index10', // 泰康在线重疾险福利版
        name: 'YXTKZX01Index10',
        component: () => import('@/views/ZYBX/YiXin/YXTKZX01/Index10'),
        meta: {
            title: '免费领保障金',
        },
    },
    {
        path: '/ZYBX/YXTKZX01/Index11', // 泰康在线重疾险福利版
        name: 'YXTKZX01Index11',
        component: () => import('@/views/ZYBX/YiXin/YXTKZX01/Index11'),
        meta: {
            title: '免费领保障金',
        },
    },
    {
        path: '/ZYBX/YXTK01/PreUW/:id', // 泰康百万医疗（轻享版）-预核保
        component: () => import('@/views/ZYBX/YiXin/YXTK01/Index'),
    },
    {
        path: '/ZYBX/YXTK01/Index1', // 泰康百万医疗（轻享版）
        name: 'YXTK01Index1',
        component: () => import('@/views/ZYBX/YiXin/YXTK01/Index1'),
        meta: {
            title: '泰康百万医疗（轻享版）',
        },
    },
    {
        path: '/ZYBX/YXTK01Index2', // 泰康百万医疗（轻享版）
        name: 'YXTK01Index2',
        component: () => import('@/views/ZYBX/YiXin/YXTK01/Index1'),
        meta: {
            title: '泰康百万医疗（轻享版）',
        },
    },
    {
        path: '/ZYBX/YXTK01/Upgrade',
        name: 'YXTK01Upgrade',
        component: () => import('@/views/ZYBX/YiXin/YXTK01/Upgrade'),
        meta: {
            title: '泰康百万医疗（轻享版）', // 泰康百万医疗（轻享版）
        },
    },
    {
        path: '/ZYBX/YXTK01/Result',
        name: 'YXTK01Result',
        component: () => import('@/views/ZYBX/YiXin/YXTK01/Result'),
        meta: {
            title: '泰康百万医疗（轻享版）', // 泰康百万医疗（轻享版）
        },
    },
    {
        path: '/ZYBX/YXTK01/Promotion',
        name: 'YXTK01Promotion',
        component: () => import('@/views/ZYBX/YiXin/YXTK01/Promotion'),
        meta: {
            title: '泰康百万医疗（轻享版）', // 泰康百万医疗（轻享版） 未升级促活页
        },
    },
    {
        path: '/ZYBX/YXTK01/Promotion1',
        name: 'YXTK01Promotion1',
        component: () => import('@/views/ZYBX/YiXin/YXTK01/Promotion1'),
        meta: {
            title: '泰康百万医疗（轻享版）', // 泰康百万医疗（轻享版） 未升级促活页
        },
    },
]
