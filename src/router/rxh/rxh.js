export default [
    {
        path: '/ZYBX/RXHGRZX01/Index1',
        name: 'RXHGRZX01Index1',
        component: () => import('@/views/ZYBX/RXH/RXHGRZX01/Index1'),
        meta: {
            title: '优保安心重疾险（福利版）', // 优保安心重疾险(福利版)-中间页
        },
    },
    {
        path: '/ZYBX/RXHGRZX01/Index10',
        name: 'RXHGRZX01Index10',
        redirect: '/ZYBX/RXHGRZX01/Index11',
    },
    {
        path: '/ZYBX/RXHGRZX01/Index11',
        name: 'RXHGRZX01Index11',
        component: () => import('@/views/ZYBX/RXH/RXHGRZX01/Index11'),
        meta: {
            title: '优保安心重疾险（福利版）', // 优保安心重疾险(福利版)-投保页
        },
    },
    {
        path: '/ZYBX/RXHZXResult',
        name: 'RXHZXResult',
        component: () => import('@/views/ZYBX/RXH/RXHGRZX01/Result'),
    },
    {
        path: '/ZYBX/RXHZHZX01/Index1',
        name: 'RXHZHZX01Index1',
        redirect: '/ZYBX/RXHZHZX01/Index10',
    },
    {
        path: '/ZYBX/RXHZHZX01/Index10',
        name: 'RXHZHZX01Index10',
        component: () => import('@/views/ZYBX/RXH/RXHZHZX01/Index10'),
        meta: {
            title: '免费领保障', // 众安优保·交通出行全能保(福利版)-投保页
        },
    },
    {
        path: '/ZYBX/RXHZHZX02/Index1',
        name: 'RXHZHZX02Index1',
        component: () => import('@/views/ZYBX/RXH/RXHZHZX02/Index1'),
    },
    {
        path: '/ZYBX/RXHZHZX02/Index10',
        name: 'RXHZHZX02Index10',
        component: () => import('@/views/ZYBX/RXH/RXHZHZX02/Index10'),
        meta: {
            title: '免费领保障', // 众安优保·重疾无忧保(福利版)-投保页
        },
    },
    {
        path: '/ZYBX/RXHZHZX02/Index11',
        name: 'RXHZHZX02Index11',
        redirect: '/ZYBX/RXHZHZX02/Index10',
    },
    {
        path: '/ZYBX/RXHZHZX02/Index12', // 跨域名缓存
        name: 'RXHZHZX02Index12',
        component: () => import('@/views/ZYBX/RXH/RXHZHZX02/Index12'),
        meta: {
            title: '免费领保障', // 众安优保·重疾无忧保(福利版)-投保页
        },
    },
    {
        path: '/ZYBX/RXHHNZX01/Index1',
        name: 'RXHHNZX01/Index1',
        component: () => import('@/views/ZYBX/RXH/RXHHNZX01/Index1'),
    },
    {
        path: '/ZYBX/RXHHNZX01/Index10',
        name: 'RXHHNZX01Index10',
        component: () => import('@/views/ZYBX/RXH/RXHHNZX01/Index10'),
        meta: {
            title: '华农畅行交通意外险（福利版）', // 华农畅行交通意外险（福利版）-投保页
        },
    },
    {
        path: '/ZYBX/RXHHNZX01/Index11',
        name: 'RXHHNZX01Index11',
        component: () => import('@/views/ZYBX/RXH/RXHHNZX01/Index11'),
        meta: {
            title: '华农畅行交通意外险（福利版）', // 华农畅行交通意外险（福利版）-投保页
        },
    },
    {
        path: '/ZYBX/RXHHNZX02/Index1',
        name: 'RXHHNZX02/Index1',
        component: () => import('@/views/ZYBX/RXH/RXHHNZX02/Index1'),
    },
    {
        path: '/ZYBX/RXHHNZX02/Index10',
        name: 'RXHHNZX02Index10',
        component: () => import('@/views/ZYBX/RXH/RXHHNZX02/Index10'),
        meta: {
            title: '免费领最高6.8万重疾保障', // 华农畅享重疾险（福利版）-投保页
        },
    },
    {
        path: '/ZYBX/RXHHNZX02/Index11',
        name: 'RXHHNZX02Index11',
        component: () => import('@/views/ZYBX/RXH/RXHHNZX02/Index11'),
        meta: {
            title: '免费领最高6.8万重疾保障', // 华农畅享重疾险（福利版）-投保页
        },
    },
]
