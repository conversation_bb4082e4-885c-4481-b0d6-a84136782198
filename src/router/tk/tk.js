export default  [
    {
        path: '/ZYBX/TK01/Index1',
        name: 'TK01Index1',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK01/Index2',
        name: 'TK01Index2',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK01/Index3',
        name: 'TK01Index3',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK01/Index4',
        name: 'TK01Index4',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK01/Index5',
        name: 'TK01Index5',
        redirect: '/ZYBX/TK12/Home/Index1',
    },

    {
        path: '/ZYBX/TK02/Index1',
        name: 'TK02Index1',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK02/Index2',
        name: 'TK02Index2',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK02/Index3',
        name: 'TK02Index3',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK02/Index4',
        name: 'TK02Index4',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK02/Index5',
        name: 'TK02Index5',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK02/Index6',
        name: 'TK02Index6',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK02/Index7',
        name: 'TK02Index7',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK02/Index8',
        name: 'TK02Index8',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK02/Index9',
        name: 'TK02Index9',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK02/Index10',
        name: 'TK02Index10',
        redirect: '/ZYBX/TK12/Home/Index1',
    },

    {
        path: '/ZYBX/TK03/Index1',
        name: 'TK03Index1',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK03/Index2',
        name: 'TK03Index2',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK03/Index3',
        name: 'TK03Index3',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK03/Index4',
        name: 'TK03Index4',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK03/Index5',
        name: 'TK03Index5',
        redirect: '/ZYBX/TK12/Home/Index1',
    },

    {
        path: '/ZYBX/TK07/Index1',
        name: 'TK07Index1',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK07/Index2',
        name: 'TK07Index2',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK07/Index3',
        name: 'TK07Index3',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK07/Index4',
        name: 'TK07Index4',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK07/Index5',
        name: 'TK07Index5',
        redirect: '/ZYBX/TK12/Home/Index1',
    },

    {
        path: '/ZYBX/TK09/Index1',
        name: 'TK09Index1',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK09/Index2',
        name: 'TK09Index2',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK09/Index3',
        name: 'TK09Index3',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK09/Index4',
        name: 'TK09Index4',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK09/Index5',
        name: 'TK09Index5',
        redirect: '/ZYBX/TK12/Home/Index1',
    },

    {
        path: '/ZYBX/TK10/Index1',
        name: 'TK10Index1',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK10/Index2',
        name: 'TK10Index2',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK10/Index3',
        name: 'TK10Index3',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK10/Index4',
        name: 'TK10Index4',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK10/Index5',
        name: 'TK10Index5',
        redirect: '/ZYBX/TK12/Home/Index1',
    },

    {
        path: '/ZYBX/TK11/Index1',
        name: 'TK11Index1',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK11/Index2',
        name: 'TK11Index2',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK11/Index3',
        name: 'TK11Index3',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK11/Index4',
        name: 'TK11Index4',
        redirect: '/ZYBX/TK12/Home/Index1',
    },
    {
        path: '/ZYBX/TK11/Index5',
        name: 'TK11Index5',
        redirect: '/ZYBX/TK12/Home/Index1',
    },

    {
        path: '/ZYBX/TK12/Home/Index1', //
        name: 'TK12Index1',
        redirect: '/ZYBX/TK17/Index1',
    },
    {
        path: '/ZYBX/TK12/Home/Index2', // 与Index1一样，纯微信版本
        name: 'TK12Index2',
        redirect: '/ZYBX/TK17/Index1',
    },
    {
        path: '/ZYBX/TK12/Home/Index3', //
        name: 'TK12Index3',
        redirect: '/ZYBX/TK17/Index1',
    },
    {
        path: '/ZYBX/TK12/Home/Index4', //
        name: 'TK12Index4',
        redirect: '/ZYBX/TK17/Index1',
    },
   
    {
        path: '/ZYBX/TK15/Home/Index1', //
        name: 'TK15Index1',
        redirect: '/ZYBX/TK18/Index1',
    },
    {
        path: '/ZYBX/TK15/Home/Index2', //
        name: 'TK15Index2',
        redirect: '/ZYBX/TK18/Index1',
    },
    {
        path: '/ZYBX/TK17/Index1', // 泰康新重疾魔方-天彩参数
        name: 'TK17Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK17/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK17/Index2', // 
        name: 'TK17Index2',
        redirect: '/ZYBX/TK17/Index1',
    },
    {
        path: '/ZYBX/TK17/Index3', // 
        name: 'TK17Index3',
        redirect: '/ZYBX/TK17/Index1',
    },
    {
        path: '/ZYBX/TK17/PayAgain',
        name: 'TK17PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK17/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK17/Upgrade',
        name: 'TK17Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK17/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK17/Result',
        name: 'TK17Result',
        component: () => import('@/views/ZYBX/TaiKang/TK17/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK17/Promotion',
        name: 'TK17Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK17/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级中间页
        },
    },
    {
        path: '/ZYBX/TK17/Promotion1',
        name: 'TK17Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK17/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付中间页
        },
    },
    {
        path: '/ZYBX/TK17/Promotion2',
        name: 'TK17Promotion2',
        component: () => import('@/views/ZYBX/TaiKang/TK17/Promotion2'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK17/Promotion3',
        name: 'TK17Promotion3',
        component: () => import('@/views/ZYBX/TaiKang/TK17/Promotion3'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK17/Promotion4', 
        name: 'TK17Promotion4',
        component: () => import('@/views/ZYBX/TaiKang/TK17/Promotion4'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK17/Promotion5',
        name: 'TK17Promotion5',
        component: () => import('@/views/ZYBX/TaiKang/TK17/Promotion5'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK18/Index1', // 泰康新重疾魔方-泰康经纪参数
        name: 'TK18Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK18/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK18/Index2', // 
        name: 'TK18Index2',
        redirect: '/ZYBX/TK18/Index1',
    },
    {
        path: '/ZYBX/TK18/Index3', // 
        name: 'TK18Index3',
        redirect: '/ZYBX/TK18/Index1',
    },
    {
        path: '/ZYBX/TK18/PayAgain',
        name: 'TK18PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK18/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK18/Upgrade',
        name: 'TK18Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK18/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK18/Result',
        name: 'TK18Result',
        component: () => import('@/views/ZYBX/TaiKang/TK18/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK18/Promotion',
        name: 'TK18Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK18/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK18/Promotion1',
        name: 'TK18Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK18/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK20/Index1', // 泰康新重疾魔方-天彩参数
        name: 'TK20Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK20/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK20/Index2', // 
        name: 'TK20Index2',
        redirect: '/ZYBX/TK20/Index1',
    },
    {
        path: '/ZYBX/TK20/Index3', // 
        name: 'TK20Index3',
        redirect: '/ZYBX/TK20/Index1',
    },
    {
        path: '/ZYBX/TK20/PayAgain',
        name: 'TK20PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK20/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK20/Upgrade',
        name: 'TK20Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK20/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK20/Result',
        name: 'TK20Result',
        component: () => import('@/views/ZYBX/TaiKang/TK20/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK20/Promotion',
        name: 'TK20Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK20/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK20/Promotion1',
        name: 'TK20Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK20/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },


    {
        path: '/ZYBX/TK21/Index1', // 泰康新重疾魔方-泰康经纪参数
        name: 'TK21Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK21/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK21/Index2', // 
        name: 'TK21Index2',
        redirect: '/ZYBX/TK21/Index1',
    },
    {
        path: '/ZYBX/TK21/Index3', // 
        name: 'TK21Index3',
        redirect: '/ZYBX/TK21/Index1',
    },
    {
        path: '/ZYBX/TK21/PayAgain',
        name: 'TK21PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK21/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK21/Upgrade',
        name: 'TK21Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK21/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK21/Result',
        name: 'TK21Result',
        component: () => import('@/views/ZYBX/TaiKang/TK21/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK21/Promotion',
        name: 'TK21Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK21/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK21/Promotion1',
        name: 'TK21Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK21/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },

    {
        path: '/ZYBX/TK23/Index1', // 泰康新重疾魔方-天彩参数
        name: 'TK23Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK23/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK23/Index2', // 
        name: 'TK23Index2',
        redirect: '/ZYBX/TK23/Index1',
    },
    {
        path: '/ZYBX/TK23/Index3', // 
        name: 'TK23Index3',
        redirect: '/ZYBX/TK23/Index1',
    },
    {
        path: '/ZYBX/TK23/PayAgain',
        name: 'TK23PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK23/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK23/Upgrade',
        name: 'TK23Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK23/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK23/Result',
        name: 'TK23Result',
        component: () => import('@/views/ZYBX/TaiKang/TK23/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK23/Promotion',
        name: 'TK23Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK23/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK23/Promotion1',
        name: 'TK23Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK23/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK23/Promotion2',
        name: 'TK23Promotion2',
        component: () => import('@/views/ZYBX/TaiKang/TK23/Promotion2'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK23/Promotion3',
        name: 'TK23Promotion3',
        component: () => import('@/views/ZYBX/TaiKang/TK23/Promotion3'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK23/Promotion4',
        name: 'TK23Promotion4',
        component: () => import('@/views/ZYBX/TaiKang/TK23/Promotion4'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK23/Promotion5',
        name: 'TK23Promotion5',
        component: () => import('@/views/ZYBX/TaiKang/TK23/Promotion5'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK24/Index1', // 泰康新重疾魔方-泰康经纪参数
        name: 'TK24Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK24/Index1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK24/Index2', // 
        name: 'TK24Index2',
        redirect: '/ZYBX/TK24/Index1',
    },
    {
        path: '/ZYBX/TK24/Index3', // 
        name: 'TK24Index3',
        redirect: '/ZYBX/TK24/Index1',
    },
    {
        path: '/ZYBX/TK24/PayAgain',
        name: 'TK24PayAgain',
        component: () => import('@/views/ZYBX/TaiKang/TK24/PayAgain'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 支付挽回页
        },
    },
    {
        path: '/ZYBX/TK24/Upgrade',
        name: 'TK24Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK24/Upgrade'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK24/Result',
        name: 'TK24Result',
        component: () => import('@/views/ZYBX/TaiKang/TK24/Result'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 正常版
        },
    },
    {
        path: '/ZYBX/TK24/Promotion',
        name: 'TK24Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK24/Promotion'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK24/Promotion1',
        name: 'TK24Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK24/Promotion1'),
        meta: {
            title: '泰超能·百万医疗险', // 泰超能·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK40/PreUW/:id', // 全能保·百万医疗险-预核保
        component: () => import('@/views/ZYBX/TaiKang/TK40/Index'),
    },
    {
        path: '/ZYBX/TK40/Index1',
        name: 'TK40Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK40/Index1'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗险 用于测试环境验收
        },
    },
    {
        path: '/ZYBX/TK40/Index2',
        name: 'TK40Index2',
        component: () => import('@/views/ZYBX/TaiKang/TK40/Index2'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗险 用于生产环境投放
        },
    },
    {
        path: '/ZYBX/TK40/Index3',
        name: 'TK40Index3',
        component: () => import('@/views/ZYBX/TaiKang/TK40/Index3'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗险 用于生产环境投放
        },
    },
    {
        path: '/ZYBX/TK40/Index4',
        name: 'TK40Index4',
        component: () => import('@/views/ZYBX/TaiKang/TK40/Index4'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗险 用于生产环境投放
        },
    },
    {
        path: '/ZYBX/TK40/Index11',
        name: 'TK40Index11',
        component: () => import('@/views/ZYBX/TaiKang/TK40/Index11'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗险-一键多绑
        },
    },
    {
        path: '/ZYBX/TK40/Upgrade',
        name: 'TK40Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK40/Upgrade'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK40/Result',
        name: 'TK40Result',
        component: () => import('@/views/ZYBX/TaiKang/TK40/Result'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗险
        },
    },
    {
        path: '/ZYBX/TK40/Promotion',
        name: 'TK40Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK40/Promotion'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK40/Promotion1',
        name: 'TK40Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK40/Promotion1'),
        meta: {
            title: '全能保·百万医疗险', // 全能保·百万医疗险 未支付促活页
        },
    },
    {
        path: '/ZYBX/TK41/PreUW/:id', // 泰康2024防癌医疗险-预核保
        component: () => import('@/views/ZYBX/TaiKang/TK41/Index'),
    },
    {
        path: '/ZYBX/TK41/Index1', // 泰康2024防癌医疗险
        name: 'TK41Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK41/Index1'),
        meta: {
            title: '泰康2024防癌医疗险',
        },
    },
    {
        path: '/ZYBX/TK41/Index2', // 泰康2024防癌医疗险
        name: 'TK41Index2',
        component: () => import('@/views/ZYBX/TaiKang/TK41/Index2'),
        meta: {
            title: '泰康2024防癌医疗险',
        },
    },
    {
        path: '/ZYBX/TK41/Index11', // 泰康2024防癌医疗险
        name: 'TK41Index11',
        component: () => import('@/views/ZYBX/TaiKang/TK41/Index11'),
        meta: {
            title: '泰康2024防癌医疗险',
        },
    },
    {
        path: '/ZYBX/TK41/Upgrade',
        name: 'TK41Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK41/Upgrade'),
        meta: {
            title: '泰康2024防癌医疗险', // 泰康2024防癌医疗险
        },
    },
    {
        path: '/ZYBX/TK41/Result',
        name: 'TK41Result',
        component: () => import('@/views/ZYBX/TaiKang/TK41/Result'),
        meta: {
            title: '泰康2024防癌医疗险', // 泰康2024防癌医疗险
        },
    },
    {
        path: '/ZYBX/TK41/Promotion',
        name: 'TK41Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK41/Promotion'),
        meta: {
            title: '泰康2024防癌医疗险', // 泰康2024防癌医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK41/Promotion1',
        name: 'TK41Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK41/Promotion1'),
        meta: {
            title: '泰康2024防癌医疗险', // 泰康2024防癌医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK42/PreUW/:id', // 泰康2024防癌医疗险-预核保
        component: () => import('@/views/ZYBX/TaiKang/TK42/Index'),
    },
    {
        path: '/ZYBX/TK42/Index1', // 泰康2024防癌医疗险
        name: 'TK42Index1',
        component: () => import('@/views/ZYBX/TaiKang/TK42/Index1'),
        meta: {
            title: '泰康2024防癌医疗险',
        },
    },
    {
        path: '/ZYBX/TK42/Index2', // 泰康2024防癌医疗险
        name: 'TK42Index2',
        component: () => import('@/views/ZYBX/TaiKang/TK42/Index2'),
        meta: {
            title: '泰康2024防癌医疗险',
        },
    },
    {
        path: '/ZYBX/TK42/Index11', // 泰康2024防癌医疗险
        name: 'TK42Index11',
        component: () => import('@/views/ZYBX/TaiKang/TK42/Index11'),
        meta: {
            title: '泰康2024防癌医疗险',
        },
    },
    {
        path: '/ZYBX/TK42/Upgrade',
        name: 'TK42Upgrade',
        component: () => import('@/views/ZYBX/TaiKang/TK42/Upgrade'),
        meta: {
            title: '泰康2024防癌医疗险', // 泰康2024防癌医疗险
        },
    },
    {
        path: '/ZYBX/TK42/Result',
        name: 'TK42Result',
        component: () => import('@/views/ZYBX/TaiKang/TK42/Result'),
        meta: {
            title: '泰康2024防癌医疗险', // 泰康2024防癌医疗险
        },
    },
    {
        path: '/ZYBX/TK42/Promotion',
        name: 'TK42Promotion',
        component: () => import('@/views/ZYBX/TaiKang/TK42/Promotion'),
        meta: {
            title: '泰康2024防癌医疗险', // 泰康2024防癌医疗险 未升级促活页
        },
    },
    {
        path: '/ZYBX/TK42/Promotion1',
        name: 'TK42Promotion1',
        component: () => import('@/views/ZYBX/TaiKang/TK42/Promotion1'),
        meta: {
            title: '泰康2024防癌医疗险', // 泰康2024防癌医疗险 未升级促活页
        },
    },
]
