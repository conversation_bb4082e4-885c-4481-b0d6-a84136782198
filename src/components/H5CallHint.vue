<template>
    <!--H5双向通话提示弹窗-->
    <van-popup v-model="h5CallHintVisible" class="mask" :close-on-click-overlay="false" position="bottom">
        <img src="@/assets/imgs/h5-call/hint.png" />
        <div class="mask-button" @click="onClickH5CallHintConfirm">
            好的，我知道了<span v-show="h5CallHintSecond > 0">({{ h5CallHintSecond }}s)</span>
        </div>
    </van-popup>
</template>

<script>
import { H5Call } from '@/utils/h5-call';

export default {
    name: "H5CallHint",
    props: {
        // 是否启用H5通话功能
        enabled: {
            type: Boolean,
            default: false
        },
        // 渠道信息
        channel: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            h5Call: null,
            h5CallHintVisible: false,
            h5CallHintTimer: null,
            h5CallHintSecond: 0,
        };
    },
    mounted() {
        if (this.enabled) {
            // 因为浏览器的安全策略，只有触发用户的点击交互后，才能播放音频，
            // 所以这里监听点击事件，等点击后再发起通话
            document.addEventListener('click', this.handleFirstClick, { once: true });
        }
    },
    beforeDestroy() {
        // H5通话断开连接并销毁
        document.removeEventListener('click', this.handleFirstClick);
        if (this.h5Call) {
            this.h5Call.endCall().then(() => {
                this.h5Call = null;
            })
        }
        this.stopH5CallHintTimer();
    },
    methods: {
        /**
         * 用户在当前页面第一次点击
         */
        handleFirstClick() {
            console.log('用户在当前页面第一次点击', 'handleFirstClick')
            const audio = document.querySelector('audio');

            let timer = setTimeout(() => {
                audio && audio.pause();
            }, 150);

            if (this.h5Call) {
                this.h5Call.endCall()
                this.h5Call = null
            }

            this.h5Call = new H5Call({ channel: this.channel })
            if (this.h5Call) {
                this.h5Call.initInstance().then(() => {
                    window.h5CallEnabled = true;
                    this.h5CallHintVisible = true;
                    this.startH5CallHintTimer();
                }).catch((e) => {
                    console.error('初始化失败', e)
                    this.h5CallHintVisible = false;
                    timer && clearTimeout(timer);
                    audio && audio.play();
                })
            } else {
                console.error('初始化失败')
                this.h5CallHintVisible = false;
                timer && clearTimeout(timer);
                audio && audio.play();
            }
        },
        stopH5CallHintTimer() {
            this.h5CallHintVisible = false;
            clearTimeout(this.h5CallHintTimer);
            this.h5CallHintSecond = 0;
        },
        startH5CallHintTimer() {
            this.stopH5CallHintTimer();
            this.h5CallHintSecond = 3;
            this.h5CallHintVisible = true;
            this.h5CallHintTimer = setInterval(() => {
                this.h5CallHintSecond--;
                if (this.h5CallHintSecond <= 0) {
                    this.stopH5CallHintTimer();
                }
            }, 1000)
        },
        onClickH5CallHintConfirm() {
            this.stopH5CallHintTimer();
        },
    }
}
</script>

<style lang="less" scoped>
.mask {
    width: 100%;
    height: 100%;
    background-color: unset;

    img {
        position: absolute;
        bottom: 0;
        display: block;
        width: 3.75rem;
        margin: 0 auto;
    }

    .mask-button {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        inset: auto 0 .16rem 0;
        width: 3.33rem;
        height: .52rem;
        margin: 0 auto;
        background: linear-gradient(90deg, #438AF6 0%, #438AF6 100%);
        border-radius: .16rem;
        font-weight: 600;
        font-size: .24rem;
        color: #FFFFFF;
        text-align: center;
    }
}
</style>
