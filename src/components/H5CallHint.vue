<template>
    <!--H5双向通话提示弹窗-->
    <van-popup v-model="visible" class="mask" :close-on-click-overlay="false" position="bottom">
        <img src="@/assets/imgs/h5-call/hint.png" />
        <div class="mask-button" @click="onClickConfirm">
            好的，我知道了<span v-show="countdown > 0">({{ countdown }}s)</span>
        </div>
    </van-popup>
</template>

<script>
export default {
    name: "H5CallHint",
    data() {
        return {
            visible: false,
            timer: null,
            countdown: 0,
        };
    },
    methods: {
        /**
         * 显示弹窗
         */
        show() {
            this.visible = true;
            this.startTimer();
        },

        /**
         * 隐藏弹窗
         */
        hide() {
            this.visible = false;
            this.stopTimer();
        },

        /**
         * 开始倒计时
         */
        startTimer() {
            this.stopTimer();
            this.countdown = 3;
            this.timer = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    this.hide();
                }
            }, 1000)
        },

        /**
         * 停止倒计时
         */
        stopTimer() {
            clearTimeout(this.timer);
            this.countdown = 0;
        },

        /**
         * 点击确认按钮
         */
        onClickConfirm() {
            this.hide();
        },
    },

    beforeDestroy() {
        this.stopTimer();
    }
}
</script>

<style lang="less" scoped>
.mask {
    width: 100%;
    height: 100%;
    background-color: unset;

    img {
        position: absolute;
        bottom: 0;
        display: block;
        width: 3.75rem;
        margin: 0 auto;
    }

    .mask-button {
        position: absolute;
        display: flex;
        justify-content: center;
        align-items: center;
        inset: auto 0 .16rem 0;
        width: 3.33rem;
        height: .52rem;
        margin: 0 auto;
        background: linear-gradient(90deg, #438AF6 0%, #438AF6 100%);
        border-radius: .16rem;
        font-weight: 600;
        font-size: .24rem;
        color: #FFFFFF;
        text-align: center;
    }
}
</style>
