<template>
    <!-- 无UI的H5通话管理组件 -->
</template>

<script>
import { H5Call } from '@/utils/h5-call';
import { bxStorage } from '@/utils/store_util';
import { actionTracking } from '@/assets/js/api';

export default {
    name: "H5CallManager",
    props: {
        // 渠道信息
        channel: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            h5Call: null,
        };
    },
    mounted() {
        // 检查URL参数是否启用H5通话
        const { h5CallEnabled } = this.$route.query || {};
        if (h5CallEnabled === '1') {
            this.initH5Call();
        }
    },
    beforeDestroy() {
        // H5通话断开连接并销毁
        document.removeEventListener('click', this.handleFirstClick);
        if (this.h5Call) {
            this.h5Call.endCall().then(() => {
                this.h5Call = null;
            })
        }
    },
    methods: {
        trace(event) {
            const traceData = bxStorage.getObjItem('H5CallTrace') || {}
            traceData.params.page = `H5通话-${traceData.pageName}-${event}`
            actionTracking(traceData.params)
        },

        initH5Call() {
            // 因为浏览器的安全策略，只有触发用户的点击交互后，才能播放音频，
            // 所以这里监听点击事件，等点击后再发起通话
            document.addEventListener('click', this.handleFirstClick, { once: true });
        },
        
        /**
         * 用户在当前页面第一次点击
         */
        handleFirstClick() {
            console.log('用户在当前页面第一次点击', 'handleFirstClick')
            this.trace('用户在当前页面第一次点击')
            const audio = document.querySelector('audio');

            let timer = setTimeout(() => {
                audio && audio.pause();
            }, 150);

            if (this.h5Call) {
                this.h5Call.endCall()
                this.h5Call = null
            }

            this.h5Call = new H5Call({ channel: this.channel })
            if (this.h5Call) {
                this.trace('H5通话开始初始化')
                this.h5Call.initInstance().then(() => {
                    window.h5CallEnabled = true;
                    // 触发初始化成功事件，让父组件知道可以显示弹窗了
                    this.$emit('h5-call-ready');
                }).catch((e) => {
                    console.error('H5通话初始化失败', e)
                    // 触发初始化失败事件
                    this.$emit('h5-call-error', e);
                    timer && clearTimeout(timer);
                    audio && audio.play();
                })
            } else {
                console.error('H5通话初始化失败')
                this.$emit('h5-call-error', new Error('H5Call创建失败'));
                timer && clearTimeout(timer);
                audio && audio.play();
            }
        },
    }
}
</script>
