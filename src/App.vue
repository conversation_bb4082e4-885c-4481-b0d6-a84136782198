<template>
    <div id="app">
        <!--    <keep-alive>-->
        <!--      <router-view v-wechat-title='$route.meta.title' />-->
        <!--    </keep-alive>-->
        <keep-alive>
            <router-view v-if="$route.meta.keepAlive" v-wechat-title='$route.meta.title'></router-view>
        </keep-alive>
        <router-view v-if="!$route.meta.keepAlive"></router-view>

    </div>
</template>

<script>
import moment from 'moment';
import { injectDebugTools, } from "@/assets/js/fn";
import { AESDecrypt } from '@/assets/js/common';
import { H5Call } from '@/utils/h5-call';

export default {
    name: "App",
    data() {
        return {
            h5Call: null,
            h5CallHintVisible: false,
            h5CallHintTimer: null,
            h5CallHintSecond: 0,
        };
    },
    methods: {
        setRem() {
            var width =
                document.documentElement.clientWidth > 640
                    ? 640
                    : document.documentElement.clientWidth;
            var offWidth = width / 3.75; // 这里用宽度/30表示1rem取得的px
            document.getElementsByTagName("html")[0].style.fontSize = offWidth + "px"; // 把rem的值复制给顶级标签html的font-size
        },
        /**
         * 用户在当前页面第一次点击
         */
        handleFirstClick() {
            console.log('用户在当前页面第一次点击', 'handleFirstClick')
            const audio = document.querySelector('audio');

            let timer = setTimeout(() => {
                audio && audio.pause();
            }, 150);

            if (this.h5Call) {
                this.h5Call.endCall()
                this.h5Call = null
            }

            const { channel } = this.$route.query || {}
            this.h5Call = new H5Call({ channel })
            if (this.h5Call) {
                this.h5Call.initInstance().then(() => {
                    window.h5CallEnabled = true;
                    this.h5CallHintVisible = true;
                    this.startH5CallHintTimer();
                }).catch((e) => {
                    console.error('初始化失败', e)
                    this.h5CallHintVisible = false;
                    timer && clearTimeout(timer);
                    audio && audio.play();
                })
            } else {
                console.error('初始化失败')
                this.h5CallHintVisible = false;
                timer && clearTimeout(timer);
                audio && audio.play();
            }
        },
        stopH5CallHintTimer() {
            this.h5CallHintVisible = false;
            clearTimeout(this.h5CallHintTimer);
            this.h5CallHintSecond = 0;
        },
        startH5CallHintTimer() {
            this.stopH5CallHintTimer();
            this.h5CallHintSecond = 3;
            this.h5CallHintVisible = true;
            this.h5CallHintTimer = setInterval(() => {
                this.h5CallHintSecond--;
                if (this.h5CallHintSecond <= 0) {
                    this.stopH5CallHintTimer();
                }
            }, 1000)
        },
        onClickH5CallHintConfirm() {
            this.stopH5CallHintTimer();
        },
    },
    mounted() {
        console.log('publishTime==', moment(parseInt(this.version)).format('YYYY/MM/DD HH:mm:ss'))

        let vm = this;
        // 初始化
        vm.setRem();

        // 改变窗口大小时重新设置 rem
        window.onresize = function () {
            vm.setRem();
        };

        // 处理点击输入框后的一系列现象
        $('body').delegate('input[type="text"],input[type="tel"],input[type="number"],textarea', 'click', function () {
            var target = this;
            if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
                //     //Ios
            } else if (/(Android)/i.test(navigator.userAgent)) {
                setTimeout(function () {
                    // target.scrollIntoView(false);
                    target.scrollIntoViewIfNeeded(true);
                }, 800);
            }
        });

        $('body').delegate('input[type="text"],input[type="tel"],input[type="number"],textarea,select', 'blur', function () {
            if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
                document.body && (document.body.scrollTop = document.body.scrollTop);
            }
        });

        try {
            const { channel, ctm } = this.$route.query || {};
            const version = moment().format('YYYYMMDDHH');

            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/config/channel_config.js' + `?v=${version}`;
            document.getElementsByTagName('head')[0].appendChild(script);
            script.onload = () => {
                const excludes = window.channel_exclude || [];
                const channels = excludes.map(v => {
                    const [min, max] = (v + '').split('-');
                    return { min: min, max: max || min }
                });

                let isExclude = channels.some(v => v.min <= channel && v.max >= channel);

                if (!isExclude) {
                    const excludes = window.string_exclude || [];
                    isExclude = excludes.some(v => window.location.href.indexOf(v) > 0);
                }

                if (isExclude) {
                    document.title = '';
                    const body = document.createElement('body');
                    document.body = body;
                    return window.location.href = 'about:blank';
                }

                if (ctm) {
                    const isPromotion = window.location.href.indexOf('/Promotion') > 0;
                    if (isPromotion) {
                        const isIYBPA06 = window.location.href.indexOf('IYBPA06/Promotion') > 0;
                        const days = isIYBPA06 ? (window.promotePA06ValidDays || 1) : (window.promoteValidDays || 7);
                        const ts = ctm.replace(/-/g, '+').replace(/_/g, '/');
                        const isExpired = moment().diff(moment(+AESDecrypt(ts)), 'days') > days;
                        if (isExpired) {
                            document.title = '';
                            const body = document.createElement('body');
                            document.body = body;
                            return window.location.href = 'about:blank';
                        }
                    }
                }

                // 如果URL参数里表示启用H5通话，则更新到全局变量，并启动点击监听
                const { h5CallEnabled } = this.$route.query || {};
                if (h5CallEnabled) {
                    // 因为浏览器的安全策略，只有触发用户的点击交互后，才能播放音频，
                    // 所以这里监听点击事件，等点击后再发起通话
                    document.addEventListener('click', this.handleFirstClick, { once: true });
                }
            }
        } catch (error) {


        }

        try {
            if (window.location.href.indexOf('isDebug=1') > 0 || document.referrer.indexOf('isDebug=1') > 0) {
                injectDebugTools();
            }
        } catch (e) {

        }
    },
    beforeDestroy() {
        // H5通话断开连接并销毁
        document.removeEventListener('click', this.handleFirstClick);
        if (this.h5Call) {
            this.h5Call.endCall().then(() => {
                this.h5Call = null;
            })
        }
        this.stopH5CallHintTimer();
    },
};
(function () {
    if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
        handleFontSize();
    } else {
        if (document.addEventListener) {
            document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
        } else if (document.attachEvent) {
            document.attachEvent("WeixinJSBridgeReady", handleFontSize);
            document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
        }
    }
    function handleFontSize() {
        // 设置网页字体为默认大小
        WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 0 });
        // 重写设置网页字体大小的事件
        WeixinJSBridge.on('menu:setfont', function () {
            WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 0 });
        });
    }
})();
</script>

<style lang="less" type="text/less">
@import "assets/css/common.css";

.mint-toast {
    width: 70% !important;
}

.mint-toast-text {
    font-size: 0.16rem !important;
}

#app {
    -webkit-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
    -moz-text-size-adjust: 100% !important;

    font-family: "Avenir", Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    height: 100%;
    /*overflow: auto;*/
    max-width: 3.75rem;
    margin: 0 auto;

    .mask {
        width: 100%;
        height: 100%;
        background-color: unset;

        img {
            position: absolute;
            bottom: 0;
            display: block;
            width: 3.75rem;
            margin: 0 auto;
        }

        .mask-button {
            position: absolute;
            display: flex;
            justify-content: center;
            align-items: center;
            inset: auto 0 .16rem 0;
            width: 3.33rem;
            height: .52rem;
            margin: 0 auto;
            background: linear-gradient(90deg, #438AF6 0%, #438AF6 100%);
            border-radius: .16rem;
            font-weight: 600;
            font-size: .24rem;
            color: #FFFFFF;
            text-align: center;
        }
    }
}
</style>
