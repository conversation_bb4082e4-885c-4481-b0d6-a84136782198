<template>
    <div id="app">
        <!--    <keep-alive>-->
        <!--      <router-view v-wechat-title='$route.meta.title' />-->
        <!--    </keep-alive>-->
        <keep-alive>
            <router-view v-if="$route.meta.keepAlive" v-wechat-title='$route.meta.title'></router-view>
        </keep-alive>
        <router-view v-if="!$route.meta.keepAlive"></router-view>

    </div>
</template>

<script>
import moment from 'moment';
import { injectDebugTools, } from "@/assets/js/fn";
import { AESDecrypt } from '@/assets/js/common';

export default {
    name: "App",
    data() {
        return {};
    },
    methods: {
        setRem() {
            var width =
                document.documentElement.clientWidth > 640
                    ? 640
                    : document.documentElement.clientWidth;
            var offWidth = width / 3.75; // 这里用宽度/30表示1rem取得的px
            document.getElementsByTagName("html")[0].style.fontSize = offWidth + "px"; // 把rem的值复制给顶级标签html的font-size
        },
    },
    mounted() {
        console.log('publishTime==', moment(parseInt(this.version)).format('YYYY/MM/DD HH:mm:ss'))

        let vm = this;
        // 初始化
        vm.setRem();

        // 改变窗口大小时重新设置 rem
        window.onresize = function () {
            vm.setRem();
        };

        // 处理点击输入框后的一系列现象
        $('body').delegate('input[type="text"],input[type="tel"],input[type="number"],textarea', 'click', function () {
            var target = this;
            if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
                //     //Ios
            } else if (/(Android)/i.test(navigator.userAgent)) {
                setTimeout(function () {
                    // target.scrollIntoView(false);
                    target.scrollIntoViewIfNeeded(true);
                }, 800);
            }
        });

        $('body').delegate('input[type="text"],input[type="tel"],input[type="number"],textarea,select', 'blur', function () {
            if (/(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent)) {
                document.body && (document.body.scrollTop = document.body.scrollTop);
            }
        });

        try {
            const { channel, ctm } = this.$route.query || {};
            const version = moment().format('YYYYMMDDHH');

            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/config/channel_config.js' + `?v=${version}`;
            document.getElementsByTagName('head')[0].appendChild(script);
            script.onload = () => {
                const excludes = window.channel_exclude || [];
                const channels = excludes.map(v => {
                    const [min, max] = (v + '').split('-');
                    return { min: min, max: max || min }
                });

                let isExclude = channels.some(v => v.min <= channel && v.max >= channel);

                if (!isExclude) {
                    const excludes = window.string_exclude || [];
                    isExclude = excludes.some(v => window.location.href.indexOf(v) > 0);
                }

                if (isExclude) {
                    document.title = '';
                    const body = document.createElement('body');
                    document.body = body;
                    return window.location.href = 'about:blank';
                }

                if (ctm) {
                    const isPromotion = window.location.href.indexOf('/Promotion') > 0;
                    if (isPromotion) {
                        const isIYBPA06 = window.location.href.indexOf('IYBPA06/Promotion') > 0;
                        const days = isIYBPA06 ? (window.promotePA06ValidDays || 1) : (window.promoteValidDays || 7);
                        const ts = ctm.replace(/-/g, '+').replace(/_/g, '/');
                        const isExpired = moment().diff(moment(+AESDecrypt(ts)), 'days') > days;
                        if (isExpired) {
                            document.title = '';
                            const body = document.createElement('body');
                            document.body = body;
                            return window.location.href = 'about:blank';
                        }
                    }
                }

            }
        } catch (error) {


        }

        try {
            if (window.location.href.indexOf('isDebug=1') > 0 || document.referrer.indexOf('isDebug=1') > 0) {
                injectDebugTools();
            }
        } catch (e) {

        }
    },
};
(function () {
    if (typeof WeixinJSBridge == "object" && typeof WeixinJSBridge.invoke == "function") {
        handleFontSize();
    } else {
        if (document.addEventListener) {
            document.addEventListener("WeixinJSBridgeReady", handleFontSize, false);
        } else if (document.attachEvent) {
            document.attachEvent("WeixinJSBridgeReady", handleFontSize);
            document.attachEvent("onWeixinJSBridgeReady", handleFontSize);
        }
    }
    function handleFontSize() {
        // 设置网页字体为默认大小
        WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 0 });
        // 重写设置网页字体大小的事件
        WeixinJSBridge.on('menu:setfont', function () {
            WeixinJSBridge.invoke('setFontSizeCallback', { 'fontSize': 0 });
        });
    }
})();
</script>

<style lang="less" type="text/less">
@import "assets/css/common.css";

.mint-toast {
    width: 70% !important;
}

.mint-toast-text {
    font-size: 0.16rem !important;
}

#app {
    -webkit-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
    -moz-text-size-adjust: 100% !important;

    font-family: "Avenir", Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    height: 100%;
    /*overflow: auto;*/
    max-width: 3.75rem;
    margin: 0 auto;
}
</style>
