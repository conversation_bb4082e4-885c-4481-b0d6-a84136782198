import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

// 解决重复点击导航路由报错
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err);
}

var router = new Router({
    mode: 'history',
    base: process.env.VUE_APP_BASE_URL || '/marketfront/insurance/',
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition
        } else {
            return {x: 0, y: 0}
        }
    },
    routes: [
        //异常信息页面
        {
            path: '/404',
            name: 'Exception404', // 404异常页面
            component: () => import('@/views/Exception/404'),
            meta: {
                title: '404',
            },
        },
        {
            path: '/test',
            name: 'test',
            component: () => import('@/views/Test'),
            meta: {
                title: '',
            },
        },
        {
            path: '/rrweb',
            name: 'rrweb',
            component: () => import('@/views/rrweb/Index'),
            meta: {
                title: '',
            },
        },
    ],
})

export default router
