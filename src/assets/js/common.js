import {baseUrl,} from './constans'
import {zhTran2Simp} from "./zhTran2Simple";
import CryptoJS from "crypto-js";

export const fetchWSLocationFromIp = (ip) => {
    const url = 'https://apis.map.qq.com/ws/location/v1/ip?key=L7QBZ-HI6C6-R4QSW-ERS5Y-3ZYFH-Y6BKN&output=jsonp&callback=ip_callback' + (ip ? `&ip=${ip}` : '');

    return new Promise((resolve, reject) => {
        window.ip_callback = (res) => {
            const {status, result} = res;
            if (status != 0) {
                return reject();
            }

            const {ip, location, ad_info} = result || {};
            // {"ip":"*************","lat":31.30227,"lng":120.63132,"nation":"中国","province":"江苏省","city":"苏州市","district":"姑苏区","adcode":320508,"nation_code":156}
            const obj = Object.assign({ip}, location, ad_info);
            resolve(obj);
        }

        let script = document.querySelector('#id_2311281037');
        if (script) {
            script.remove();
        }
        script = document.createElement('script');
        script.id = 'id_2311281037';
        script.src = url;
        script.onload = () => {

        };
        script.onerror = () => {
            reject();
        };
        document.body.appendChild(script);
    });
}

export const AESEncrypt = (obj) => {
    const message = typeof obj === 'string'? obj : JSON.stringify(obj);
    const bytes = CryptoJS.enc.Utf8.parse(message);
    const key = CryptoJS.enc.Utf8.parse('VlfQyUHanBcmDUrc');
    const sign = CryptoJS.AES.encrypt(bytes, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    }).toString();

    return sign;
}

export const AESDecrypt = (message) => {
    const key = CryptoJS.enc.Utf8.parse('VlfQyUHanBcmDUrc');
    const sign = CryptoJS.AES.decrypt(message, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    }).toString(CryptoJS.enc.Utf8);

    return sign;
}

export const object2QueryParams = (obj) => {
    const getDataType = (o) => {
        return Object.prototype.toString.call(o).slice(8, -1).toLowerCase();
    }

    if (getDataType(obj) != 'object') {
        return;
    }

    let result = '';

    for (const key in obj) {
        const value = obj[key];
        const valueType = getDataType(value);
        if (valueType != 'null' && valueType != 'undefined') {
            result += `&${key}=${value}`
        }
    }

    return result;
}

export const queryURLParams = (url) => {
    if (!url) return {};

    const search = url;
    const regExp = /[?&][^?&]+=[^?&]+/g;
    const params = {};
    const matchResult = search.match(regExp);
    if (matchResult) {
        matchResult.forEach(item => {
            const [key, value] = item.substring(1).split('=');
            params[key] = value;
        });
    }

    return params;
}


//验证是否为空
function isNull(value, text) {
    if (value === undefined || value === null || value === "") {
        return text + "不能为空"
    }
    return ""
}

//验证手机号码是否正确
function checkTel(tel) {
    if (tel == undefined || tel == "" || tel == null) {
        return "手机号码不能为空";
    }

    var telReg = /^1[3456789]\d{9}$/;
    if (!telReg.test(tel)) {
        return "手机号码格式有误";
    }

    return ""
}

// 验证手机号
function isPhoneNum(phone) {
    if (!phone) {
        return false;
    }
    // return RegExp(/^1\d{10}$/).test(phone);
    return RegExp(/^1[3456789]\d{9}$/).test(phone);
}

export const isMaskedAndT1Phone = (phone, phone1) => {
    phone = phone + '';
    phone1 = phone1 + '';

    if (!phone || !phone1) {
        return false;
    }

    return isMaskedPhone(phone) && (!phone1.startsWith('H2')) && ((phone1.indexOf('_') > 0) || phone1.indexOf('-') > 0);
}

export const isMaskedPhone = (phone) => {
    phone = phone + '';
    if (!phone) {
        return false;
    }

    const pattern = /^1[3-9]\d\*{4}\d{4}$/;

    return pattern.test(phone);
}

// 把号码18123456789转成181****6789
export const star_marked_phone = (phone = '') => {
    if (phone.length != 11) {
        return phone;
    }
    let temp = phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    return temp;
}

// 把号码18123456789转成181****6789
export const securePhone = (phone) => {
    if (!isPhoneNum(phone)) {
        return phone;
    }
    let temp = phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
    return temp;
}

// 把号码320164584654666623转成3201**********6623
export const secureIdCard = (idcard) => {
    if (!isCardNo(idcard)) {
        return idcard;
    }

    // let temp = idcard.replace(/(\d{4})\d{4}(\d{4})/, "$1****$2");

    var temp = idcard.replace(/^(.{4})(?:.+)(.{4})$/,"$1******$2");
    return temp;
}
export const secureName = (name) => {
  if (!isPersonName(name)) {
      return name;
  }
  let newStr;
  if (name.length === 2) {
    newStr = name.substr(0, 1) + '*';
  } else if (name.length > 2) {
      let char = '';
      for (let i = 0, len = name.length - 2; i < len; i++) {
          char += '*';
      }
      newStr = name.substr(0, 1) + char + name.substr(-1, 1);
  } else {
      newStr = name;
  }
  return newStr;
}

export const isPersonName = (name) => {
    if (!name || (name + '').length < 2) {
        return false;
    }
    if (name.indexOf('*') > 0) {
        return true;
    }
    return RegExp(/^[\u4E00-\u9FFF·•]{2,20}$/).test(name);
}

export const correctName = (name) => {
    return name;
}

export const correctCertNo = (idCard) => {
    return idCard;
}

export const reviseName = (name) => {
    if (!name) return '';

    name = (name + '').replace(/\s/g, '');
    name = name.replace(/[.]/g,'·');
    name = name.replace(/[^\u3400-\u9FFF𤣩·•]/g, '');
    name = name.replace(/^[·•]+|[·•]+$/g,'');
    name = zhTran2Simp(name); // 繁体转简体
    const familyNames = {
        '弓长': '张',
        '彳佘': '徐',
        '冫马': '冯',
        '口十': '叶',
        '讦': '许',
        '𤣩': '王',
        '口玉': '国',
        '廴聿': '建',
        '冖车': '军',
        '⻗叚': '霞',
        '雨叚': '霞',
        '礻畐': '福',
        '冈刂': '刚',
        '王令': '玲',
        '𤣩令': '玲',
        '玪': '玲',
        '人玉': '金',
        '八玉': '金',
        '入玉': '金',
        '扌辰': '振',
    };
    for (let key of Object.keys(familyNames)) {
        if (name.startsWith(key)) {
            name = name.replace(key,familyNames[key]);
            break;
        }
    }

    name = name.replace(/[丨丶忄冫彳乚讠丿]/g, '');
    name = name.replace(/[亻⻖阝氵氺⻌宀冖纟乛亠疒匚⻊艹⺮攵扌刂]/g, '');
    name = name.replace(/^[一二三四五六七八九十]+月/g, '');
    name = name.replace(/(家人|父母|父|母|父亲|母亲|爸爸|妈妈|爸|妈|爱人|老伴|丈夫|老公|先生|妻子|老婆|媳妇|婆娘|太太|孩子|宝宝|儿女|儿子|大儿|小儿|女儿|闺女|姑娘)/g, '');
    // if (name.length > 3) {
    //     name = name.replace(/^.+(省|市|自治区|自治州|县|区|).*/g, '');
    // }
    name = name.replace(/^[一十丫了去在卅我你姓名]+/g, '');

    return name;
}

export const reviseIdCard = (idCard) => {
    if (!idCard) return '';

    idCard = (idCard + '').replace(/\s/g, '');
    idCard = idCard.toUpperCase();
    idCard = idCard.replace(/[✘×ⅩＸ✕╳❌乂㐅乄χ]/g, 'X');

    idCard = idCard.replace(/^。+/g, '');
    idCard = idCard.replace(/[O０Σ○〇。⊙❤]/g, '0');
    idCard = idCard.replace(/[Ⅰ丨|LI/／∫）]/g, '1');
    idCard = idCard.replace(/[Ⅱ‖]/g, '11');
    idCard = idCard.replace(/[Z工乙]/g, '2');
    idCard = idCard.replace(/[了]/g, '3');
    idCard = idCard.replace(/[Ψ屮]/g, '4');
    idCard = idCard.replace(/[GQ]/g, '9');
    idCard = idCard.replace(/[^\dX]/g, '');

    return idCard.slice(0, 18);
}

export const isAIChannel = (channel) => {
    return true;
    // if (!channel) return false;
    //
    // channel = channel + '';
    // if (channel.includes('25115') || channel.includes('20005') || channel.startsWith('252') || channel.startsWith('253') || channel.startsWith('290')) {
    //     return true;
    // }
    //
    // return false;
}

// 手机验证码
export function isVerCode(code) {
    if (!code) {
        return false;
    }
    return RegExp(/\d{4}$/).test(code);
}

//验证姓名是否正确
function checkName(name) {
    if (name == undefined || name == "" || name == null) {
        return "姓名不能为空";
    }

    var telReg = /^[\u4e00-\u9fa5 ]{2,20}$/;
    if (!telReg.test(name)) {
        return "姓名格式有误";
    }

    return ""
}

//验证身份证号是否正确
function isCardNo(identity) {
    if (identity == undefined || identity == "" || identity == null) {
        return false;
    }
    if (identity.indexOf('*') > 0 && identity.length == 18) {
        return true;
    }
    return checkCode(identity);
}

var checkCode = function (val) {
    var p = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
    var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
    var code = val.substring(17);
    if (p.test(val)) {
        var sum = 0;
        for (var i = 0; i < 17; i++) {
            sum += val[i] * factor[i];
        }
        if (parity[sum % 11] == code.toUpperCase()) {
            return true;
        }
    }
    return false;
}

//获取url中参数
function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}

//判断是不是安卓机
function isAndroid() {
    var u = navigator.userAgent;
    if (u.indexOf("Android") > -1 || u.indexOf("Linux") > -1) {
        return true;
    }
    return false;
}

export const isIos = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isIos = (ua.indexOf('iphone') != -1) || (ua.indexOf('ipad') != -1);
    return isIos;
}

//判断是不是在微信内
function isInWx() {
    var ua = navigator.userAgent;
    var isWeixin = !!/MicroMessenger/i.test(ua);
    if (isWeixin) {
        return true
    }
    return false
}

export const isInAliPay = () => {
    const ua = navigator.userAgent.toLowerCase();
    return !!/Alipay/i.test(ua);
}

// 判断是否为华为浏览器 || UC || 夸克
export function isHuaWeiBrowser() {
    const ua = navigator.userAgent;
    console.log('浏览器的UA =>', ua);
    const bool1 = ua.toLowerCase().indexOf('HuaweiBrowser'.toLowerCase()) >= 0;
    const bool2 = ua.toLowerCase().indexOf('UCBrowser'.toLowerCase()) >= 0;
    const bool3 = ua.toLowerCase().indexOf('Quark'.toLowerCase()) >= 0;
    return bool1 || bool2 || bool3;
}

//计算年龄
function GetAge(identityCard) {
    var len = (identityCard + "").length;
    if (len == 0) {
        return 0;
    } else {
        if ((len != 15) && (len != 18))//身份证号码只能为15位或18位其它不合法
        {
            return 0;
        }
    }
    var strBirthday = "";
    if (len == 18)//处理18位的身份证号码从号码中得到生日和性别代码
    {
        strBirthday = identityCard.substr(6, 4) + "/" + identityCard.substr(10, 2) + "/" + identityCard.substr(12, 2);
    }
    if (len == 15) {
        strBirthday = "19" + identityCard.substr(6, 2) + "/" + identityCard.substr(8, 2) + "/" + identityCard.substr(10, 2);
    }
    //时间字符串里，必须是“/”
    var birthDate = new Date(strBirthday);
    var nowDateTime = new Date();
    var age = nowDateTime.getFullYear() - birthDate.getFullYear();
    //再考虑月、天的因素;.getMonth()获取的是从0开始的，这里进行比较，不需要加1
    if (nowDateTime.getMonth() < birthDate.getMonth() || (nowDateTime.getMonth() == birthDate.getMonth() && nowDateTime.getDate() < birthDate.getDate())) {
        age--;
    }
    return age >= 0 ? age : 0;
}

/**
 * 按身份证号码获取性别
 * @idNumber 身份证号码
 * @return 男：male；女：female；异常（身份证号码为空或长度、格式错误）：undefined
 */
 function getGender(idNumber) {
  if (idNumber) {
      let genderCode; // 性别代码
      if (idNumber.length == 18) { // 二代身份证号码长度为18位（第17位为性别代码）
          genderCode = idNumber.charAt(16);
      } else if (idNumber.length == 15) { // 一代身份证号码长度为15位（第15位为性别代码）
          genderCode = idNumber.charAt(14);
      }
      if (genderCode && !isNaN(genderCode)) {
          // 两代身份证号码的性别代码都为男奇女偶
          if (parseInt(genderCode) % 2 == 0) {
              return '女';
          }
          return '男';
      }
  }
}


function generateUUID() {
    var d = new Date().getTime();
    if (window.performance && typeof window.performance.now === "function") {
        d += performance.now(); //use high-precision timer if available
    }
    var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = (d + Math.random() * 16) % 16 | 0;
        d = Math.floor(d / 16);
        return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
    });
    return uuid;
}

function tips() {

}

function confirm() {

}

const checkMail = (val) => {
    if (val == undefined || val == "" || val == null) {
        return "邮箱不能为空";
    }
    let emailReg = /^[a-zA-Z0-9_-]+@([a-zA-Z0-9]+\.)+(com|cn|net|org)$/;
    if (!emailReg.test(val)) {
        return "邮箱格式有误";
    }
}

const isEmail = (val) => {
    if (val == undefined || val == "" || val == null) {
        return false;
    }
    let emailReg = /^[a-zA-Z0-9_-]+@([a-zA-Z0-9]+\.)+(com|cn|net|org)$/;
    return emailReg.test(val)
}

const thousandFormat = (s,n) => {
    n = n > 0 && n <= 20 ? n : 2;
    s = parseFloat((s + "").replace(/[^\d\.-]/g, "")).toFixed(n) + "";
    var l = s.split(".")[0].split("").reverse(), r = s.split(".")[1];
    let t = "";
    for (let i = 0; i < l.length; i++) {
        t += l[i] + ((i + 1) % 3 == 0 && (i + 1) != l.length ? "," : "");
    }
    return t.split("").reverse().join("");
}

export const number2Thousandth = (s, n) => {
    n = n || 0;
    s = parseFloat((s + "").replace(/[^\d.-]/g, "")).toFixed(n) + "";
    const splits = s.split('.');
    const integer = splits[0].split("").reverse().reduce((prev, next, idx) => {
        return ((idx % 3) ? next : (next + ',')) + prev;
    });
    const decimal = splits[1] ? `.${splits[1]}` : '';

    return `${integer}${decimal}`
}

export const base64_encode = (data) => {
    if (!data) {
        return "";
    }

    // 加密
    return window.btoa(unescape(encodeURIComponent(data)));
};

export const base64_decode = (data) => {
    if (!data) {
        return "";
    }

    // 解密
    return decodeURIComponent(escape(window.atob(data)));
};

export const url_safe_b64_encode = (data) => {
    if (!data) {
        return "";
    }

    const result = base64_encode(data);
    const safe_result = result.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
    return safe_result;
};

export const url_safe_b64_decode = (data) => {
    if (!data) {
        return "";
    }

    data = (data + '').replace(/-/g, '+').replace(/_/g, '/');
    return base64_decode(data);
};

//页面埋点的key
const TraceLogInfoKeys = {
    'n_bike': 'N_BIKE', //太平洋非机动车险
    'xue_ping': 'TPY_XPX', //太平洋学平险-分期
    'xue_ping_year': 'TPY_XPX_YEAR', //太平洋学平险-年缴
    'health_classic': 'health_classic', //泰康旧版百万医疗-大病保
    'health_classic_new': 'HEALTH_CLASSIC_NEW', //泰康新版百万医疗- 泰爱保
    'tk_health_send': 'TK_HEALTH_SEND', //泰康新版百万医疗赠险
    'za_pet': 'ZA_PET', //众安
    'yg_ht_health_classic': 'YG_HT_HEALTH_CLASSIC', //华泰百万医疗
    'tk_million_medical_enjoy': 'tk_million_medical_enjoy', //泰康在线尊享版

    'ejb_tk_normal': 'EJB_TK_NORMAL', // E家保险顾问赠险-9.9
    'ejb_tk_blue_normal': 'EJB_TK_BLUE_NORMAL', // E家保险顾问赠险(蓝色)-9.9
    'ejb_tk_back': 'EJB_TK_BACK', // E家保险顾问赠险-0.99
    'ejb_tk_blue_back': 'EJB_TK_BLUE_BACK', // E家保险顾问赠险(蓝色)-0.99
    'ejb_normal': 'EJB_NORMAL', // E家保险顾问正常-9.9
    'ejb_back': 'EJB_BACK', // E家保险顾问正常-0.99

    'yqb_tk_normal': 'YQB_TK_NORMAL', // 易启保保险顾问赠险-9.9
    'yqb_tk_back': 'YQB_TK_BACK', // 易启保保险顾问赠险-0.99
    'yqb_tk_high_normal': 'YQB_TK_HIGH_NORMAL', // 易启保保险顾问赠险-19.9
    'yqb_tk_high_back': 'YQB_TK_HIGH_BACK', // 易启保保险顾问赠险-19.9-0.99
    'yqb_normal': 'YQB_NORMAL', // 易启保保险顾问正常-9.9
    'yqb_back': 'YQB_BACK', // 易启保保险顾问正常-0.99
    'jla_free_normal': 'JLA_FREE_NORMAL', // 金联安保险顾问正常-0

    'yg_ht_send_health': 'yg_ht_send_health', // 华泰赠险
    'pa_health': 'pa_health', // 平安百万医疗
    'tpy_hn_health': 'tpy_hn_health',// 太平洋百万医疗
    'su_hui': 'su_hui', // 苏惠保
    'tk_traffic_send': 'tk_traffic_send', // 泰康在线瑞鹏交通意外险(赠险)

    'iyb_tpy_health': 'iyb_tpy_health', //I云保-太平洋-百万医疗
    'iyb_tpy_send': 'iyb_tpy_send', //I云保-太平洋-赠险
    'iyb_za_health':'iyb_za_health',// I云保-众安-百万医疗

    'za_health': 'za_health',//众安百万医疗
    'ks_tk_traffic_send':'ks_tk_traffic_send', // 凯森泰康交通赠险
    'nw_medical_health':'nw_medical_health',// 暖哇众安百万医疗 - 2元版
    'nw_za_health':'nw_za_health',// 暖哇众安百万医疗 - 1元版
    'nw_health_send':'nw_health_send',// 暖哇众安赠险
    'nw_traffic_send':'nw_traffic_send',// 暖哇众安交通意外赠险
    'nw_covid_send':'nw_covid_send',    // 暖哇众安防疫赠险
    'nw_tp_send': 'nw_tp_send', // 暖哇太平赠险
    'nw_tp_za_jh_send': 'nw_tp_za_jh_send', // 暖哇众安和太平聚合

    'iyb_pa_health':'iyb_pa_health',// I云保-平安百万

    'iyb_pa_disease_send':'iyb_pa_disease_send', //平安E生平安疾病守护金
    'iyb_pa_traffic_send':'iyb_pa_traffic_send', //平安个人交通工具意外险
    'iyb_gr_disease_send':'iyb_gr_disease_send', //保通国任重疾赠险
    'iyb_tb_family_property_send':'iyb_tb_family_property_send', // 保通太平洋家财赠险
    'iyb_pa_qjf_jt_send':'iyb_pa_qjf_jt_send', // 保通平安全家福交通赠险

    'iyb_rb_send': 'iyb_rb_send',   //I云保人保赠险
    'iyb_rb_health': 'iyb_rb_health',   //I云保人保百万
    'iyb_rb_smooth_health':'iyb_rb_smooth_health',//I云保人保百万平滑分期
    'iyb_zh_health':'iyb_zh_health',//I云保中华百万平滑分期
    'iyb_zy_tk_send':'iyb_zy_tk_send', // I云保泰康重疾赠险
    'iyb_pa_money_disease_send':'iyb_pa_money_disease_send', // 平安产险重大疾病保险（白金版）
    'iyb_pa_age_traffic_send':'iyb_pa_age_traffic_send', // 平安产险交通工具意外伤害保险
    'iyb_pa_health_high_average_health':'iyb_pa_health_high_average_health', // 平安关爱百万医疗-均分

    'nw_xd_health': 'nw_xd_health',   // 暖哇现代百万

    'bg_za_send': 'bg_za_send',   // 白鸽赠险-周周领

    'xb_pa_health':'xb_pa_health', // 星贝平安百万医疗
    'xb_pa_disease_send':'xb_pa_disease_send', // 星贝平安疾病守护金赠险
    'xb_pa_traffic_send':'xb_pa_traffic_send', // 星贝平安交通意外赠险

    'nw_za_smooth_health':'nw_za_smooth_health',  //众安平滑产品
    'nw_za_average_health':'nw_za_average_health', // 众安尊享e生均分
    'nw_yt_health':'nw_yt_health',  //亚太百万医疗
    'nw_za_fa_health':'nw_za_fa_health', // 众安防癌险
    'nw_za_clinic': 'nw_za_clinic', // 众安门诊险
    'nw_za_clinic_send': 'nw_za_clinic_send', // 众安门诊险(新)
    'iyb_0_consult': 'iyb_0_consult',   //保险顾问0元版本

    'iyb_xd_health': 'iyb_xd_health',   //I云保现代百万

    'iyb_za_send':'iyb_za_send', //I云保众安赠险
    'iyb_tk_disease_send': 'iyb_tk_disease_send', // 保通泰康千元重疾
    'iyb_as_health':'iyb_as_health', // I云保安盛百万

    'nw_gr_health':'nw_gr_health', // 国任百万医疗-任小康
    'nw_gr_tp_jh_send': 'nw_gr_tp_jh_send', // 暖哇国任赠险
    'nw_za_fl_send':'nw_za_fl_send', // 暖哇众安赠险+百万

    'mh_free_consult':'mh_free_consult', // 美化0元1V1
    'mh_9.9_consult':'mh_9.9_consult', // 美化1.99元1V1

    'iyb_jd_health_send':'iyb_jd_health_send', // I云保京东健康
    'bt_insurance_consult':'bt_insurance_consult', // 保通微信客服课堂

    'nw_za_fl_free_health':'nw_za_fl_free_health', // I云保京东健康
    'nw_clinic_send' : 'nw_clinic_send', // 暖哇众安门诊赠险
    'nw_za_emergency' : 'nw_za_emergency', // 暖哇众安门急诊险

    'tk_cube_base' : 'tk_cube_base', // 泰康百万医疗
    'tk_default_cube_base':'tk_default_cube_base', // 泰康百万医疗默认升级

    'iyb_zy_tk_cube_base': 'iyb_zy_tk_cube_base', // 泰康2022百万医疗险普惠版
    'iyb_zy_tk_cube_upgrade': 'iyb_zy_tk_cube_upgrade', // 泰康2022百万医疗险升级版

    'iyb_zy_tk_resign_cube_base': 'iyb_zy_tk_resign_cube_base', // 保通泰康住院保双签
    'iyb_tk_new_final_a_disease_cube_base':'iyb_tk_new_final_a_disease_cube_base', // 保通参数-泰康终版A魔方
    'iyb_tk_new_final_a_disease_default_cube_base':'iyb_tk_new_final_a_disease_default_cube_base',
    'iyb_tk_medical_health_cube_base':'iyb_tk_medical_health_cube_base', // 泰医享·全民医疗险
    'iyb_tk_medical_health_default_cube_base':'iyb_tk_medical_health_default_cube_base', // 泰医享·全民医疗险 前置
    'iyb_tk_xxl_medical_health_cube_base':'iyb_tk_xxl_medical_health_cube_base', // 泰医享·全民医疗险-信息流
    'iyb_tk_xxl_medical_health_default_cube_base':'iyb_tk_xxl_medical_health_default_cube_base', // 泰医享·全民医疗险-信息流 前置
    'iyb_tk_cn_medical_health_cube_base':'iyb_tk_cn_medical_health_cube_base', // 保通泰康泰超能全民医疗险-信息流
    'iyb_tk_cn_medical_health_default_cube_base':'iyb_tk_cn_medical_health_default_cube_base', // 保通泰康泰超能全民医疗险-信息流
    'iyb_tk_cn_medical_health_low_cube_base':'iyb_tk_cn_medical_health_low_cube_base',
    'iyb_tk_cn_medical_health_low_default_cube_base':'iyb_tk_cn_medical_health_low_default_cube_base',
    'iyb_tk_cn_medical_health_high_cube_base':'iyb_tk_cn_medical_health_high_cube_base',
    'iyb_tk_cn_medical_health_high_default_cube_base':'iyb_tk_cn_medical_health_high_default_cube_base',
    'iyb_tk_372_cn_medical_health_cube_base':'iyb_tk_372_cn_medical_health_cube_base',
    'iyb_tk_372_cn_medical_health_default_cube_base':'iyb_tk_372_cn_medical_health_default_cube_base',
    'iyb_tk_391_cn_medical_health_cube_base':'iyb_tk_391_cn_medical_health_cube_base',
    'iyb_tk_391_cn_medical_health_default_cube_base':'iyb_tk_391_cn_medical_health_default_cube_base',
    'iyb_tk_372_cn_medical_high_cube_base':'iyb_tk_372_cn_medical_high_cube_base',
    'iyb_tk_372_cn_medical_high_default_cube_base':'iyb_tk_372_cn_medical_high_default_cube_base',
    'iyb_tk_391_cn_medical_high_cube_base':'iyb_tk_391_cn_medical_high_cube_base',
    'iyb_tk_391_cn_medical_high_default_cube_base':'iyb_tk_391_cn_medical_high_default_cube_base',
    'iyb_tk_04_699_cn_medical_cube_base':'iyb_tk_04_699_cn_medical_cube_base',
    'iyb_tk_04_699_cn_medical_default_cube_base':'iyb_tk_04_699_cn_medical_default_cube_base',
    'iyb_tk_04_700_cn_medical_cube_base':'iyb_tk_04_700_cn_medical_cube_base',
    'iyb_tk_04_700_cn_medical_default_cube_base':'iyb_tk_04_700_cn_medical_default_cube_base',
    'iyb_tk_09_699_cn_medical_cube_base':'iyb_tk_09_699_cn_medical_cube_base',
    'iyb_tk_09_699_cn_medical_default_cube_base':'iyb_tk_09_699_cn_medical_default_cube_base',
    'iyb_tk_09_700_cn_medical_cube_base':'iyb_tk_09_700_cn_medical_cube_base',
    'iyb_tk_09_700_cn_medical_default_cube_base':'iyb_tk_09_700_cn_medical_default_cube_base',
    'iyb_tk_cn_bw_medical_green_cube_base':'iyb_tk_cn_bw_medical_green_cube_base',
    'iyb_tk_cn_bw_medical_green_default_cube_base':'iyb_tk_cn_bw_medical_green_default_cube_base',
    'jd_tk_cn_bw_medical_green_cube_base':'jd_tk_cn_bw_medical_green_cube_base',
    'jd_tk_cn_bw_medical_green_default_cube_base':'jd_tk_cn_bw_medical_green_default_cube_base',
    'iyb_tk_rr_cn_by_green_jd_cube_base':'iyb_tk_rr_cn_by_green_jd_cube_base',
    'iyb_tk_rr_cn_by_green_jd_default_cube_base':'iyb_tk_rr_cn_by_green_jd_default_cube_base',
    'iyb_tk_sl_rr_cn_by_jd_cube_base':'iyb_tk_sl_rr_cn_by_jd_cube_base',
    'iyb_tk_sl_rr_cn_by_jd_default_cube_base':'iyb_tk_sl_rr_cn_by_jd_default_cube_base',
    'iyb_tk_rr_cn_by_jd_cube_base':'iyb_tk_rr_cn_by_jd_cube_base',
    'iyb_tk_rr_cn_by_jd_default_cube_base':'iyb_tk_rr_cn_by_jd_default_cube_base',
    'iyb_tk_tab_disease_jd_cube_base':'iyb_tk_tab_disease_jd_cube_base',
    'iyb_tk_tab_disease_jd_default_cube_base':'iyb_tk_tab_disease_jd_default_cube_base',
    'iyb_tk_cn_bw_medical_year_average_health':'iyb_tk_cn_bw_medical_year_average_health',  // 保通泰超能百万医疗年缴均分

    'iyb_gr_bw_health_cube_base':'iyb_gr_bw_health_cube_base', // 保通国任百万魔方
    'iyb_gr_bw_health_default_cube_base':'iyb_gr_bw_health_default_cube_base',

    'iyb_gr_bw_health_high_cube_base':'iyb_gr_bw_health_high_cube_base', // 保通国任百万魔方 优化
    'iyb_gr_bw_health_high_cube_upgrade':'iyb_gr_bw_health_high_cube_upgrade',
    'iyb_gr_bw_health_high_default_cube_base':'iyb_gr_bw_health_high_default_cube_base',
    'iyb_gr_bw_health_high_default_cube_upgrade':'iyb_gr_bw_health_high_default_cube_upgrade',

    'iyb_gr_health_disease_cube_base':'iyb_gr_health_disease_cube_base', // 保通国任重疾魔方
    'iyb_gr_health_disease_default_cube_base':'iyb_gr_health_disease_default_cube_base',
    'iyb_gr_disease_low_jd_cube_base':'iyb_gr_disease_low_jd_cube_base',
    'iyb_gr_disease_low_jd_default_cube_base':'iyb_gr_disease_low_jd_default_cube_base',
    'iyb_tb_tpy_zh_year_average_health':'iyb_tb_tpy_zh_year_average_health', // 保通太平洋
    'iyb_tb_tpy_zh_average_health':'iyb_tb_tpy_zh_average_health',

    'tc_tk_cube_base' : 'tc_tk_cube_base', // 天彩泰康百万医疗
    'tc_tk_default_cube_base':'tc_tk_default_cube_base', // 天彩泰康百万医疗默认升级
    'tc_tk_disease_cube_base':'tc_tk_disease_cube_base', // 天彩泰康重疾魔方
    'tc_tk_disease_default_cube_base':'tc_tk_disease_default_cube_base', // 天彩泰康重疾魔方-前置升级
    'tc_tk_new_disease_cube_base':'tc_tk_new_disease_cube_base', // 天彩泰康重疾魔方新费率
    'tc_tk_new_disease_default_cube_base':'tc_tk_new_disease_default_cube_base', // 天彩泰康重疾魔方新费率-前置升级
    'tc_tk_tmp_disease_cube_base':'tc_tk_tmp_disease_cube_base', // 天彩泰康重疾魔方-临时
    'tc_tk_tmp_disease_default_cube_base':'tc_tk_tmp_disease_default_cube_base', // 天彩泰康重疾魔方-临时-前置升级
    'tc_tk_final_disease_cube_base':'tc_tk_final_disease_cube_base', // 天彩泰康重疾魔方A+B
    'tc_tk_final_disease_default_cube_base':'tc_tk_final_disease_default_cube_base', // 天彩泰康重疾魔方-前置升级A+B
    'tc_tk_final_a_disease_cube_base':'tc_tk_final_a_disease_cube_base', // 天彩泰康重疾魔方A
    'tc_tk_final_a_disease_default_cube_base':'tc_tk_final_a_disease_default_cube_base', // 天彩泰康重疾魔方-前置升级A
    'tc_tk_medical_health_cube_base':'tc_tk_medical_health_cube_base',
    'tc_tk_medical_health_default_cube_base':'tc_tk_medical_health_default_cube_base',
    'tc_tk_cn_medical_health_cube_base':'tc_tk_cn_medical_health_cube_base', // 天彩泰康泰超能医疗险
    'tc_tk_cn_medical_health_default_cube_base':'tc_tk_cn_medical_health_default_cube_base',
    'tc_tk_cn_medical_health_low_cube_base':'tc_tk_cn_medical_health_low_cube_base',
    'tc_tk_cn_medical_health_low_default_cube_base':'tc_tk_cn_medical_health_low_default_cube_base',
    'tc_tk_cn_medical_health_high_cube_base':'tc_tk_cn_medical_health_high_cube_base',
    'tc_tk_cn_medical_health_high_default_cube_base':'tc_tk_cn_medical_health_high_default_cube_base',

    'tc_tk_smooth_cube_base': 'tc_tk_smooth_cube_base', // 天彩泰康平滑魔方基础款
    'tc_tk_smooth_cube_upgrade': 'tc_tk_smooth_cube_upgrade', // 天彩泰康平滑魔方升级款
    'tc_tk_resign_cube_base': 'tc_tk_resign_cube_base', // 天彩泰康重签约魔方基础款
    'tc_tk_resign_cube_upgrade': 'tc_tk_resign_cube_upgrade', // 天彩泰康重签约魔方升级款

    'tc_tk_disease_smooth_cube_base': 'tc_tk_disease_smooth_cube_base', // 天彩泰康平滑重疾魔方基础款
    'tc_tk_disease_smooth_cube_upgrade': 'tc_tk_disease_smooth_cube_upgrade', // 天彩泰康平滑重疾魔方升级款
    'tc_tk_disease_resign_cube_base': 'tc_tk_disease_resign_cube_base', // 天彩泰康重签约重疾魔方基础款
    'tc_tk_disease_resign_cube_upgrade': 'tc_tk_disease_resign_cube_upgrade', // 天彩泰康重签约重疾魔方升级款

    'nw_cube_base':'nw_cube_base', // 暖哇众安魔方百万医疗
    'nw_default_za_cube_base':'nw_default_za_cube_base', // 暖哇众安魔方静默升级
    'iyb_xd_cube_base':'iyb_xd_cube_base', // 保通现代百万医疗

    'tk_free_send':'tk_free_send', // 泰康赠险
    'tc_tk_free_send':'tc_tk_free_send', // 天彩泰康赠险

    'za_account_safe_send':'za_account_safe_send', // 众安联运账户安全赠险
    'yb_za_covid_send':'yb_za_covid_send', // 众安联运新冠赠险
    'yb_za_traffic_send':'yb_za_traffic_send', // 众安联运交通意外赠险
    'yb_za_health_send':'yb_za_health_send', // 众安联运重疾赠险
    'yb_gr_send':'yb_gr_send', // 优保国任赠险
    'yb_tp_traffic_send': 'yb_tp_traffic_send', // 优保太平赠险
    'za_cube_base':'za_cube_base', // 众安联运魔方
    'za_low_cube_base':'za_low_cube_base', // 优保众安低价魔方
    'za_low_default_cube_base':'za_low_default_cube_base', // 优保众安低价魔方-前置
    'za_zx_cube_base':'za_zx_cube_base', // 优保尊享
    'za_zx_default_cube_base':'za_zx_default_cube_base', // 优保尊享-前置
    'za_disease_cube_base': 'za_disease_cube_base', // 优保 众安康 重疾魔方
    'za_disease_cube_upgrade' : 'za_disease_cube_upgrade', // 优保 众安康 重疾魔方 升级
    'za_bw_cube_base' : 'za_bw_cube_base', // 优保百万医疗魔方
    'za_bw_default_cube_base':'za_bw_default_cube_base', // 优保百万医疗魔方-前置
    'za_bw_cube_upgrade' : 'za_bw_cube_upgrade',
    'yb_za_zx_cube_base':'yb_za_zx_cube_base', // 优保众安尊享23魔方
    'yb_za_zx_default_cube_base':'yb_za_zx_default_cube_base',
    'yb_hn_traffic_send' : 'yb_hn_traffic_send', // 优保华农交通赠险
    'yb_hn_disease_send' :'yb_hn_disease_send', // 优保华农重疾赠险
    'yb_zh_traffic_send' : 'yb_zh_traffic_send', // 优保众惠交通赠险
    'yb_zh_disease_send' :'yb_zh_disease_send', // 优保众惠重疾赠险
    'yb_zh_cube_base' : 'yb_zh_cube_base', // 优保众惠随心百万
    'yb_zh_default_cube_base': 'yb_zh_default_cube_base', // 优保众惠随心百万
    'yb_zh_jd_cube_base':'yb_zh_jd_cube_base', // 优保众惠京东魔方
    'yb_zh_jd_default_cube_base':'yb_zh_jd_default_cube_base', // 优保众惠京东魔方-前置
    'yb_hn_cube_base':'yb_hn_cube_base', // 优保华农魔方
    'yb_hn_default_cube_base':'yb_hn_default_cube_base', // 优保华农魔方-前置
    'yb_gr_cube_base':'yb_gr_cube_base', // 优保国任魔方
    'yb_gr_default_cube_base':'yb_gr_default_cube_base', // 优保国任魔方-前置
    'yb_gr_jd_cube_base':'yb_gr_jd_cube_base',
    'yb_gr_jd_default_cube_base':'yb_gr_jd_default_cube_base',
    'yb_gr_jd_new_cube_base':'yb_gr_jd_new_cube_base',
    'yb_gr_jd_new_default_cube_base':'yb_gr_jd_new_default_cube_base',
    'yb_gr_jdzk_cube_base':'yb_gr_jdzk_cube_base',
    'yb_gr_jdzk_default_cube_base':'yb_gr_jdzk_default_cube_base',
    'yb_gr_by_low_jd_cube_base':'yb_gr_by_low_jd_cube_base',
    'yb_gr_by_low_jd_default_cube_base':'yb_gr_by_low_jd_default_cube_base',
    'yb_gr_disease_cube_base':'yb_gr_disease_cube_base',
    'yb_gr_disease_default_cube_base':'yb_gr_disease_default_cube_base',
    'yb_gr_disease_jd_cube_base':'yb_gr_disease_jd_cube_base',
    'yb_gr_disease_jd_default_cube_base':'yb_gr_disease_jd_default_cube_base',
    "yb_zs_traffic_accident_send":"yb_zs_traffic_accident_send", // 优保交通聚合赠险
    "yb_ks_gr_send":"yb_ks_gr_send", // 优保凯森聚合赠险
    "yb_hr_send":"yb_hr_send", // 优保凯森聚合赠险
    "yb_gr_accident_send":"yb_gr_accident_send", // 优保国任意外赠险
    "cqb_zh_traffic_send":"cqb_zh_traffic_send", // 优保众惠聚合赠险
    'yb_jc_axb_jd_cube_base':'yb_jc_axb_jd_cube_base', // 优保众安家财险 基础款
    'yb_jc_axb_jd_default_cube_base':'yb_jc_axb_jd_default_cube_base', // 优保家财安心保京东魔方基础款（默认升级）
    'yb_jc_axb_jd_cube_upgrade':'yb_jc_axb_jd_cube_upgrade', // 优保家财安心保京东魔方升级款
    'yb_jc_axb_jd_default_cube_upgrade':'yb_jc_axb_jd_default_cube_upgrade', // 优保家财安心保京东魔方升级款（默认升级）

    'nw_disease_cube_base': 'nw_disease_cube_base', // 暖哇 众安康 重疾险魔方
    'nw_disease_default_cube_base':'nw_disease_default_cube_base',// 暖哇 众安康 重疾险魔方-前置
    'nw_disease_cube_upgrade': 'nw_disease_cube_upgrade',
    'nw_disease_wx_cube_base':'nw_disease_wx_cube_base',
    'nw_disease_wx_default_cube_base':'nw_disease_wx_default_cube_base',
    'nw_disease_jd_cube_base':'nw_disease_jd_cube_base',
    'nw_disease_jd_default_cube_base':'nw_disease_jd_default_cube_base',
    'iyb_za_cube_base':'iyb_za_cube_base', // 保通众安魔方
    'iyb_za_low_cube_base':'iyb_za_low_cube_base', // 保通低价魔方
    'iyb_za_bs_low_cube_base': 'iyb_za_bs_low_cube_base', // 保通低价魔方
    'iyb_za_high_cube_base':'iyb_za_high_cube_base', // 保通高龄魔方
    'iyb_za_hospital_average_health':'iyb_za_hospital_average_health', // 保通众安住院保2022
    'iyb_tp_rxy_send':'iyb_tp_rxy_send', // 保通太平交通意外赠险
    'iyb_pa_appt_send':'iyb_pa_appt_send', // 保通平安百万随行赠险-预约赠险
    'nw_za_low_cube_base':'nw_za_low_cube_base', // 暖哇众安魔方2022
    'nw_default_za_low_cube_base':'nw_default_za_low_cube_base', // 暖哇众安魔方2022（默认）
    'slt_product_order':'slt_product_order', // 华润三九
    'iyb_tk_plus_send':'iyb_tk_plus_send', // 保通泰康飞铁保
    'nw_za_hospital_average_health':'nw_za_hospital_average_health',// 暖哇众安住院保
    'nw_za_exclusive_average_health':'nw_za_exclusive_average_health', // 暖哇尊享24均分
    'nw_za_fa_average_health': 'nw_za_fa_average_health', // 孝欣保中老年防癌险2022
    'nw_zc_cube_base':'nw_zc_cube_base',// 暖哇众诚魔方
    'nw_default_zc_cube_base':'nw_default_zc_cube_base', // 暖哇众诚魔方默认升级
    'nw_zc_new_low_cube_base':'nw_zc_new_low_cube_base',// 暖哇众诚魔方-新
    'nw_default_zc_new_low_cube_base':'nw_default_zc_new_low_cube_base', // 暖哇众诚魔方默认升级-新
    'nw_zc_low_cube_base':'nw_zc_low_cube_base',// 暖哇众诚低价魔方
    'nw_default_zc_low_cube_base':'nw_default_zc_low_cube_base', // 暖哇众诚低价魔方默认升级
    'nw_zy_cube_base':'nw_zy_cube_base',// 暖哇中银魔方
    'nw_zy_cube_default_base':'nw_zy_default_cube_base',// 暖哇中银魔方
    'nw_zx_health': 'nw_zx_health', // 暖哇尊享2022均分
    'nw_rt_cube_base': 'nw_rt_cube_base', // 暖哇融通百万医疗
    'nw_rt_default_cube_base': 'nw_rt_default_cube_base', // 暖哇融通百万医疗(默认升级)
    'nw_gr_cube_base':'nw_gr_cube_base', // 暖哇国任魔方
    'nw_gr_default_cube_base':'nw_gr_default_cube_base', // 暖哇国任魔方-前置升级
    'nw_gr_2024_ph_cube_base':'nw_gr_2024_ph_cube_base', // 暖哇国任2024普惠版
    'nw_gr_2024_ph_default_cube_base':'nw_gr_2024_ph_default_cube_base', // 暖哇国任2024普惠版
    'nw_za_zx_default_cube_base': 'nw_za_zx_default_cube_base', // 暖哇众安尊享e生2023魔方-前置升级
    'nw_za_zx_cube_base': 'nw_za_zx_cube_base', // 暖哇众安尊享e生2023魔方
    'nw_za_average_cube_upgrade': 'nw_za_average_cube_upgrade', // 暖哇众安尊享轻享均分
    'nw_za_zx_bw_average_health':'nw_za_zx_bw_average_health', // 尊享百万医疗卓越版
    'nw_za_zx_new_cube_base':'nw_za_zx_new_cube_base',
    'nw_za_zx_new_default_cube_base':'nw_za_zx_new_default_cube_base',
    'nw_za_bw_health_wy_cube_base':'nw_za_bw_health_wy_cube_base',
    'nw_za_bw_health_wy_default_cube_base':'nw_za_bw_health_wy_default_cube_base',
    'nw_za_bw_health_hm_cube_base': 'nw_za_bw_health_hm_cube_base', // 暖哇众安百万2024惠民无忧版
    'nw_za_bw_health_hm_default_cube_base' : 'nw_za_bw_health_hm_default_cube_base',// 暖哇众安百万2024惠民无忧版
    'nw_za_bw_health_hm_wx_cube_base':'nw_za_bw_health_hm_wx_cube_base', // 暖哇众安百万医疗险2024惠民版 魔方
    'nw_za_by_hm_high_jd_cube_base':'nw_za_by_hm_high_jd_cube_base',
    'nw_za_by_hm_high_jd_default_cube_base':'nw_za_by_hm_high_jd_default_cube_base',
    'nw_za_bw_health_hm_wx_default_cube_base':'nw_za_bw_health_hm_wx_default_cube_base',
    'nw_za_bw_health_hm_jd_cube_base':'nw_za_bw_health_hm_jd_cube_base',
    'nw_za_bw_health_hm_jd_default_cube_base':'nw_za_bw_health_hm_jd_default_cube_base',
    'nw_za_by_hm_new_high_jd_cube_base':'nw_za_by_hm_new_high_jd_cube_base', // 暖哇众安百医惠民版新高价京东魔方基础款
    'nw_za_by_hm_new_high_jd_cube_upgrade':'nw_za_by_hm_new_high_jd_cube_upgrade', // 暖哇众安百医惠民版新高价京东魔方升级款
    'nw_za_by_hm_new_high_jd_default_cube_base':'nw_za_by_hm_new_high_jd_default_cube_base', // 暖哇众安百医惠民版新高价京东魔方基础款（默认升级）
    'nw_za_by_hm_new_high_jd_default_cube_upgrade':'nw_za_by_hm_new_high_jd_default_cube_upgrade', // 暖哇众安百医惠民版新高价京东魔方升级款（默认升级）
    'nw_za_by_hm_jd_fb_cube_base':'nw_za_by_hm_jd_fb_cube_base',
    'nw_za_by_hm_jd_fb_default_cube_base':'nw_za_by_hm_jd_fb_default_cube_base',
    'nw_za_by_hm_jd_new_fb_cube_base':'nw_za_by_hm_jd_new_fb_cube_base',
    'nw_za_by_hm_jd_new_fb_default_cube_base':'nw_za_by_hm_jd_new_fb_default_cube_base',
    'nw_za_disease_new_cube_base':'nw_za_disease_new_cube_base',
    'nw_za_disease_new_default_cube_base':'nw_za_disease_new_default_cube_base',
    'nw_zak_disease_high_cube_base':'nw_zak_disease_high_cube_base', // 暖哇众安康重疾高价魔方
    'nw_zak_disease_high_default_cube_base':'nw_zak_disease_high_default_cube_base',
    'nw_za_exclusive_wx_average_health':'nw_za_exclusive_wx_average_health', // 暖哇众安百万医疗险2024惠民版 均分
    'nw_za_bw_health_ph_cube_base': 'nw_za_bw_health_ph_cube_base', // 暖哇新众安百万2024魔方基础款
    'nw_za_bw_health_ph_cube_upgrade' : 'nw_za_bw_health_ph_cube_upgrade',// 暖哇新众安百万2024魔方升级款
    'nw_za_bw_health_ph_default_cube_base' : 'nw_za_bw_health_ph_default_cube_base', // 暖哇新众安百万2024魔方基础款（默认升级）
    'nw_za_bw_health_ph_default_cube_upgrade' : 'nw_za_bw_health_ph_default_cube_upgrade', // 暖哇新众安百万2024魔方升级款（默认升级）
    'nw_za_bw_health_hm_high_wx_average_health': 'nw_za_bw_health_hm_high_wx_average_health', // 暖哇新众安百万2024 均分
    'nw_za_by_hm_high_jd_average_health':'nw_za_by_hm_high_jd_average_health',
    'nw_tk_qn_health_cube_base':'nw_tk_qn_health_cube_base',
    'nw_tk_qn_health_default_cube_base':'nw_tk_qn_health_default_cube_base',
    'nw_tk_qn_health_low_cube_base':'nw_tk_qn_health_low_cube_base',
    'nw_tk_qn_health_low_default_cube_base':'nw_tk_qn_health_low_default_cube_base',
    'nw_tk_qn_health_low_jd_cube_base':'nw_tk_qn_health_low_jd_cube_base',
    'nw_tk_qn_health_low_jd_default_cube_base':'nw_tk_qn_health_low_jd_default_cube_base',
    'nw_tk_qn_health_low_new_jd_cube_base':'nw_tk_qn_health_low_new_jd_cube_base',
    'nw_tk_qn_health_low_new_jd_default_cube_base':'nw_tk_qn_health_low_new_jd_default_cube_base',
    'nw_tk_ty_prevent_cancer_cube_base':'nw_tk_ty_prevent_cancer_cube_base',
    'nw_tk_ty_prevent_cancer_default_cube_base':'nw_tk_ty_prevent_cancer_default_cube_base',
    'nw_tk_disease_cube_base': 'nw_tk_disease_cube_base', // 暖哇泰康重疾险魔方基础款
    'nw_tk_disease_cube_upgrade': 'nw_tk_disease_cube_upgrade', // 暖哇泰康重疾险魔方升级款
    'nw_tk_disease_default_cube_base': 'nw_tk_disease_default_cube_base', // 暖哇泰康重疾险魔方基础款（默认升级）
    'nw_tk_disease_default_cube_upgrade': 'nw_tk_disease_default_cube_upgrade', // 暖哇泰康重疾险魔方升级款（默认升级）
    'nw_zl_property_cube_base':'nw_zl_property_cube_base',
    'nw_zl_property_default_cube_base':'nw_zl_property_default_cube_base',
    'nw_dd_bw_medical_inclusive_cube_base':'nw_dd_bw_medical_inclusive_cube_base',
    'nw_dd_bw_medical_inclusive_default_cube_base':'nw_dd_bw_medical_inclusive_default_cube_base',
    'nw_tp_zax_send':'nw_tp_zax_send', // 太平众安心百万医疗
    'nw_new_tp_zax_jh_send':'nw_new_tp_zax_jh_send', // 太平聚合赠险 （众安心 + 任逍遥）
    'nw_tp_jh_006_send':'nw_tp_jh_006_send', // 太平聚合赠险 （众安心 + 任逍遥 + 众安重疾）
    'nw_tb_family_property_send':'nw_tb_family_property_send', // 暖哇太保家财赠险
    'nw_tb_family_property_year_average_health':'nw_tb_family_property_year_average_health', // 暖哇太保家财险（年缴均分）

    'iyb_pa_cube_base':'iyb_pa_cube_base', // 平安魔方
    'iyb_pa_default_cube_base':'iyb_pa_default_cube_base', // 平安魔方-默认升级
    'iyb_pa_disease_cube_base':'iyb_pa_disease_cube_base', // 平安重疾魔方
    'iyb_pa_disease_default_cube_base':'iyb_pa_disease_default_cube_base', // 平安重疾魔方-默认升级
    'iyb_pa_health_cube_base':'iyb_pa_health_cube_base', // 平安关爱魔方
    'iyb_pa_health_default_cube_base':'iyb_pa_health_default_cube_base', // 平安关爱魔方-前置升级
    'bbj_consult': 'bbj_consult', // 百保君保险顾问
    'iyb_pa_average_health':'iyb_pa_average_health', // 平安均分
    'iyb_tk_jb_free_send':'iyb_tk_jb_free_send', // 骏伯
    'iyb_pa_medicine_card_send':'iyb_pa_medicine_card_send', // 保通平安药诊卡赠险
    'iyb_pa_medicine_card_average_health':'iyb_pa_medicine_card_average_health', // 保通平安药诊卡（均分）-月缴
    'iyb_pa_medicine_card_year_average_health':'iyb_pa_medicine_card_year_average_health', // 保通平安药诊卡（均分）-年缴
    'iyb_pa_medicine_card_high_year_average_health':'iyb_pa_medicine_card_high_year_average_health', // 保通平安药诊卡高价（均分）- 年缴
    'iyb_zy_tk_health_disease_cube_base':'iyb_zy_tk_health_disease_cube_base',
    'iyb_zy_tk_health_disease_default_cube_base':'iyb_zy_tk_health_disease_default_cube_base', // 保通泰康健重疾魔方
    'iyb_zy_tk_disease_high_ex_cube_base':'iyb_zy_tk_disease_high_ex_cube_base',
    'iyb_zy_tk_disease_high_ex_default_cube_base':'iyb_zy_tk_disease_high_ex_default_cube_base', // 保通泰康健重疾高价魔方
    'iyb_zy_tk_disease_high_jd_cube_base':'iyb_zy_tk_disease_high_jd_cube_base',
    'iyb_zy_tk_disease_high_jd_default_cube_base':'iyb_zy_tk_disease_high_jd_default_cube_base', // 保通泰康健重疾高价魔方-京东
    'iyb_zy_tk_prevent_cancer_ex_cube_base':'iyb_zy_tk_prevent_cancer_ex_cube_base',
    'iyb_zy_tk_prevent_cancer_ex_default_cube_base':'iyb_zy_tk_prevent_cancer_ex_default_cube_base',
    'iyb_zy_tk_prevent_cancer_cube_base':'iyb_zy_tk_prevent_cancer_cube_base', // 保通泰康防癌险
    'iyb_zy_tk_prevent_cancer_default_cube_base':'iyb_zy_tk_prevent_cancer_default_cube_base',
    'iyb_zy_tk_prevent_cancer_low_cube_base':'iyb_zy_tk_prevent_cancer_low_cube_base', // 保通泰康防癌险
    'iyb_zy_tk_prevent_cancer_low_default_cube_base':'iyb_zy_tk_prevent_cancer_low_default_cube_base',
    'iyb_zy_tk_prevent_cancer_jd_cube_base':'iyb_zy_tk_prevent_cancer_jd_cube_base',
    'iyb_zy_tk_prevent_cancer_jd_default_cube_base':'iyb_zy_tk_prevent_cancer_jd_default_cube_base', // 保通泰康防癌险-京东
    'iyb_zy_tk_bw_health_cube_base':'iyb_zy_tk_bw_health_cube_base',
    'iyb_zy_tk_bw_health_default_cube_base':'iyb_zy_tk_bw_health_default_cube_base', // 保通泰康百万医疗魔方
    "iyb_zy_tk_bw_health_high_cube_base":"iyb_zy_tk_bw_health_high_cube_base", // 保通泰康2024百万医疗险优享版
    "iyb_zy_tk_bw_health_high_default_cube_base":"iyb_zy_tk_bw_health_high_default_cube_base", // 保通泰康2024百万医疗险优享版
    "iyb_zy_tk_new_bw_health_cube_base":"iyb_zy_tk_new_bw_health_cube_base", // 保通泰康2024新百万医疗险优享版
    "iyb_zy_tk_new_bw_health_default_cube_base":"iyb_zy_tk_new_bw_health_default_cube_base", // 保通泰康2024新百万医疗险优享版
    "iyb_zy_tk_bw_health_jd_cube_base":"iyb_zy_tk_bw_health_jd_cube_base", // 保通泰康2024新百万医疗险优享版-京东支付
    "iyb_zy_tk_bw_health_jd_default_cube_base":"iyb_zy_tk_bw_health_jd_default_cube_base", // 保通泰康2024新百万医疗险优享版-京东支付
    "iyb_zy_tk_life_ftb_send":"iyb_zy_tk_life_ftb_send", // i云保泰康寿飞铁保赠险

    // 泰康经纪
    'tk_jj_free_send':'tk_jj_free_send',
    'tk_jj_medical_health_cube_base':'tk_jj_medical_health_cube_base',
    'tk_jj_medical_health_default_cube_base':'tk_jj_medical_health_default_cube_base',
    'tk_jj_cn_medical_health_cube_base':'tk_jj_cn_medical_health_cube_base', // 泰康经纪参数-泰超能
    'tk_jj_cn_medical_health_default_cube_base':'tk_jj_cn_medical_health_default_cube_base',
    'tk_jj_cn_medical_health_low_cube_base':'tk_jj_cn_medical_health_low_cube_base',
    'tk_jj_cn_medical_health_low_default_cube_base':'tk_jj_cn_medical_health_low_default_cube_base',
    'tk_jj_cn_medical_health_high_cube_base':'tk_jj_cn_medical_health_high_cube_base',
    'tk_jj_cn_medical_health_high_default_cube_base':'tk_jj_cn_medical_health_high_default_cube_base',

    'rxh_gr_disease_send' : 'rxh_gr_disease_send', // 优保安心重疾险（福利版）
    'rxh_zh_traffic_send' : 'rxh_zh_traffic_send', // 众安优保·交通出行全能保(福利版)
    'rxh_zh_disease_send' : 'rxh_zh_disease_send', // 众安优保·重疾无忧保(福利版)
    'rxh_hn_traffic_send' : 'rxh_hn_traffic_send', // 华农畅行交通意外险（福利版）
    'rxh_hn_disease_send' : 'rxh_hn_disease_send', // 华农畅享重疾险（福利版）

    'ry_flow_tx': 'ry_flow_tx', // 重庆联通接口页面
    'qn_flow_mg': 'qn_flow_mg', // 芒果tv接口页面
    'sms_reply_dbh_smrs':'sms_reply_dbh_smrs', // 懂保会  生命人寿
    'lr_medicine_card':'lr_medicine_card', // 联仁药诊卡
    'lr_fy_card':'lr_fy_card', // 联仁防疫卡
    'lt_flow_card':'lt_flow_card',// 联通流量卡
    'bhhw_flow_card':'bhhw_flow_card',// 桂花卡
    'bawang_flow_card':'bawang_flow_card',
    'ty_sx_membership_card':'ty_sx_membership_card', // 天翼高清

    // 科大保通泰康产品
    'iyb_tk_cube_base' : 'iyb_tk_cube_base', // 泰康百万医疗
    'iyb_tk_default_cube_base':'iyb_tk_default_cube_base', // 泰康百万医疗默认升级
    'iyb_tk_disease_cube_base': 'iyb_tk_disease_cube_base', // 泰康重疾魔方
    'iyb_tk_disease_default_cube_base': 'iyb_tk_disease_default_cube_base', // 泰康重疾魔方默认升级
    'iyb_tk_new_disease_cube_base' : 'iyb_tk_new_disease_cube_base',
    'iyb_tk_new_disease_default_cube_base': 'iyb_tk_new_disease_default_cube_base',
    'iyb_tk_tmp_disease_cube_base': 'iyb_tk_tmp_disease_cube_base', // 临时款泰康重疾
    'iyb_tk_tmp_disease_default_cube_base': 'iyb_tk_tmp_disease_default_cube_base',
    'iyb_tk_final_disease_cube_base': 'iyb_tk_final_disease_cube_base', // 泰康终版重疾A+B
    'iyb_tk_final_disease_default_cube_base': 'iyb_tk_final_disease_default_cube_base',
    'iyb_tk_final_a_disease_cube_base': 'iyb_tk_final_a_disease_cube_base', // 泰康终版重疾 纯A
    'iyb_tk_final_a_disease_default_cube_base': 'iyb_tk_final_a_disease_default_cube_base',
    'iyb_tk_free_send':'iyb_tk_free_send', // 泰康赠险
    'gddx_flow_card':'gddx_flow_card', // 广东电信号卡
    'cslt_flow_card':'cslt_flow_card', // 长沙联通号卡
    'car_sms_clue':'car_sms_clue', // 普短汽车

    'az_tkj_qn_by_jd_cube_base':'az_tkj_qn_by_jd_cube_base', // 艾泽泰康百医 生产 基础款
    'az_tkj_qn_by_jd_default_cube_base':'az_tkj_qn_by_jd_default_cube_base', // 艾泽泰康百医 生产 基础款 默认升级
    'az_tkj_qn_by_bk_jd_cube_base':'az_tkj_qn_by_bk_jd_cube_base',
    'az_tkj_qn_by_bk_jd_default_cube_base':'az_tkj_qn_by_bk_jd_default_cube_base',
    'az_tkj_bw_prevent_cancer_jd_cube_base':'az_tkj_bw_prevent_cancer_jd_cube_base', // 艾泽泰康防癌 基础款
    'az_tkj_bw_prevent_cancer_jd_default_cube_base':'az_tkj_bw_prevent_cancer_jd_default_cube_base', // 艾泽泰康防癌 基础款 默认升级
    'az_tkj_prevent_cancer_bk_jd_cube_base':'az_tkj_prevent_cancer_bk_jd_cube_base',
    'az_tkj_prevent_cancer_bk_jd_default_cube_base':'az_tkj_prevent_cancer_bk_jd_default_cube_base',
    'az_tkj_new_prevent_cancer_jd_cube_base':'az_tkj_new_prevent_cancer_jd_cube_base',
    'az_tkj_new_prevent_cancer_jd_default_cube_base':'az_tkj_new_prevent_cancer_jd_default_cube_base',

    'yx_tk_bw_jd_cube_base':'yx_tk_bw_jd_cube_base',
    'yx_tk_bw_jd_default_cube_base':'yx_tk_bw_jd_default_cube_base',
    'yx_tk_disease_send':'yx_tk_disease_send',
}

// const Url = "http://admin.mp.wwz114.cn/market/Delivery";
// const Url = "http://admin.mp.wwz114.cn/market";
const Url = baseUrl;
const domainUrl = 'http://cdn.bountech.com/marketfront/file/insurance'

export {
    tips,
    confirm,
    isCardNo,
    isPhoneNum,
    checkTel,
    isNull,
    getQueryString,
    isAndroid,
    Url,
    domainUrl,
    checkName,
    GetAge,
    getGender,
    checkMail,
    isInWx,
    TraceLogInfoKeys,
    generateUUID,
    thousandFormat,
    isEmail
}
