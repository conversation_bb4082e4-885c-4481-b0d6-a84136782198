let baseUrl: string = "";   //这里是一个默认的url，可以没有
switch (process.env.VUE_APP_TITLE) {
    case 'development':
        baseUrl = "http://admin.mp.wwz114.cn/market";  //这里是本地的请求url
        break
    case 'alpha':   // 注意这里的名字要和步骤二中设置的环境名字对应起来
        baseUrl = "http://admin.mp.wwz114.cn/market";  //这里是测试环境中的url
        break
    case 'production':
        baseUrl = "http://lm.app.x1618.cn/market";   //生产环境url
        break
    default:
        baseUrl = "http://admin.mp.wwz114.cn/market";  //这里是本地的请求url
        break
}

export default baseUrl;