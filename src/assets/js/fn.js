export const checkPhoneNumber = (phone) => {
    if (!phone) {
        return false;
    }

    const pattern = /^1[3-9]\d{9}$/;

    return pattern.test(phone);
}

export const removeSpaceCharacter = (str) => {
    if (!str) {
        return '';
    }
    const pattern = /\s/g;

    return str.replace(pattern);
}

export const queryURLParams = (url) => {
    if (!url) return {};

    const pattern = /([^?&]+)=([^?&]+)/ig;
    const params = {};
    url.replace(pattern, ($, $1, $2) => {
        params[$1] = $2;
    });
    return params;
}

export const typeOfValue = (o) => {
    return Object.prototype.toString.call(o).slice(8, -1).toLowerCase();
}

export const isWeixin = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isWeixin = ua.indexOf('micromessenger') != -1;
    return isWeixin;
}

export const isAndroid = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isAndroid = ua.indexOf('android') != -1;
    return isAndroid;
}

export const isIos = () => {
    const ua = navigator.userAgent.toLowerCase();
    const isIos = (ua.indexOf('iphone') != -1) || (ua.indexOf('ipad') != -1);
    return isIos;
}

export const checkUrl = url => {
    const reg = /^(((ht|f)tps?):\/\/)?([^!@#$%^&*?.\s-]([^!@#$%^&*?.\s]{0,63}[^!@#$%^&*?.\s])?\.)+[a-z]{2,6}\/?/
    return reg.test(url)
}

export const checkName = name => {
    const regCnName = /^(?:[\u4e00-\u9fa5·]{2,16})$/
    const regEnName = /(^[a-zA-Z][a-zA-Z\s]{0,20}[a-zA-Z]$)/
    return regCnName.test(name) || regEnName.test(name)
}

export const injectDebugTools = () => {
    if (window.eruda) {
        return window.eruda.init();
    }

    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/eruda';
    script.onload = () => {
        window.eruda && window.eruda.init();
    };

    script.onerror = () => {
        console.log('eruda加载失败');
    };

    document.body.append(script);
}

export const fetchUserIpAddress = () => {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', 'https://api.ipify.org', true);
        xhr.onload = () => {
            resolve(xhr.response);
        }
        xhr.onerror = () => {
            reject();
        }
        xhr.send();
    });
}

export const fetchGeoFromIp = (ip) => {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', `https://www.cz88.net/api/cz88/ip/geo?ip=${ip}`, true);
        xhr.responseType = 'json';
        xhr.onload = () => {
            resolve(xhr.response);
        }
        xhr.onerror = () => {
            reject();
        }
        xhr.send();
    });
}
