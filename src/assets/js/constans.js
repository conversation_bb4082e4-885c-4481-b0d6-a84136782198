"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var baseUrl = ""; //这里是一个默认的url，可以没有
switch (process.env.VUE_APP_TITLE) {
    case 'development':
        baseUrl = "http://test.bountech.com/market"; //这里是本地的请求url
        break;
    case 'alpha':// 注意这里的名字要和步骤二中设置的环境名字对应起来
        baseUrl = window.location.protocol + "//" + window.location.host + "/market"; //这里是测试环境中的url
        // baseUrl = "http://prod.bountech.com/marketprod"; //这里是本地的请求url
        break;
    case 'test':// 注意这里的名字要和步骤二中设置的环境名字对应起来
        baseUrl = window.location.protocol + "//" + window.location.host + "/market"; //这里是测试环境中的url
        break;
    case 'production':
        baseUrl = window.location.protocol + "//" + window.location.host + "/marketprod"; //生产环境url
        break;
    case 'prod':
        baseUrl = window.location.protocol + "//" + window.location.host + "/marketprod"; //生产环境url
        break;
    case 'yhprod':
        baseUrl = window.location.protocol + "//" + window.location.host + "/marketprod"; //生产环境url
        break;
    case 'kdprod':
        baseUrl = window.location.protocol + "//" + window.location.host + "/mktprod/request/marketprod"; //生产环境url
        break;
    case 'iybprod':
        baseUrl = window.location.protocol + "//" + window.location.host + "/mktprod/request/marketprod"; //生产环境url
        // baseUrl = "https://cdns.bountech.com/marketprod"; //生产环境url
        break;
    default:
        baseUrl = "http://uat.bountech.com/market"; //这里是本地的请求url
        // baseUrl = "http://prod.bountech.com/marketprod"; //这里是本地的请求url
        // baseUrl = "https://cdns.bountech.com/marketprod"; //生产环境url
        // baseUrl = "https://channel.rrbxw.cn/mktprod/request/marketprod"; //生产环境url
        // baseUrl = "https://channel.zhelibao.com/mktprod/request/marketprod"; //生产环境url
        // baseUrl = "https://zhelaibao.sknii.cn/marketprod"; //生产环境url
        break;
}
exports.baseUrl = baseUrl;
