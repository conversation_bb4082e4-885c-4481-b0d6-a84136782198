import request from "../../utils/request";

async function buryingPoint(page,channel,phone) {
    //记录页面点击
    var res = (await request({
        url: "/Delivery/add",
        method: "POST",
        headers:{version:'1.0.0_web_BXCS'},
        data: {
            channel: channel,
            mobileId: phone,
            page: page
        }
    })).data;
}

async function buryingPoint1(page,channel,phone,infoKey,time,longitude,latitude,position) {
    //记录页面点击
    var res = (await request({
        url: "/Insurance/trace/v2/log",
        method: "POST",
        headers:{version:'1.0.0_web_BXCS'},
        data: {
            channel: channel,
            mobileId: phone,
            page: page,
            infoKey: infoKey || '',
            time: time,
            longitude: longitude,
            latitude: latitude,
            position: position,
        }
    })).data;

    return res
}

async function actionTracking(data) {
    // 进行H5双向语音通话时，埋点参数需要UUID，这个参数来自通话前获取空闲账号的接口，用于标识这一通电话
    data.uuid = window.h5CallEnabled ? (window.h5CallId || null) : undefined;
    //记录页面点击
    var res = (await request({
        url: "/Insurance/trace/v2/log",
        method: "POST",
        headers:{version:'1.0.0_web_BXCS'},
        data: data,
    })).data;

    return res
}

export {
    buryingPoint,
    buryingPoint1,
    actionTracking
}
