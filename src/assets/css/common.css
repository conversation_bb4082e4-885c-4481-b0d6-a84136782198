* {
    -webkit-tap-highlight-color: transparent;
    outline: none 0;
    margin: 0;
    padding: 0
}
body {
    -webkit-overflow-scrolling: touch;
    overflow-scrolling: touch;
    line-height: 1.1;
    background: #F5F5F5;
}

body,html {
    position: relative;
    font-family: "Helvetica Neue",Helvetica,Arial,PingFangSC-Regular,"Hiragino Sans GB","Heiti SC","Microsoft Yahei","WenQuanYi Micro Hei",sans-serif;
    width: 100%;
    height: 100%;
    margin: auto;
}
a {
    text-decoration: none;
}
/*提示框样式*/
.overlayer{
    position:fixed;
    left:0;
    top:0;
    width:100%;
    height:100%;
    z-index:998;
    background: rgba(0,0,0,.5);
    opacity: 1;
    overflow: auto;
    display: none;
}
.overlayer .modal {
    position: absolute;
    background-color: #fff;
    top: 30%;
    left: 50%;
    transform: translate(-50%,0);
    width: 2.6rem;
    min-height: 0.6rem;
    z-index: 1000;
    text-align: center;
    border-radius: 8px;
    padding-bottom: 0.34rem;
}
.overlayer .modal_contents {
    height: 100%;
    background-color: #fff;
    border-radius: 6px;
    display: flex;
}
.overlayer .modal .contents {
    font-family: PingFangSC-Semibold;
    font-size: 0.14rem;
    color: #333333;
    letter-spacing: -0.45px;
    line-height: 0.22rem;
    width: 2rem;
    display: inline-block;
    margin: 0.3rem auto;
    vertical-align: top;
    font-weight: 500;
}
.overlayer .bottom_btns {
    height: 0.4rem;
    line-height: 0.4rem;
    border: 0;
    text-align: center;
    font-size: 0.14rem;
    margin-top: 0.15rem;
    position: fixed;
    bottom: 0;
    border-top: 1px solid #ddd;
    width: 100%;
}
.overlayer .bottom_btns .btn_get  {
    color: #fff;
    display: inline-block;
    width: 100%;
    height: 0.4rem;
    border-radius: 3px;
    color: #333;
}

/*协议弹出框*/
.overlayer_agreement{
    position:fixed;
    top:0;
    width:100%;
    height:100%;
    z-index:999;
    background: rgba(0,0,0,.5);
    opacity: 1;
    overflow: auto;
    max-width: 750px;
    margin: 0 auto;
    display: none;
    left: 50%;
    transform: translate(-50%,0);
}
.overlayer_agreement .modal {
    position: absolute;
    background-color: #fff;
    top: 3%;
    left: 50%;
    transform: translate(-50%,0);
    width: 95%;
    height: 85%;
    z-index: 1000;
    text-align: center;
    border-radius: 3px;
    padding-bottom: 0.5rem;
}
.overlayer_agreement .modal_contents {
    height: 100%;
    background-color: #fff;
    border-radius: 6px;
}
.overlayer_agreement .modal .contents {
    font-family: PingFangSC-Semibold;
    font-size: 0.14rem;
    color: #333333;
    letter-spacing: -0.45px;
    line-height: 0.22rem;
    width: 2rem;
    display: inline-block;
    margin: 0.2rem auto;
    vertical-align: top;
    font-weight: 500;
}
.overlayer_agreement .title {
    font-size: 0.18rem;
    line-height: 0.35rem;
    color: red;
    font-weight: 400;
}
.overlayer_agreement textarea {
    padding: 0.1rem 0.1rem 0.05rem;
    box-sizing: border-box;
    width: 100%;
    height: calc(100% - 0.5rem);
    line-height: 0.22rem;
    border: 0;
    color: #65686E;
    font-family: Arial,MicrosoftYaHei,'微软雅黑',Arial,Tahoma,Helvetica,Georgia,Sans-serif,STXihei,'华文黑体',Hei,'Hiragino Kaku Gothic Pro',SimSun;
}
.overlayer_agreement .bottom_btns {
    height: 0.5rem;
    border: 0;
    text-align: center;
    font-size: 0.14rem;
    margin-top: 0.15rem;
    position: fixed;
    bottom: 0;
    width: 100%;
}
.overlayer_agreement .bottom_btns .btn_get  {
    color: #fff;
    display: inline-block;
    width: 30%;
    height: 0.34rem;
    line-height: 0.34rem;
    border-radius: 3px;
    background-color: #fe4c00;
    color: #fff;
    /*margin-top: 0.08rem;*/
}

/*须知弹出框*/
.overlayer_notice{
    position:fixed;
    top:0;
    width:100%;
    height:100%;
    z-index:999;
    background: rgba(0,0,0,.5);
    opacity: 1;
    overflow: auto;
    max-width: 750px;
    margin: 0 auto;
    display: none;
    left: 50%;
    transform: translate(-50%,0);
}
.overlayer_notice .modal {
    position: absolute;
    background-color: #fff;
    top: 3%;
    left: 50%;
    transform: translate(-50%,0);
    width: 95%;
    height: 85%;
    z-index: 1000;
    text-align: center;
    border-radius: 3px;
    padding-bottom: 0.6rem;
}
.overlayer_notice .modal_contents {
    height: 100%;
    background-color: #fff;
    border-radius: 6px;
}
.overlayer_notice .modal .contents {
    font-family: PingFangSC-Semibold;
    font-size: 0.14rem;
    color: #333333;
    letter-spacing: -0.45px;
    line-height: 0.22rem;
    width: 2rem;
    display: inline-block;
    margin: 0.2rem auto;
    vertical-align: top;
    font-weight: 500;
}
.overlayer_notice .title {
    font-size: 0.18rem;
    line-height: 0.35rem;
    color: red;
}
.overlayer_notice textarea {
    padding: 0.1rem 0.1rem 0.05rem;
    box-sizing: border-box;
    width: 100%;
    height: calc(100% - 0.45rem);
    line-height: 0.22rem;
    border: 0;
    color: #65686E;
    font-family: Arial,MicrosoftYaHei,'微软雅黑',Arial,Tahoma,Helvetica,Georgia,Sans-serif,STXihei,'华文黑体',Hei,'Hiragino Kaku Gothic Pro',SimSun;
}
.overlayer_notice .bottom_btns {
    height: 0.5rem;
    border: 0;
    text-align: center;
    font-size: 0.14rem;
    margin-top: 0.15rem;
    position: fixed;
    bottom: 0;
    width: 100%;
}
.overlayer_notice .bottom_btns .btn_get  {
    color: #fff;
    display: inline-block;
    width: 30%;
    height: 0.34rem;
    line-height: 0.34rem;
    border-radius: 3px;
    background-color: #fe4c00;
    color: #fff;
}