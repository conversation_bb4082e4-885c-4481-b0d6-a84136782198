<template>
    <van-popup v-model="visible" class="container_2211031700" position="bottom" round @close="closeAction">
        <div class="header">权益保障提示</div>
        <div class="body">
            <div class="body-feature">•理赔无忧&nbsp;&nbsp;&nbsp;•客服协助&nbsp;&nbsp;&nbsp;•风险提示</div>
            <div class="body-content">
                您已进入投保流程，请仔细阅读免责条款、投保须知、<span class="body-content-policy"
                @click="onViewDetail('客户告知书')">客户告知书</span>等信息，为维护您的合法权益，您的操作轨迹将被记录。
            </div>
        </div>
        <div class="footer">
            <div class="button" @click="closeAction">
                知道了
            </div>
        </div>
    </van-popup>
</template>

<script>

export default {
    name: "HHWarnHint", // 重要提醒
    props: { obj: Object },
    data() {
        return {
            timerId: null,
            visible: false,
        };
    },
    mounted() {
        setTimeout(() => {
            this.visible = this.obj.hintVisible;
        }, 500);

        this.timerId = setTimeout(() => {
            this.visible = false;
        }, 3500);
    },
    beforeDestroy() {
        this.timerId && clearTimeout(this.timerId);
    },
    methods: {
        closeAction() {
            this.visible = false;
            this.obj.hintVisible = false;
        },
        onViewDetail(v) {
            this.$emit('view', v);
        }
    },
};

</script>

<style lang="less" scoped type="text/less">
.container_2211031700 {
    margin: 0 auto;
    width: 3.75rem;
    left: 0;
    right: 0;

    display: flex;
    flex-direction: column;

    .header {
        padding: 0.15rem 0;
        color: #333333;
        font-size: 0.18rem;
        font-weight: 700;
        text-align: center;
    }

    .body {
        padding: 0.15rem 0.15rem 0.2rem;

        .body-feature {
            color: #999999;
            font-size: 0.12rem;
            line-height: 0.18rem;
        }

        .body-content {
            margin-top: 0.1rem;
            color: #474e66;
            font-size: 0.15rem;
            line-height: 1.6;

            .body-content-policy {
                font-weight: 500;
                color: #ff4509;
            }
        }
    }

    .footer {
        background-color: #ffffff;
        border-top: #f2f2f2 1px solid;

        .button {
            margin: 0.1rem 0.25rem;
            height: 0.45rem;
            line-height: 0.45rem;
            border-radius: 0.23rem;
            font-size: 0.16rem;
            font-weight: 500;
            text-align: center;
            color: #ffffff;
            background: linear-gradient(to right, rgba(255, 20, 28, 0.65), rgba(255, 0, 0, 0.8));
        }
    }
}
</style>
