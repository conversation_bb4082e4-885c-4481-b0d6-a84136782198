<template>
    <Popup v-model="onVisible" position="bottom" class="popup">
        <div class="header" @click="onClose">{{ pageName }}</div>
        <div class="content" id="popup-page">
            <Policy_Health @seePolicy="seePolicy"></Policy_Health>
        </div>
        <div class="footer">
            <div class="button" @click="clickNo">
                部分为是
            </div>
            <div class="button color" @click="clickYes">
                以上全否
            </div>
        </div>
    </Popup>
</template>

<script>
import {Popup} from "mint-ui";
import Policy_Health from "./Policy/Policy_Health";
import {msgToast} from "../function";

export default {
    name: "HealthPopup",
    components: {
        Policy_Health,
        Popup,
    },
    props: {
        type: String
    },
    data() {
        return {
            pageName: '健康告知',
            onVisible: false,
        }
    },
    methods: {
        seePolicy(policy) {
            this.$emit('seePolicy', policy);
        },
        onShow(page, policy) {
            // console.log('打开页面onShow =>', page);
            this.onVisible = true;
            setTimeout(this.setTop, 0);
        },
        clickNo() {
            this.onVisible = false;
            msgToast('很抱歉，您暂时不符合本产品的投保要求')
            this.$emit('clickYes', false);
        },
        clickYes() {
            this.onVisible = false;
            this.$emit('clickYes', true);
        },
        onClose() {
            this.onVisible = false;
        },
        setTop() {
            document.getElementById('popup-page').scrollTop = 0
        }
    },
}
</script>

<style scoped type="text/less" lang="less">
.popup {
    width: 100%;
    height: 80%;

    .header {
        height: 0.5rem;
        line-height: 0.5rem;
        color: #333333;
        font-size: 0.17rem;
        font-weight: 700;
        text-align: center;

        //&::after {
        //    content: "";
        //    position: absolute;
        //    top: 0.17rem;
        //    right: 0.15rem;
        //    height: 0.16rem;
        //    width: 0.16rem;
        //    background: url("../../../../../assets/imgs/common/sprites.png") no-repeat -1.12rem 0;
        //    background-size: 1.6rem 3.2rem;
        //}
    }

    .content {
        overflow: auto;
        height: calc(100% - 1.1rem);
    }

    .footer {
        position: fixed;
        bottom: 0;
        width: 100%;
        background-color: #FFFFFF;
        border-top: #F2F2F2 1px solid;

        display: flex;
        justify-content: space-around;

        .button {
            margin: 0.08rem 0.2rem;
            height: 0.44rem;
            width: 1.50rem;
            line-height: 0.44rem;
            border-radius: 0.22rem;
            font-size: 0.16rem;
            font-weight: 500;
            text-align: center;
            color: #333333;
            border: #EEEEEE 1px solid;
            background-color: #FFFFFF;
        }

        .color {
            color: #FFFFFF;
            background: linear-gradient(to right, #FFCC00, #FF9500);
        }
    }
}

</style>

