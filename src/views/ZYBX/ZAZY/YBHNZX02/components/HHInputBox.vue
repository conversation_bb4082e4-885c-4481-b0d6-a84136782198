<template>
	<div id="input_x_2302131855" class="container_2302131855">
		<div class="header">
			<div class="header-top">
				{{ title.text1 }}
				<span class="header-top-color">&nbsp;&nbsp;{{ title.text2 }}</span>
			</div>
			<p v-if="isStep2" class="header-mark">投、被保人与领取人需为同一人</p>
		</div>
		<div v-if="isStep2">
            <div class="x-item">
				<span class="x-item-label">手机号码</span>
				<input :value="maskedPhone" class="x-item-input" placeholder="您的信息将被严格保密" readonly>
			</div>
			<div class="x-item">
				<span class="x-item-label">真实姓名</span>
				<input v-model="obj.name1" class="x-item-input" maxlength="30" placeholder="信息保密，仅用于生成保单" type="text" @focus="onFocus('certName',$event.target.value)" @input="onInput('certName',$event.target.value)">
			</div>
			<div class="x-item">
				<span class="x-item-label">身份证号</span>
				<input v-model="obj.idCard1" class="x-item-input" maxlength="18" placeholder="信息保密，仅用于生成保单" type="text" @focus="onFocus('certNo',$event.target.value)" @input="onInput('certNo',$event.target.value)">
			</div>
		</div>
		<div v-else>
			<div class="x-item">
				<span class="x-item-label">手机号码</span>
				<input v-model="obj.phoneNo" class="x-item-input" maxlength="11" onkeyup="value=value.replace(/\D/g,'')" placeholder="您的信息将被严格保密" type="tel" @focus="onFocus('phone',$event.target.value)" @input="onInput('phone',$event.target.value)">
			</div>
		</div>
		<div id="id_action_button" class="submit-button" @click="submit">
			免费投保
			<img alt="hand" class="hand" src="@/assets/imgs/common/icon_hand2.png">
		</div>
	</div>
</template>

<script>

import { isPhoneNum, star_marked_phone } from "@/assets/js/common";

export default {
	name: "HHInputBox",
	props: { obj: Object },
	computed: {
		isStep2() {
			return this.obj.step == 'step2';
		},
		title() {
			if (this.isStep2) {
				return { text1: '最后一步', text2: '完善本人信息' };
			}
			return { text1: '输入手机号', text2: '免费投保' };
		},
		maskedPhone() {
			const { phoneNo, starPhone } = this.obj;
			return star_marked_phone(isPhoneNum(phoneNo) ? phoneNo : starPhone);
		}
	},
	methods: {
		submit() {
			this.$emit('submit');
		},
		onInput(key, value) {
			this.$emit('input', { key, value });
		},
		onFocus(key, value) {
			this.$emit('focus', { key, value });
		},
        onViewPolicy(page) {
            this.$emit('onViewPolicy', page);
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302131855 {
		position: relative;
		margin: 0 0.15rem;
		padding: 1px 0;
		color: #333333;
		font-size: 0.16rem;
		border-radius: 0.15rem;
		background-color: #ffffff;
		box-shadow: 0 1px 10px 0 rgba(200, 133, 85, 0.24);
        margin-top: -0.12rem;

		&::before {
			position: absolute;
			content: " ";
			width: 0.5rem;
			height: 1.15rem;
			left: -0.15rem;
			top: 50%;
			transform: translateY(-50%);
			// background: url("~@/assets/imgs/common/hand_left.png") no-repeat center;
			background-size: 100%;
		}

		&::after {
			position: absolute;
			content: " ";
			width: 0.5rem;
			height: 1.15rem;
			right: -0.15rem;
			top: 50%;
			transform: translateY(-50%);
			// background: url("~@/assets/imgs/common/hand_right.png") no-repeat center;
			background-size: 100%;
		}

		.header {
			padding: 0.2rem 0 0.15rem;

			.header-mark {
				margin-top: 0.05rem;
				color: #737680;
				font-size: 0.12rem;
				text-align: center;
			}

			.header-top {
				display: flex;
				align-items: center;
				justify-content: center;

				font-size: 0.18rem;
				font-weight: bold;

				&::before,
				&::after {
					content: " ";
					width: 0.55rem;
					height: 0.12rem;
				}

				&::before {
					margin: -0.02rem 0.12rem 0 0;
					background: url("~@/assets/imgs/common/icon_needle_left.png")
						no-repeat center;
					background-size: 100%;
				}

				&::after {
					margin: -0.02rem 0 0 0.12rem;
					background: url("~@/assets/imgs/common/icon_needle_right.png")
						no-repeat center;
					background-size: 100%;
				}

				.header-top-color {
					color: #ff3300;
				}
			}
		}

		.x-item {
			margin: 0 0.15rem 0.12rem;
			display: flex;
			align-items: center;
			border-radius: 0.12rem;
			border: 1px solid #FFB05D;
			background-color: #FFF5E9;

			.x-item-label,
			.x-item-input {
				height: 0.5rem;
				line-height: 0.5rem;
			}

			.x-item-label {
				width: 1rem;
				text-align: center;
				font-size: 0.15rem;
				font-weight: 500;
                color: #262626;
			}

			.x-item-input {
				flex: 1;
				border: 0;
				outline: none;
				width: 2rem;
				font-size: 0.16rem;
				background-color: unset;
			}
		}

		.submit-button {
			position: relative;
			margin: 0.2rem 0.25rem 0.15rem;
			padding: 0.15rem 0;
			color: #ffffff;
			font-size: 0.26rem;
			font-weight: 700;
			text-align: center;
			border-radius: 0.28rem;
			box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
			background: linear-gradient( 90deg, #FD7A45 0%, #FF112E 100%);
			animation: button_animation 1.5s linear infinite;

			.hand {
				position: absolute;
				top: 0.15rem;
				left: 75%;
				width: 18%;
				animation: hand_animation 1.25s linear infinite;
			}
		}

        .policy-x {
			margin: 0.15rem;
			color: #262626;
			text-align: justify;
			font-size: 0.12rem;
			line-height: 1.6;

			.policy-icon {
				color:  #2E5BE3;
				font-size: 0.16rem;
				vertical-align: -0.01rem;
			}

			.policy-txt {
				color: #2E5BE3;
				font-weight: 500;
			}

			.policy-note {
				color: #ffffffcc;
			}
		}
	}
</style>
