<template>
    <div class="container">
        <div class="content">
            <div class="item" v-for="policy in policyList" :key="policy.page" @click="seePolicy(policy)">
                {{ policy.page }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "PolicyList",
    props: {list: Array},
    data() {
        return {

        }
    },
    computed: {
        policyList() {
            return this.list;
        }
    },
    methods: {
        seePolicy(policy) {
            this.$emit('seePolicy', policy);
        },
    },
}
</script>

<style scoped type="text/less" lang="less">

.container {

    .content {

        .item {
            padding: 0.15rem;
            line-height: 0.20rem;
            box-sizing: border-box;
            border-bottom: #EEEEEE 1px solid;
        }
    }
}

</style>

