import moment from "moment";
import CryptoJS from "crypto-js";
import {Indicator, Toast} from "mint-ui";
import {isCardNo, isMaskedAndT1Phone, isPersonName, isPhoneNum, TraceLogInfoKeys, isInWx, isInAliPay, GetAge, url_safe_b64_encode,} from "@/assets/js/common";
import {PremiumRate, PremiumRate1} from "./src";
import {actionTracking} from "@/assets/js/api";
import {bxStorage,} from "@/utils/store_util";
import {
    fetchCreateOrderForZA,
    upgradeZANewCrossSellProduct,
    createPromotionOrder
} from "@/api/insurance-api";

export const createOrderInfo = (action = '') => {
    return {
        isChecked: false,
        hintVisible: true,

        source: action, // 页面来源
        action: action, //  back: '返回',promotion: '促活跳转',follow: '结果跳转',direct: '直发',forward: '转发',result: '结果页',promote: '促活'

        productKey: TraceLogInfoKeys.yb_gr_jdzk_cube_base,
        identifier: '',

        relation: 1,    // 为谁投保 1:本人；2:配偶；3:儿女；4:父母；
        insurance: 1,   // 有无社保
        repay: 1,       // 缴费方式
        step: 1,        // 信息填写步骤
        channel: '1',
        m: '',          // 链接自带的加密手机号
        phoneCode: '',  //验证码
        hasCode: false, // 是否包含手机验证码
        tel: '',        // 操作人手机号
        mTel: '',       // 加密手机号
        infoNo: '',     // 订单号
        orderNo: '',     // 暖哇、保通等订单号
        totalPremium: 0,
        upgradePremium: 0,
        starPhone: '',
        phoneNo: '',    // 手机号
        name1: '',      // 本人W
        name2: '',      // 配偶
        name3: '',      // 儿女
        name4: '',      // 父母
        idCard1: '',
        idCard2: '',
        idCard3: '',
        idCard4: '',
        channelCode: '',
        certNo: '',
        certName: '',
        mobile: '',
        name: '',
        idCard: '',
        callbackUrl:'',
        pType: 1, // 1.微信 2.支付宝
        utmId: '1640849639782',
        traceBackUuid: '',
        enterUpgrade: 0, // 是否进入过升级页面
        school: null,
        authFailObj: {}, // 实名认证失败
    }
}

export const checkOrderParams = (orderInfo, page,) => {
    const {
        productKey,
        infoNo,
        mTel,
        tel,
        channel,
        traceBackUuid,
        isChecked,
        hasCode,
        channelCode = '',
        utmId,
        school,
        totalPremium,
        upgradePremium,
    } = orderInfo;
    const {name1, idCard1, phoneNo, starPhone, relation, repay, insurance, phoneCode, pType} = orderInfo;
    const isOwn = relation == 1;
    const params = {
        infoNo: infoNo,
        relation: relation,
        insurance: insurance,
        paymentPlan: repay,
        planKey: productKey,
        channelId: channel,
        traceBackUuid: traceBackUuid,
        page: page,
        channelCode,

        operatorPhone: '',
        holderName: '',
        holderIdCard: '',
        holderPhone: '',
        insuredName: '',
        insuredIdCard: '',
        insuredPhone: '',
    };

    if (!isOwn) {
        const name = orderInfo[`name${relation}`]
        if (!isPersonName(name)) {
            return {valid: false, msg: '请填写正确的被保人姓名'};
        }
        params.insuredName = name;

        const idCard = orderInfo[`idCard${relation}`]
        if (!isCardNo(idCard)) {
            return {valid: false, msg: '请填写正确的被保人身份证号'};
        }
        params.insuredIdCard = idCard;

        if (!isPersonName(name1)) {
            return {valid: false, msg: '请填写正确的投保人姓名'};
        }
        params.holderName = name1;

        if (!isCardNo(idCard1)) {
            return {valid: false, msg: '请填写正确的投保人身份证号'};
        }
        params.holderIdCard = idCard1;
    } else {
        const name = orderInfo[`name${relation}`]
        if (!isPersonName(name)) {
            return {valid: false, msg: `请填写正确的${isOwn ? '您的' : '被保人'}姓名`};
        }
        params.holderName = name;

        const idCard = orderInfo[`idCard${relation}`]
        if (!isCardNo(idCard)) {
            return {valid: false, msg: `请填写正确的${isOwn ? '您的' : '被保人'}身份证`};
        }
        params.holderIdCard = idCard;
    }

    if (!isPhoneNum(phoneNo) && !isMaskedAndT1Phone(starPhone, mTel)) {
        return {valid: false, msg: '请填写正确的手机号码'};
    }
    params.holderPhone = isMaskedAndT1Phone(starPhone, mTel) ? mTel : phoneNo;
    params.operatorPhone = mTel || tel || phoneNo;

    const insuredIdCard = orderInfo[`idCard${relation}`];
    const age = GetAge(idCard1)
    const age1 = GetAge(insuredIdCard)
    if (age < 18) {
        return { valid: false, msg: '请填写正确的投保人(年龄≥18周岁)' };
    }

    if (relation != 1) {
        if (idCard1 == insuredIdCard) {
            return { valid: false, msg: '请填写正确的投保人(您与被保人身份证号码不能相同）' };
        }
    }

    if (relation == 2) {
        const agender = (+idCard1.slice(16, 17)) % 2;
        const agender1 = (+insuredIdCard.slice(16, 17)) % 2;
        if(age1 < 18) {
            return { valid: false, msg: '请填写正确的被保人(年龄≥18周岁)' };
        }
        if (agender == agender1) {
            return { valid: false, msg: '请填写正确的被保人(配偶双方性别不能相同）' };
        }
    }

    if (relation == 3 && (age - age1 < 16)) {
        return { valid: false, msg: '请填写正确的被保人(您与儿女年龄至少要相差16周岁)' };
    }

    if (relation == 4 && (age1 - age < 16)) {
        return { valid: false, msg: '请填写正确的被保人(您与父母年龄至少要相差16周岁)' };
    }
    saveOrderInfo(orderInfo);

    if (!isChecked) {
        return {valid: false, msg: '用户协议未同意'};
    }

    const index = window.location.href.indexOf('/ZYBX/');
    let resultUrl = `${window.location.href.substring(0, index)}/ZYBX/YBGR04/Upgrade`;

    const certName = `name${relation}`;
    const insuredName = orderInfo[certName];
    const certNo = `idCard${relation}`;

    const obj = {
        channel,
        school,
        mTel,
        starPhone,
        relation,
        name1,
        idCard1,
        phoneNo,
        utmId,
        totalPremium,
        upgradePremium,
        [certNo]: insuredIdCard,
        [certName]: insuredName,
    }

    console.log(JSON.stringify(obj))

    const param = url_safe_b64_encode(JSON.stringify(obj));
    resultUrl = `${resultUrl}?bizParams=${param}`;   

    orderInfo.callbackUrl = resultUrl;

    const envCode = isInWx() ? 'WEIXIN' : (isInAliPay() ? 'ALIPAY' : 'H5');

    const extendParams = {
        envCode,
        frontendNotifyUrl: resultUrl,
        school,
        utmId,
        payMode: 'jdpay',
        resourceNo: 'qywx_mfA_wtbrp_mfAzB01',
        startNo: 'qywx_mfA_wtbrp_mfAzB01'
    };

    params.extendParams = JSON.stringify(extendParams);

    return {valid: true, data: params};
}

export const submitOrderInfo = (params, orderInfo) => {
    const v = getFailIdentity(orderInfo);
    if (v) {
        return Promise.reject({ reason: '核保失败', msg: v });
    }

    Indicator.open({
        text: '订单提交中\n请稍候',
        spinnerType: 'fading-circle'
    });

    return new Promise((resolve, reject) => {
        fetchCreateOrderForZA(params).then(result => {
            orderResultHandle(result, orderInfo, resolve, reject);
        }).catch(err => {
            reject({reason: '接口出错', msg: JSON.stringify(err)});
        }).finally(() => {
            Indicator.close();
        });
    });
}

export const submitPromoteOrderInfo = (params, orderInfo) => {
    Indicator.open({
        text: '订单提交中\n请稍候',
        spinnerType: 'fading-circle'
    });

    return new Promise((resolve, reject) => {
        createPromotionOrder(params).then(result => {
            Indicator.close();
            orderResultHandle(result, orderInfo, resolve, reject);
        }).catch(err => {
            Indicator.close();
            reject({reason: '接口出错', msg: JSON.stringify(err)});
        });
    });
}

const orderResultHandle = (result, orderInfo, resolve, reject) => {
    const {code, msg, data} = result.data || {};
    if (code != 2000) {
        return reject({reason: '核保失败', msg: msg});
    }

    const rdata = data || {};
    const {orderNo, success, msg: message, payforURL} = rdata;
    if (orderNo) {
        orderInfo.infoNo = orderNo;
    }
    if (!success) {
        let v = message;
        if (v.indexOf('不通过') >= 0 || v.indexOf('未通过') >= 0) {
            v = '实名认证未通过，请核对修改姓名和身份证号';
            saveFailIdentity(orderInfo, v);
        }
        return reject({ reason: '核保失败', msg: v });
    }

    if (payforURL) {
        if (payforURL == 'about:blank') {
            window.location.href = payforURL;
            return resolve('拉起支付链接');
        }
        const inputForm = document.createElement("div");
        document.body.appendChild(inputForm);
        inputForm.innerHTML = payforURL;
        document.forms['cashierSubmit'].submit();
        document.body.removeChild(inputForm);
        return resolve('拉起支付链接');
    }
    return reject({reason: '支付链接为空', msg: '未获取到支付链接'});
}

export const upgradeOrderInfo = (infoNo, page, traceBackUuid, signBackUrl) => {
    return new Promise((resolve, reject) => {
        const params = {
            infoNo,
            page,
            traceBackUuid,
            signBackUrl,
        };
        upgradeZANewCrossSellProduct(params).then(res => {
            const {code, msg, data} = res.data || {};
            if (code != 2000) {
                return reject({code: -1, msg: msg});
            }

            const {code: code1, signUrl, result = ''} = data || {};
            if (code1 == 0 || result.includes('已升级')) {
                if (signUrl) {
                    const inputForm = document.createElement("div");
                    document.body.appendChild(inputForm);
                    inputForm.innerHTML = signUrl;
                    document.forms['cashierSubmit'].submit();
                    document.body.removeChild(inputForm);
                    return resolve({code: 0, signAgainFlag: true});
                }
                return resolve({code: 0, signAgainFlag: false});
            }
            return reject({code: -1, msg: result});
        }).catch(err => {
            reject({code: -1, msg: JSON.stringify(err)});
        });
    });
}

export const calculatePremium = (idCardNo, insurance, repay) => {
    if (!idCardNo || idCardNo.length != 18) {
        return {low: 0, high: 0};
    }

    const birthday = idCardNo.substr(6, 8);
    const age = moment().add(1, 'days').diff(moment(birthday), 'year'); // 计算年龄
    const premiumObj = PremiumRate.find(item => item.min <= age && item.max >= age);
    const premiumObj1 = PremiumRate1.find(item => item.min <= age && item.max >= age);

    // 年龄超出投保范围
    if (!premiumObj || !premiumObj1) {
        return {low: 0, high: 0};
    }

    const obj = repay == 0 ? premiumObj.year : premiumObj.month;
    const obj1 = repay == 0 ? premiumObj1.year : premiumObj1.month;

    const premium = insurance == 0 ? obj.data2 : obj.data1;
    const premium1 = insurance == 0 ? obj1.data2 : obj1.data1;

    return {low: premium, high: premium1};
}

export const isStarPhone = (obj) => {
  const {starPhone, mTel} = obj || {};
  return starPhone && mTel;
}

export const eventTracking = (orderInfo, name, time = 0) => {
    const {action, productKey, mTel, tel, channel, phoneNo, identifier} = orderInfo;
    const map = {
        back: '返回',
        promotion: '促活跳转',
        follow: '结果跳转',
        rFollow: '新版结果跳转',
        direct: '直发',
        forward: '转发',
        result: '结果页',
        promote: '促活'
    };
    const prefix = action in map ? `${map[action]}-` : '';
    const page = `${prefix}优保国任魔方${identifier}`;
    const phone = mTel || tel || (isPhoneNum(phoneNo) ? phoneNo : '');

    actionTracking({
        page: `${page}(${productKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: productKey,
        time: time,
    }).then(res => {
        const {code, data} = res;
        if (code != 2000) return;
        const {mobileId} = data || {};
        if (mobileId) {
            orderInfo.mTel = mobileId;
        }
    });
}

export const inputEndEditing = () => {
  const inputList = document.getElementsByTagName('input') || [];
  for (const input of inputList) {
      input.blur && input.blur();
  }
}

export const loadOrderInfo = () => {
    return bxStorage.getObjItem('YBGR04Info') || {};
}

export const saveOrderInfo = (orderInfo) => {
    bxStorage.setObjItem('YBGR04Info', orderInfo);
}

export const msgToast = (msg) => {
    Toast({
        message: msg || '',
        position: 'center',
        duration: 2500, // 弹窗时间毫秒
    });
}

export const createNoticeList = () => {
    const noticeList = []
    for (let idx = 0; idx < 100; idx++) {
        const tailNumber = (Math.random() * 8999 + 1000).toFixed(0)
        noticeList.push(tailNumber)
    }
    return noticeList
}

const saveFailIdentity = (orderInfo, reason) => {
    const { relation, name1, idCard1,} = orderInfo;
    const certName = orderInfo[`name${relation}`];
    const certNo = orderInfo[`idCard${relation}`];
    const md5 = CryptoJS.MD5(`${name1}${idCard1}${certName}${certNo}`).toString();
    orderInfo.authFailObj[md5] = reason;
    console.log(md5);
}

const getFailIdentity = (orderInfo) => {
    const { relation, name1, idCard1,} = orderInfo;
    const certName = orderInfo[`name${relation}`];
    const certNo = orderInfo[`idCard${relation}`];
    const md5 = CryptoJS.MD5(`${name1}${idCard1}${certName}${certNo}`).toString();
    return orderInfo.authFailObj[md5]
}
