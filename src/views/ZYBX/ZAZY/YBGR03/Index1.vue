<template>
    <div class="container_2304241525">
        <div id="id_header_banner" class="header">
            <img :src="imgList.img_1" alt="头图" class="header-img">
            <!--领取轮播-->
            <van-notice-bar :scrollable="false" class="notice-bar">
                <van-swipe :autoplay="2000" :show-indicators="false" class="notice-swipe" vertical>
                    <van-swipe-item v-for="(notice,idx) in noticeList" :key="idx">尾号{{ notice }}刚刚已领取</van-swipe-item>
                </van-swipe>
            </van-notice-bar>
        </div>
        <div id="信息填写" class="orderForm">
            <UserField :orderInfo="orderInfo" @input="orderInfoEdit"></UserField>
            <div class="premium"><span class="number">{{ premiumDict.amount }}</span>元{{ premiumDict.suffix }}/月</div>
            <!--投保按钮-->
            <div id="id_action_button" class="submitButton" @click="onBottomSubmit(true)">
                <img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
            </div>
            <!--参与人数-->
            <div class="peopleNum">累计已有<span class="number">{{ peopleNumber }}</span>人参与</div>
            <div class="policyField">
                <span :class="{active:orderInfo.isChecked}" class="normal checkBox" @click="orderInfo.isChecked = !orderInfo.isChecked">
                    我已阅读并同意
                    <span class="read" @click.stop="lookupTerm('健康告知',true)">《健康告知》</span>
                    <span class="read" @click.stop="lookupTerm('责任免除',true)">《责任免除》</span>
                    <!-- <span class="read" @click.stop="lookupTerm('特别约定',true)">《特别约定》</span> -->
                    <span class="read" @click.stop="lookupTerm('投保须知',true)">《投保须知》</span>
                    <span class="read" @click.stop="lookupTerm('重要提示',true)">《重要提示》</span>
                    <span class="read" @click.stop="lookupTerm('投保声明',true)">《投保声明》</span>
                    <span class="read" @click.stop="lookupTerm('个人信息保护政策',true)">《个人信息保护政策》</span>
                    <span class="read" @click.stop="lookupTerm('客户告知书',true)">《客户告知书》</span>
                    <span class="read" @click.stop="lookupTerm('经纪委托协议',true)">《经纪委托协议》</span>
                    <span class="read" @click.stop="lookupTerm('被保人同意声明',true)">《被保人同意声明》</span>
                    <span class="read" @click.stop="lookupTerm('条款及费率',true)">《条款及费率》</span>
                </span>
                <br>
                <!-- <span class="normal">保费与被保人年龄、社保相关，详情请见
                    <span class="read" @click.stop="lookupTerm('费率表',true)">《费率表》</span>
                </span> -->
            </div>
        </div>
        <van-tabs v-model="currentTab" class="section-wrapper" color="#ff7700" scrollspy sticky title-active-color="#ff7700">
            <van-tab key="保障内容" name="保障内容" title="保障内容">
                <div class="section">
                    <div class="divider"></div>
                    <Plan id="保障内容" @click="lookupTerm"></Plan>
                </div>
            </van-tab>
            <van-tab key="产品特色" name="产品特色" title="产品特色">
                <div class="section">
                    <div class="divider"></div>
                    <div class="section-title">产品特色</div>
                    <img :src="imgList.img_2" class="section-image">
                </div>
            </van-tab>
            <van-tab key="理赔说明" name="理赔说明" title="理赔说明">
                <div class="section">
                    <div class="divider"></div>
                    <div class="section-title">理赔说明</div>
                    <ClaimProcess></ClaimProcess>
                    <div class="divider"></div>
                    <div class="section-title">常见问题</div>
                    <AskedQuestion @click="lookupTerm('常见问题')"></AskedQuestion>
                </div>
            </van-tab>
            <van-tab key="保险条款" name="保险条款" title="保险条款">
                <div class="section">
                    <div class="divider"></div>
                    <div class="section-title">保险条款</div>
                    <InsurancePolicy class="policy-box" @onRead="seePolicy"></InsurancePolicy>
                </div>
            </van-tab>
        </van-tabs>
        <div class="copyright">
            <p>产品介绍页面仅供参考，具体责任描述以保险合同为准</p>
            <!-- <p>版权所有©众安在线保险经纪有限公司</p>
            <p><a href="https://beian.miit.gov.cn">粤ICP备18090138号-4</a></p> -->
        </div>
        <div v-if="bottomButtonVisible" class="bottomButton" @click="onBottomSubmit(true)">
            <img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <BTPopup ref="BTPopupRef"></BTPopup>
        <TabPopup ref="TabPopupRef" @confirm="confirmTerms" @seePolicy="seePolicy"></TabPopup>
        <SilentUpgradePopup ref="SilentUpgradePopupRef" :orderInfo="orderInfo" @click="silentUpgrade"></SilentUpgradePopup>
        <FailPopup :obj="failObj" @click="goToFailLink"></FailPopup>
        <BackPopup :obj="backObj" @click="backButtonClick"></BackPopup>
        <ImpReminder :obj="orderInfo" @seePolicy="seePolicy"></ImpReminder>
        <!--录屏-->
        <ZALYRecord page="优保国任魔方YBGR03Index1" @SetIseeBiz="SetIseeBiz"></ZALYRecord>
    </div>
</template>

<script>
import moment from "moment";
import img_1 from '@/assets/imgs/ZAZY/GR01/img01_2.png';
import img_2 from '@/assets/imgs/ZAZY/GR01/img02.png';
import InsurancePolicy from "./components/InsurancePolicy";
import {
    isAIChannel,
    isInWx,
    isMaskedAndT1Phone,
    isPhoneNum,
    number2Thousandth,
    TraceLogInfoKeys,
    url_safe_b64_decode,
    url_safe_b64_encode
} from "@/assets/js/common";
import {
    checkOrderParams,
    createNoticeList,
    createOrderInfo,
    eventTracking,
    inputEndEditing,
    isStarPhone,
    loadOrderInfo,
    msgToast,
    saveOrderInfo,
    submitOrderInfo,
} from "./function";
import BTPopup from "./components/BTPopup";
import AskedQuestion from "./components/AskedQuestion";
import Plan from "./components/Plan";
import ClaimProcess from "./components/ClaimProcess";
import {
    channelLoginForYBZA,
    fetchInfoByOrderNoForZA,
    fetchRoundRobinWithAgeCommonFailForZA,
    fetchRoundRobinWithAgeZXResultForZA,
    fetchStarPhoneV4,
} from "@/api/insurance-api";
import TabPopup from "./components/TabPopup";
import UserField from "./components/UserField";
import ZALYRecord from "@/views/components/ZALYRecord";
import ImpReminder from "./components/ImpReminder";
import SilentUpgradePopup from "./components/SilentUpgradePopup1";
import FailPopup from "./components/FailPopup1";
import BackPopup from "./components/BackPopup";
import { domainPathMap, } from "@/views/ZYBX/src";
import { getSceneCodeForYB } from "@/utils/filing_number";

export default {
    name: "Index1", // 推广版
    components: {
        BackPopup,
        FailPopup,
        SilentUpgradePopup,
        ImpReminder,
        UserField,
        TabPopup,
        ZALYRecord,
        ClaimProcess,
        InsurancePolicy,
        Plan,
        AskedQuestion,
        BTPopup,
    },
    data() {
        const orderInfo = createOrderInfo('direct');
        return {
            imgList: { img_1, img_2, },
            currentTab: '',       // 切换的tab
            scrollRecord: false, // 页面滚动
            autoSubmit: true,    // 自动拉起支付
            orderInfo,
            isRequesting: false,
            bottomButtonVisible: false,
            failObj: { visible: false, showTimer: false, path: '' },
            backObj: { visible: false, showTimer: false, path: '' },
            YBExistFailHref: 'https://zhfin.zhonganib.com/transfer/recommend?locationId=rp_07bxdtdx001_hbtc&channelNo=385003',
            YBExistBackHref: 'https://zhfin.zhonganib.com/transfer/recommend?locationId=rp_07bxdtdx001_wltc&channelNo=385003',
            noticeList: [],
        }
    },
    computed: {
        orderPage() {
            const { source, action, identifier, school, } = this.orderInfo;
            const productKey = school == 1 ? TraceLogInfoKeys.yb_gr_jd_default_cube_base : TraceLogInfoKeys.yb_gr_jd_cube_base;
            return `infoKey:${productKey}&page:${identifier}&act:${action}&src:${source}`;
        },
        premiumDict() {
            const { totalPremium, } = this.orderInfo;
            if (totalPremium) {
                return { amount: number2Thousandth(totalPremium, 2) + '', suffix: '' };
            }
            return { amount: '0.99', suffix: '起' };
        },
        peopleNumber() {
            const date = moment().format('MMDD');
            const seconds = moment().diff(moment().startOf('day'), 'seconds');
            // 每天重新计算，起步3万多，每天增长约3k
            return (parseInt(date) + 35791 + seconds / 20).toFixed(0);
        },
    },
    created() {
        this.init();
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        if (this.orderInfo.enterUpgrade) {
            this.fetchInfoByOrderNo('', true);
        }

        this.$nextTick(() => {
            this.actionButtonObserver();
            this.addObserverForBanner();
            this.timeOnPage();
        });

        this.$nextTick(() => { // 监听页面返回
            this.pushState();
            window.addEventListener("popstate", this.popstate, true);
        });

        this.noticeList = createNoticeList();
    },
    beforeDestroy() {
        window.removeEventListener('popstate', this.popstate);
    },
    methods: {
        // 停留时间埋点
        timeOnPage() {
            const obj = this.orderInfo;
            const action = (function () {
                let idx = 0;
                const start = Date.now();
                return function () {
                    try {
                        if (idx <= 0) {
                            idx++;
                            const time = Date.now() - start;
                            const {channel, mTel, phoneNo} = obj;
                            const phone = mTel || (isPhoneNum(phoneNo) ? phoneNo : '');
                            const params = { "page": `优保关爱·重大疾病保险YBGR03Index1(yb_gr_jd_default_cube_base)-停留时间(${time})`, "channel": channel, "mobileId": phone, "infoKey": "yb_gr_jd_default_cube_base", "time": time };
                            const blob = new Blob([JSON.stringify(params)], { type: 'application/json;charset=UTF-8' });
                            navigator.sendBeacon('https://oiisf.zabxib.com/marketprod/Insurance/trace/v2/log', blob);
                        }
                    } catch (e) {

                    }
                }
            })();

            // 使用beforeunload事件
            window.addEventListener('beforeunload', (event) => {
                action();
            });

            // 使用visibilitychange事件
            document.addEventListener('visibilitychange', () => {
                if (document.visibilityState === 'hidden') {
                    action();
                }
            });
        },

        actionButtonObserver() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries, observer) => {
                    this.bottomButtonVisible = !entries[0].isIntersecting;
                }, { threshold: 0.01 });

                const buttonNode = document.getElementById('id_action_button');
                buttonNode && observer.observe(buttonNode);
            }
        },
        addObserverForBanner() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries, observer) => {
                    const entry = entries[0];
                    if (!entry.isIntersecting) {
                        observer.unobserve(entry.target);
                        this.scrollTimeReport();
                    }
                }, { threshold: 0.10 });

                const buttonNode = document.getElementById('id_header_banner');
                buttonNode && observer.observe(buttonNode);
            }
        },
        seePolicy(policy) {
            this.$refs.BTPopupRef && this.$refs.BTPopupRef.onShow(policy.page, policy);
        },
        SetIseeBiz(value) {
            this.orderInfo.traceBackUuid = value
            saveOrderInfo(this.orderInfo);
        },
        // 初始化
        init() {
            const query = this.$route.query || {};
            query.source = query.source || 'direct'; // source要以链接携带的参数为准
            query.action = query.action || 'direct'; // action要以链接携带的参数为准
            query.channelCode = query.cld || query.channelCode || '';
            query.utmId = query.utmId || '1640849639782';
            query.pType = isInWx() ? '1' : '2';

            const inStore = loadOrderInfo();
            Object.assign(this.orderInfo, inStore, query);

            try {
                if (query.bizParams) {
                    const params = JSON.parse(url_safe_b64_decode(query.bizParams));
                    Object.assign(this.orderInfo, params);
                }
            } catch (error) {

            }

            this.orderInfo.identifier = 'YBGR03Index1';
            this.orderInfo.hasCode = false;
            this.orderInfo.phoneCode = '';
            this.orderInfo.school = '1';
            this.orderInfo.productKey = TraceLogInfoKeys.yb_gr_jd_default_cube_base;
            // 特殊渠道号处理
            const isTestChannel = (this.orderInfo.channel + '').length < 4;
            this.orderInfo.isChecked = !isTestChannel;

            saveOrderInfo(this.orderInfo);

            const channel = this.orderInfo.channel || 1;
            if (channel >= 103401 && channel <= 103500) {
                this.YBExistFailHref = 'https://zhfin.zhonganib.com/transfer/recommend?locationId=rp_07bxzxddtdxbx001_hbtc&channelNo=385003';
                this.YBExistBackHref = 'https://zhfin.zhonganib.com/transfer/recommend?locationId=rp_07bxzxddtdxbx001_wltc&channelNo=385003';
            }

            this.fetchPhoneNumber();
        },
        // 手机号解密
        fetchPhoneNumber() {
            const { m, phoneNo } = this.orderInfo;
            if (!m || isPhoneNum(phoneNo) || isStarPhone(this.orderInfo)) {
                return this.homeTimeReport();
            }

            const params = { encryptContent: m };
            fetchStarPhoneV4(params).then(res => {
                const { encryptPhone, showPhone } = res.data;
                this.orderInfo.mTel = encryptPhone;
                this.orderInfo.starPhone = showPhone;
                saveOrderInfo(this.orderInfo);
            }).finally(() => {
                return this.homeTimeReport();
            });
        },
        orderInfoEdit() {
            saveOrderInfo(this.orderInfo);
            if (this.autoSubmit) {
                this.onBottomSubmit(false);
            }
        },
        silentUpgrade(value) {
            this._actionTracking(`首页：${value ? '默认升级' : '正常升级'}`);
            this.orderInfo.school = value ? '1' : '0';
            this.onSubmit();
        },
        onBottomSubmit(errNotice) {
            const channel = this.orderInfo.channel + '';
            if (isAIChannel(channel)) {
                errNotice && this._actionTracking('点击立即投保按钮');
            }

            const result = checkOrderParams(this.orderInfo, this.orderPage);
            const { valid, msg, } = result;
            if (!valid) {
                if (msg.startsWith('请填写正确的')) {
                    if (errNotice) {
                        msgToast(msg);
                        document.body.scrollTop = document.documentElement.scrollTop = 200;
                    }
                    return false;
                }
                inputEndEditing();
                this.lookupTerm('责任免除', true);
                return true;
            }
            inputEndEditing();
            this.checkHealth();
        },
        onSubmit() {
            const { data } = checkOrderParams(this.orderInfo, this.orderPage);
            data.planKey = this.orderInfo.school != 1 ? TraceLogInfoKeys.yb_gr_jd_cube_base : TraceLogInfoKeys.yb_gr_jd_default_cube_base;
            this._actionTracking('点击立即领取按钮');

            submitOrderInfo(data, this.orderInfo).then(() => {
                // window.location.href = url;
            }).catch(err => {
                const { msg } = err;
                const message = msg || '';
                if (message.includes('未通过')) {
                    return msgToast(message);
                }
                if (!message.includes('无法再次投保') && !message.includes('已存在保单') && !message.includes('投保份数')) {
                    return this.getFailPath();
                }
                this.fetchInfoByOrderNo(message);
            }).finally(() => {
                saveOrderInfo(this.orderInfo);
            });

            return true;
        },
        // 根据orderNo查询订单信息
        fetchInfoByOrderNo(msg, enterUpgrade) {
            const { relation, callbackUrl } = this.orderInfo;
            const idCardNo = this.orderInfo[`idCard${relation}`];
            const params = { infoKey: TraceLogInfoKeys.yb_gr_jd_cube_base, insuredIdCard: idCardNo };

            fetchInfoByOrderNoForZA(params).then(r => {
                const { code, data } = r.data;
                if (code != 2000 || !data) {
                    return !enterUpgrade && this.getFailPath();
                }

                const { infoNo } = data || {};
                if (infoNo && callbackUrl && callbackUrl.indexOf('http') >= 0) {
                    const params = `infoNo=${infoNo}`;
                    let href = callbackUrl.replace(/&?infoNo=[^?&]*/ig, '');
                    href = href.indexOf('?') > 0 ? `${href}&${params}` : `${href}?${params}`;
                    return window.location.href = href;
                }
                return !enterUpgrade && msgToast(msg);
            }).catch(() => {
                return !enterUpgrade && msgToast(msg);
            });
        },

        getFailPath() {
            const { channel, phoneNo, mTel, relation } = this.orderInfo;

            const params = {
                channelId: channel,
                idCard: this.orderInfo[`idCard${relation}`],
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                robinKey: 'yb_mf_common_fail',
                currentPage: 'YBGR03',
            }

            fetchRoundRobinWithAgeCommonFailForZA(params).then(result => {
                const { path } = result.data || {};
                this.failObj.path = path || '';
            }).finally(() => {
                if (this.failObj.path == 'NotFound') {
                    return msgToast('投保失败，您可以选择为家人投保');
                }

                if (!domainPathMap[this.failObj.path]) {
                    this.failObj.path = 'YBExistFailPath';
                }

                if (this.failObj.path == 'YBExistFailPath') {
                    this.phoneEncryptForZX();
                }

                this.failObj = { ...this.failObj, visible: true, showTimer: true };
                this._actionTracking(`显示核保失败弹窗(${this.failObj.path})`);
            });
        },

        goToFailLink() {
            const path = this.failObj.path;

            this._actionTracking(`点击核保失败图片(${path})`);

            if (path == 'YBExistFailPath') {
                return setTimeout(() => {
                    window.location.href = this.YBExistFailHref;
                }, 250);
            }

            const { channel, relation, mTel, phoneNo, channelCode, starPhone, identifier, name1, idCard1 } = this.orderInfo;

            const params = {
                channel, cld: channelCode, mTel, relation, source: identifier, action: 'forward',
                name1, idCard1, [`name${relation}`]: this.orderInfo[`name${relation}`], [`idCard${relation}`]: this.orderInfo[`idCard${relation}`]
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else if (isMaskedAndT1Phone(starPhone, mTel)) {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));

            setTimeout(() => {
                const href = domainPathMap[path];
                window.location.href = `${href}?bizParams=${bizParams}`;
            }, 250);
        },

        getBackPath() {
            const { channel, phoneNo, mTel } = this.orderInfo;

            const params = {
                channelId: channel,
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                robinKey: 'yb_za_mf_back',
            }

            fetchRoundRobinWithAgeZXResultForZA(params).then(result => {
                const { path } = result.data || {};
                this.backObj.path = path || '';
            }).finally(() => {
                if (!domainPathMap[this.backObj.path]) {
                    this.backObj.path = 'YBExistBackPath';
                }

                if (this.backObj.path != 'YBExistBackPath') {
                    return;
                }

                this.phoneEncryptForZX();
                this.backObj = { ...this.backObj, visible: true, showTimer: false, };
                this._actionTracking(`首页-点击返回按钮(${this.backObj.path})`);
            });
        },

        backButtonClick() {
            const path = this.backObj.path;
            this._actionTracking(`首页-点击返回弹框图片(${path})`);

            return setTimeout(() => {
                window.location.href = this.YBExistBackHref;
            }, 250);
        },

        phoneEncryptForZX() {
            if (this.YBExistFailHref.indexOf('cUserId') >= 0 || this.isRequesting) {
                return;
            }
            const { phoneNo, mTel, utmId } = this.orderInfo;
            const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;
            if (!phone) return;

            this.isRequesting = true;
            const sceneCode = getSceneCodeForYB();
            const params = {mobile:phone, utmId, sceneCode}
            channelLoginForYBZA(params).then(r => {
                const { code, data } = r.data || {};
                if (code == 2000) {
                    const { accessKey, accountId } = data;
                    this.YBExistFailHref = `${this.YBExistFailHref}&cToken=${accessKey}&cUserId=${accountId}`;
                    this.YBExistBackHref = `${this.YBExistBackHref}&cToken=${accessKey}&cUserId=${accountId}`;
                }
            }).finally(() => {
                this.isRequesting = false;
            });
        },

        checkHealth() {
            this.$refs.SilentUpgradePopupRef && this.$refs.SilentUpgradePopupRef.onShow();
        },
        confirmTerms(isSubmit) {
            this.orderInfo.isChecked = true;

            if (!isSubmit) return;
            this.onBottomSubmit(true);
        },
        lookupTerm(val, isSubmit) {
            if (['费率表', '常见问题', '保障详情'].includes(val)) {
                this.$refs.BTPopupRef && this.$refs.BTPopupRef.onShow(val);
            } else {
                this.$refs.TabPopupRef && this.$refs.TabPopupRef.onShow(val, isSubmit);
            }
        },
        scrollTimeReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd } = timing || {};
            const detentionTime = moment() - domContentLoadedEventEnd;
            this._actionTracking('首页滚动', detentionTime);
        },
        homeTimeReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);
        },
        _actionTracking(name, time = 0) {
            eventTracking(this.orderInfo, name, time);
        },
        pushState() {
            window.history.pushState({}, null, '');
        },
        popstate() {
            this.getBackPath();
            this.pushState();
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
    .container_2304241525 {
        width: 3.75rem;
        min-height: 100%;
        font-size: 0.15rem;
        background-color: #ffffff;

        .header {
            .header-img {
                display: block;
                width: 100%;
            }

            .notice-bar {
                position: absolute;
                top: 0.12rem;
                right: 0.16rem;
                width: 1.16rem;
                height: 0.2rem;
                padding: 0 0.1rem;
                border-radius: 0.18rem;
                color: #fff;
                background-color: rgba(0, 0, 0, .05);

                .notice-swipe {
                    height: 0.20rem;
                    padding: 0.02rem 0 0;
                    font-size: 0.12rem;
                    line-height: 0.20rem;
                    text-align: center;
                }
            }
        }

        .section-wrapper {
            .section {
                background-color: #ffffff;

                .section-title {
                    display: flex;
                    justify-content: center;
                    align-items: center;

                    color: #333333;
                    font-size: 0.18rem;
                    font-weight: 500;
                    line-height: 0.45rem;

                    &::before {
                        content: " ";
                        margin-right: 0.1rem;
                        width: 0.55rem;
                        height: 0.13rem;
                        background: url("~@/assets/imgs/common/icon_needle_left.png")
                            no-repeat center;
                        background-size: 100%;
                    }

                    &::after {
                        content: " ";
                        margin-left: 0.1rem;
                        width: 0.55rem;
                        height: 0.13rem;
                        background: url("~@/assets/imgs/common/icon_needle_right.png")
                            no-repeat center;
                        background-size: 100%;
                    }
                }

                .section-image {
                    display: block;
                    width: 100%;
                }
            }
        }

        .orderForm {
            border-radius: 0.1rem;
            position: relative;
            margin: -0.16rem 0.1rem 0.1rem;
            background-color: #ffffff;
            box-shadow: 0 1px 10px 0 rgba(200, 133, 85, 0.24);

            .premium {
                margin: 0.15rem 0 0.1rem;
                font-size: 0.16rem;
                font-weight: 500;
                text-align: center;

                .number {
                    margin: 0 0.05rem;
                    font-size: 0.2rem;
                    color: #ff4509;
                }
            }

            .submitButton {
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                width: 3.19rem;
                height: 0.52rem;
                margin: 0.2rem auto;
                text-align: center;
                font-size: 0.24rem;
                font-weight: 800;
                line-height: 0.27rem;
                color: #ffffff;
                border-radius: 0.25rem;
                //animation: button_animate 1.5s linear infinite;
                background: url("~@/assets/imgs/NW/NWTK03/button.gif") no-repeat;
                background-size: contain;

                .hand {
                    position: absolute;
                    top: 0.25rem;
                    left: 75%;
                    width: 18%;
                    animation: hand_animate 1s linear infinite;
                }
            }

            .peopleNum {
                padding: 0.05rem 0;
                color: #999;
                font-size: 0.12rem;
                line-height: 0.25rem;
                text-align: center;
                border-radius: 6px;

                .number {
                    margin: 0 0.04rem;
                    color: #fe2b33;
                    font-size: 0.16rem;
                    font-weight: bold;
                }
            }

            .policyField {
                padding: 0.08rem;
                background-color: #fff7f7;

                .normal {
                    font-size: 0.13rem;
                    color: #333333;
                    line-height: 0.2rem;
                    text-align: justify;
                }

                .checkBox {
                    position: relative;
                    padding-left: 0.2rem;

                    &.active {
                        &::before {
                            background: url("~@/assets/imgs/common/sprites.png")
                                no-repeat -0.32rem 0;
                            background-size: 1.6rem 3.2rem;
                        }
                    }

                    &::before {
                        content: "";
                        position: absolute;
                        top: 0rem;
                        left: 0rem;
                        height: 0.16rem;
                        width: 0.16rem;
                        background: url("~@/assets/imgs/common/sprites.png")
                            no-repeat -0.48rem 0;
                        background-size: 1.6rem 3.2rem;
                    }
                }

                .read {
                    color: #ff8c41;
                    font-weight: 500;
                }
            }
        }

        .copyright {
            padding: 0 0 1rem;
            text-align: center;
            color: #737680;
            font-size: 0.12rem;
            font-weight: 500;
            line-height: 1.80;
            
            a {
                color: #737680;
                text-decoration: underline;
            }
        }

        .bottomButton {
            position: fixed;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0 auto;
            inset: auto 0 0.3rem 0;
            width: 3.19rem;
            height: 0.52rem;
            text-align: center;
            font-size: 0.24rem;
            font-weight: 700;
            color: #fff;
            border-radius: 0.25rem;
            //animation: button_animate 1.5s linear infinite;
            background: url("~@/assets/imgs/NW/NWTK03/button.gif") no-repeat;
            background-size: contain;

            .hand {
                position: absolute;
                top: 0.25rem;
                left: 75%;
                width: 18%;
                animation: hand_animate 1s linear infinite;
            }
        }

        .divider {
            height: 0.08rem;
            background: #f8f8f8;
        }
    }

    @keyframes button_animate {
        0% {
            transform: scale(1);
        }
        40% {
            transform: scale(1);
        }
        70% {
            transform: scale(0.95);
        }
        100% {
            transform: scale(1);
        }
    }

    @keyframes hand_animate {
        0% {
            transform: translate(-0.1rem, -0.1rem);
        }
        45% {
            transform: translate(0.1rem, 0);
        }
        70% {
            transform: translate(0.1rem, 0);
        }
        100% {
            transform: translate(-0.1rem, -0.1rem);
        }
    }
</style>
