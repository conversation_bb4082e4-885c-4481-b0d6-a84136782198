<template>
    <div class="insurance-policy">
        <h5 class="header">保险条款</h5>
        <div class="item" v-for="(item) in insurancePolicy" :key="item.page" >
              <span class="type" v-html="item.type"></span>
              <div  class="content" @click="onRead(item)">
                  <span class="name">
                    <span class="text">{{ item.page }}</span>
                    <svg-icon iconClass="arrow-right" ></svg-icon>
                  </span>
                  <span class="code">{{ item.code }}</span>
              </div>
        </div>
      </div>
</template>

<script>
import {insurancePolicy} from '../src';
import '@/assets/icons/arrow-right.svg';
export default {
    data() {
        return {insurancePolicy,}
    },
    computed: {
    },
    methods: {
      onRead(doc) {
        this.$emit('onRead', doc);
      }
    }
}
</script>

<style scoped type="text/less" lang="less">
.insurance-policy {
      background-color: #FFFFFF;
      min-height: 90%;
      width: 100%;
      box-sizing: border-box;
      margin: 0.08rem auto;
      padding: 0 0.15rem 0.14rem;
      border-radius: 0.1rem;
      .header {
        font-size: 0.18rem;
        line-height: 0.24rem;
        margin-bottom: 0.05rem;
      }
      .item {
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          padding: 0.12rem 0;
          font-size: 0.14rem;
          line-height: 0.18rem;
          color: #333333;
          border-bottom: #F2F2F2 1px solid;
          .type {
            line-height: 0.22rem;
            font-size: 0.16rem;
            margin-bottom: 0.1rem;
          }
          .content {
            display: flex;
            justify-content: space-between;
            flex-direction: column;
            .name {
              display: flex;
              justify-content: space-between;
              color: #737680;
              margin-bottom: 0.05rem;
              .text {
                max-width: 3rem;
              }
              .svg-icon {
                font-size: 0.16rem;
                stroke-width: 100;
                fill: #737680;
              }

            }
            .code {
              font-size: 0.13rem;
              color: #a0a4b3;
            }
          }
      }
}
</style>
