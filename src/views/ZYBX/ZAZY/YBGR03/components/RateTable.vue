<template>
    <div class="rateTable">
        <img v-lazy="rateTable"/>
    </div>
</template>

<script>
import rateTable from '@/assets/imgs/ZAZY/GR01/rate.png';
import rateTable1 from '@/assets/imgs/ZAZY/GR01/rate1.png';

export default {
    name: "RateTable",
    props: {type: String},
    data() {
        return {}
    },
    computed: {
        rateTable() {
            return this.type == 'upgrade' ? rateTable1 : rateTable;
        }
    },
}
</script>

<style scoped type="text/less" lang="less">

.rateTable {
    background-color: #FFFFFF;

    img {
        display: block;
        width: 100%;
    }
}

</style>
