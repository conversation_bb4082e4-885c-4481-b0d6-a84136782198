<template>
    <van-popup v-model="onVisible" position="bottom" class="container_2212271130" round>
        <div class="header">
            <img class="header-img" src="@/assets/imgs/ZAZY/YBGR03/upgrade.png">
            <div class="header-premium">
                <div>首月保费{{ premiumObj.lowPremium }}元，变更后次月起缴<span>{{ premiumObj.highPremium }}</span>元/月</div>
                <div>变更后的保障将于第二期保费扣费/缴费成功后生效</div>
            </div>
        </div>
        <div id="id_2212271130" class="policy">
            <div class="policy-title">健康告知</div>
            <div class="policy-content">
                <Policy_Health></Policy_Health>
            </div>
        </div>
        <div class="footer">
            <div class="footer-text">我同意次月变更保障
                <span class="footer-text-policy" @click="onReadPolicy('责任免除',false)">《责任免除》</span>
                <span class="footer-text-policy" @click="onReadPolicy('特别约定',false)">《特别约定》</span>
                <span class="footer-text-policy" @click="onReadPolicy('投保须知',false)">《投保须知》</span>
                <span class="footer-text-policy" @click="onReadPolicy('重要提示',false)">《重要提示》</span>
                <span class="footer-text-policy" @click="onReadPolicy('投保声明',false)">《投保声明》</span>
                <span class="footer-text-policy" @click="onReadPolicy('条款及费率',false)">《条款及费率》</span>
            </div>
            <div class="button-container">
                <div class="button" @click="clickNo">
                    不同意
                </div>
                <div class="button color" @click="clickYes">
                    同意并继续
                </div>
            </div>
        </div>
        <BTPopup type="upgrade" ref="BTPopupRef1"></BTPopup>
        <TabPopup ref="TabPopupRef" type="upgrade" @seePolicy="seePolicy"></TabPopup>
    </van-popup>
</template>

<script>
import Policy_Health from "./Policy/Policy_Health";
import BTPopup from "./BTPopup";
import TabPopup from "./TabPopup";
import '@/assets/icons/down.svg';

export default {
    name: "SilentUpgradePopup",
    components: {
        TabPopup,
        Policy_Health,
        BTPopup,
    },
    props: {
        orderInfo: Object
    },
    data() {
        return {
            onVisible: false,
        }
    },
    computed: {
        premiumObj() {
            const {totalPremium, upgradePremium} = this.orderInfo;
            return {lowPremium: totalPremium, highPremium: upgradePremium};
        },
    },
    methods: {
        seePolicy(policy) {
            this.$refs.BTPopupRef1 && this.$refs.BTPopupRef1.onShow(policy.page, policy);
        },
        onShow(page, policy) {
            // console.log('打开页面onShow =>', page);
            this.onVisible = true;
            setTimeout(this.setTop, 0);
        },
        clickNo() {
            this.onVisible = false;
            this.$emit('click', false);
        },
        clickYes() {
            this.onVisible = false;
            this.$emit('click', true);
        },
        setTop() {
            const node = document.getElementById('id_2212271130');
            node && (node.scrollTop = 0);
        },
        onReadPolicy(val, isSubmit) {
            this.$refs.TabPopupRef && this.$refs.TabPopupRef.onShow(val, isSubmit);
        },
    },
}
</script>

<style scoped type="text/less" lang="less">

.container_2212271130 {
    height: 95%;
    width: 3.75rem;
    left: calc((100% - 3.75rem) * 0.5);
    background-color: transparent;

    display: flex;
    flex-direction: column;

    .header {
        background-color: transparent;

        .header-img {
            display: block;
            max-width: 100%;
        }

        .header-premium {
            background-color: #fff;
            font-size: 0.13rem;
            color: #888888;
            text-align: center;
            line-height: 0.2rem;
            margin: 0 auto;
            padding-top: 0.1rem;

            .number {
                color: #FF491D;
            }
        }
    }

    .policy {
        flex: 1;
        overflow: auto;
        padding: 0.05rem 0;
        background-color: #fff;

        .policy-title {
            font-size: 0.14rem;
            text-align: center;
            font-weight: 500;
        }
    }

    .footer {
        background-color: #fff;
        border-top: #F2F2F2 1px solid;

        .footer-text {
            padding: 0.05rem 0.10rem 0;
            font-size: 0.13rem;
            line-height: 1.5;
            text-align: justify;

            .footer-text-policy {
                color: #FF8C41;
                font-weight: 500;
            }
        }

        .button-container {
            display: flex;
            justify-content: space-around;

            .button {
                margin: 0.08rem 0.2rem;
                height: 0.44rem;
                width: 1.50rem;
                line-height: 0.44rem;
                border-radius: 0.22rem;
                font-size: 0.16rem;
                font-weight: 500;
                text-align: center;
                color: #666666;
                border: #EEEEEE 1px solid;
                background-color: #FFFFFF;
            }

            .color {
                color: #FFFFFF;
                background: linear-gradient(to right, #FFCC00, #FF9500);
            }
        }
    }
}

</style>
