<template>
    <van-popup v-model="onVisible" class="container_2301041717" position="bottom" round>
        <div class="header" @click="onClose">{{ pageName }}</div>
        <div id="popup-page" class="content">
            <PlanDetail v-if="pageName === '保障详情'" :type="type"></PlanDetail>
            <Policy_Customer v-if="pageName == '客户告知书'" :type="type"></Policy_Customer>
            <RateTable v-if="pageName === '费率表'" :type="type"></RateTable>
            <ImagePage v-if="pageName.includes('保险')" :obj="policy"></ImagePage>
            <ImagePage v-else-if="pageName.includes('通用')" :obj="policy"></ImagePage>
        </div>
        <div class="footer">
            <div class="button" @click="onClose">
                知道了
            </div>
        </div>
    </van-popup>
</template>

<script>
import PlanDetail from "./PlanDetail";
import RateTable from "./RateTable";
import ImagePage from "./ImagePage";
import Policy_Customer from "./Policy/Policy_Customer";

export default {
    name: "BTPopup",
    components: {
        ImagePage,
        RateTable,
        PlanDetail,
        Policy_Customer,
    },
    props: {
        type: String
    },
    data() {
        return {
            pageName: '',
            policy: {},
            onVisible: false,
        }
    },
    methods: {
        onShow(page, policy) {
            // console.log('打开页面onShow =>', page);
            this.pageName = page;
            this.policy = policy;
            this.onVisible = true;

            setTimeout(this.setTop, 0);
        },
        onClose() {
            this.onVisible = false;
        },
        setTop() {
            document.getElementById('popup-page').scrollTop = 0
        }
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2301041717 {
    width: 3.75rem;
    height: 80%;
    left: calc((100% - 3.75rem) * 0.5);

    .header {
        height: 0.5rem;
        width: 92%;
        margin: auto 4%;
        line-height: 0.5rem;
        color: #333333;
        font-size: 0.17rem;
        font-weight: 700;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &::after {
            content: "";
            position: absolute;
            top: 0.17rem;
            right: 0.15rem;
            height: 0.16rem;
            width: 0.16rem;
            background: url("../../../../../assets/imgs/common/sprites.png") no-repeat -1.12rem 0;
            background-size: 1.6rem 3.2rem;
        }
    }

    .content {
        overflow: auto;
        height: calc(100% - 1.1rem);
    }

    .footer {
        position: fixed;
        bottom: 0;
        width: 3.75rem;
        background-color: #FFFFFF;
        border-top: #F2F2F2 1px solid;

        .button {
            margin: 0.08rem 0.2rem;
            height: 0.44rem;
            line-height: 0.44rem;
            border-radius: 0.22rem;
            font-size: 0.16rem;
            font-weight: 500;
            text-align: center;
            color: #FFFFFF;
            background: linear-gradient(to right, #FFCC00, #FF9500);
        }
    }
}

</style>
