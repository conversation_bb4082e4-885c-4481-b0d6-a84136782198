<template>
    <div class="question">
        <div class="header">
            <p class="title">常见问题</p>
            <!-- <p v-if="userFAQ.length > 3" class="more" @click="moreQuestion">更多问题</p> -->
        </div>
        <div class="list">
            <div class="item" v-for="(item,index) in questions" :key="index">
                <div class="title" :class="{expand:expandIdx == index}" @click="expandItem(index)">{{ item.q }}</div>
                <div class="content" v-if="expandIdx == index" v-html="item.a"></div>
            </div>
        </div>
    </div>
</template>

<script>
import {userFAQ,} from "../src";

export default {
    name: "AskedQuestion",
    data() {
        return {
            expandIdx: -1,
        }
    },
    computed: {
        userFAQ() {
            return userFAQ;
        },
        questions() {
            return this.userFAQ;
        },
    },
    methods: {
        moreQuestion() {
            this.$emit('click');
        },
        expandItem(index) {
            this.expandIdx = this.expandIdx == index ? -1 : index;
        },
    },
}
</script>

<style scoped type="text/less" lang="less">
.question {
    background-color: #FFFFFF;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        height: 0.40rem;
        padding: 0.05rem 0.15rem 0;
        border-bottom: #F2F2F2 1px solid;

        .title {
            color: #464646;
            font-size: 0.18rem;
            font-weight: 700;
        }

        .more {
            font-size: 0.14rem;
            //font-weight: 500;
            color: #FF4509;
        }
    }

    .list {
        .item {
            border-bottom: #F2F2F2 1px dashed;

            &:last-child {
                border-bottom: 0;
            }

            .title {
                position: relative;
                padding: 0 0 0 0.35rem;
                font-size: 0.15rem;
                line-height: 0.4rem;
                color: #333333;

                &::before {
                    content: "";
                    position: absolute;
                    top: 0.12rem;
                    left: 0.15rem;
                    height: 0.16rem;
                    width: 0.16rem;
                    background: url("../../../../../assets/imgs/common/sprites.png") no-repeat;
                    background-size: 1.6rem 3.2rem;
                }

                &::after {
                    content: "";
                    position: absolute;
                    top: 0.16rem;
                    right: 0.15rem;
                    height: 0.08rem;
                    width: 0.08rem;
                    background: url("../../../../../assets/imgs/common/sprites.png") no-repeat -0.08rem -0.08rem;
                    background-size: 0.8rem 1.6rem;
                }

                &.expand {
                    &::after {
                        background: url("../../../../../assets/imgs/common/sprites.png") no-repeat 0 -0.08rem;
                        background-size: 0.8rem 1.6rem;
                    }
                }
            }

            .content {
                position: relative;
                margin: 0.12rem 0.15rem;
                padding-left: 0.2rem;
                font-size: 0.13rem;
                line-height: 0.18rem;
                color: #888888;
                text-align: justify;

                &::before {
                    content: "";
                    position: absolute;
                    top: 0rem;
                    left: 0rem;
                    height: 0.16rem;
                    width: 0.16rem;
                    background: url("../../../../../assets/imgs/common/sprites.png") no-repeat -0.16rem 0;
                    background-size: 1.6rem 3.2rem;
                }
            }
        }
    }
}

</style>
