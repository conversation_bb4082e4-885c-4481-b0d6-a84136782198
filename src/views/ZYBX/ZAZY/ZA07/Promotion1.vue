<template>
    <div class="promotion">
        <div class="header">
            <p>众惠随心百万医疗</p>
            <div>
                <span>{{ minutes }}</span>:
                <span>{{ seconds }}</span>后订单失效，请尽快完成支付
            </div>
        </div>
        <div class="section">
            <div class="flexRow section-header">保障计划</div>
            <div v-for="(item,index) in planSummary.slice(0,3)" :key="index" class="flexRow section-item">
                <div>{{ item.key }}</div>
                <div>{{ item.value }}</div>
            </div>
        </div>
        <div v-for="(section,index) in order" :key="index" class="section">
            <div class="flexRow section-header">
                <span>{{ section.title }}</span>
                <span @click="onEdit">编辑</span>
            </div>
            <div v-for="(val,key,idx) in section.content" :key="idx" class="flexRow section-item">
                <span>{{ key }}</span>
                <span>{{ val }}</span>
            </div>
        </div>
        <div class="premium">月缴更轻松，每月0.99元</div>
        <div class="policyField">
                 <span :class="{active:orderInfo.isChecked}" class="normal checkBox"
                       @click="orderInfo.isChecked = !orderInfo.isChecked">
                     我已阅读并同意
                     <span class="read" @click.stop="lookupTerm('健康告知',true)">《健康告知》</span>
                    <span class="read" @click.stop="lookupTerm('免责说明',true)">《免责说明》</span>
                    <span class="read" @click.stop="lookupTerm('重要提示',true)">《重要提示》</span>
                    <span class="read" @click.stop="lookupTerm('投保须知',true)">《投保须知》</span>
                    <span class="read" @click.stop="lookupTerm('投保声明',true)">《投保声明》</span>
                    <span class="read" @click.stop="lookupTerm('条款及费率',true)">《条款及费率》</span>
                    <!-- <span class="read" @click.stop="lookupTerm('特别约定',true)">《特别约定》</span> -->
                    <span class="read" @click.stop="lookupTerm('个人信息保护政策',true)">《个人信息保护政策》</span>
                    <span class="read" @click.stop="lookupTerm('客户告知书',true)">《客户告知书》</span>
                    <span class="read" @click.stop="lookupTerm('经纪委托合同',true)">《经纪委托合同》</span>
                    <span class="read" @click.stop="lookupTerm('委托代扣授权书',true)">《委托代扣授权书》</span>
                    <span class="read" @click.stop="lookupTerm('被保人同意声明',true)">《被保人同意声明》</span>
                 </span>
            <br>
            <!-- <span class="normal">保费与被保人年龄、医保情况相关，详情请见
                            <span class="read" @click.stop="lookupTerm('费率表',true)">《费率表》</span>
                </span> -->
        </div>
        <div id="支付按钮" class="bottom-button" @click="onSubmit">立即支付</div>
        <BTPopup ref="BTPopupRef"></BTPopup>
        <TabPopup ref="TabPopupRef" @confirm="confirmTerms" @seePolicy="seePolicy"></TabPopup>
        <!--录屏-->
        <ZALYRecord @SetIseeBiz="SetIseeBiz"></ZALYRecord>
    </div>
</template>

<script>
import moment from 'moment';
import {fetchPromoteOrderInfo} from "@/api/insurance-api";
import {Indicator} from "mint-ui";
import ZALYRecord from "@/views/components/ZALYRecord";
import TabPopup from "./components/TabPopup";
import BTPopup from "./components/BTPopup";
import {eventTracking, msgToast, premiumCalculation, submitPromoteOrderInfo} from "./function";
import {TraceLogInfoKeys, url_safe_b64_encode} from "@/assets/js/common";
import {bxStorage,} from "@/utils/store_util";
import {planSummary} from "./src";
import { domainPathMap, } from "@/views/ZYBX/src";

export default {
    name: "Promotion",
    components: {ZALYRecord, TabPopup, BTPopup},
    data() {
        const orderInfo = {
            isChecked: true,
            source: 'promote',
            action: 'promote',
            productKey: TraceLogInfoKeys.yb_zh_cube_base,
            identifier: 'ZAZY07Promotion1',
            insurance: 1,   // 有无社保
            repay: 1,       // 缴费方式
            channel: '1',
            mTel: '',       // 加密手机号
            infoNo: '',     // 订单号
            totalPremium: 0,
            upgradePremium: 0,
            holderName: '',
            holderIdCard: '',
            holderPhone: '',
            insuredName: '',
            insuredIdCard: '',
            traceBackUuid: '',
        };

        return {
            planSummary: planSummary.slice(0, 8),
            countDown: 0,
            intervalTimer: 0,
            orderInfo: orderInfo,
        }
    },
    computed: {
        minutes() {
            const minutes = Math.floor(this.countDown / 60);
            return String(minutes).padStart(2, '0');
        },
        seconds() {
            const seconds = this.countDown % 60;
            return String(seconds).padStart(2, '0');
        },
        order() {
            return [
                {
                    title: '投保人',
                    content: {
                        姓名: this.orderInfo.holderName,
                        身份证: this.orderInfo.holderIdCard,
                        手机号: this.orderInfo.holderPhone,
                    }
                }, {
                    title: '被保人',
                    content: {
                        姓名: this.orderInfo.insuredName,
                        身份证: this.orderInfo.insuredIdCard,
                        有无社保: this.orderInfo.insurance == 1 ? '有' : '无',
                    }
                },
                // {
                //     title: '保费信息',
                //     content: this.orderInfo.repay == 1 ? {缴费方式: '按月缴费(12期)'} : {缴费方式: '年缴'}
                // }
            ];
        },
        orderPage() {
            const {productKey} = this.orderInfo;
            return `infoKey:${productKey}&page:promotion`;
        },
    },
    created() {
        this.init();
    },
    beforeDestroy() {
        this.intervalTimer && clearInterval(this.intervalTimer);
    },
    methods: {
        SetIseeBiz(value) {
            // console.log('回溯进来了===',value)
            this.orderInfo.traceBackUuid = value;
        },

        saveExpirationTime(time) {
            bxStorage.setRawItem('ZAZY07Expiration1', time);
        },

        loadExpirationTime() {
            return bxStorage.getRawItem('ZAZY07Expiration1');
        },

        //初始化
        init() {
            const inQry = this.$route.query || {}; // 必须：加密的infoNo
            const {channel, infoNo} = inQry;
            this.orderInfo.channel = channel || '1';
            this.orderInfo.expirationTime = this.loadExpirationTime();

            this.fetchOrderInfo(infoNo);
        },

        fetchOrderInfo(infoNo) {
            if (!infoNo) {
                this._uploadPagePerformance();
                return this.editOrderInfo(true);
            }

            Indicator.open({
                text: '正在请求数据\n请稍候',
                spinnerType: 'fading-circle'
            });

            fetchPromoteOrderInfo(infoNo).then(res => {
                const data = res.data || {};
                this.orderInfoHandle(data);
            }).catch(err => {
                this.editOrderInfo(true);
            }).finally(() => {
                this._uploadPagePerformance();
                Indicator.close();
            });
        },

        orderInfoHandle(data) {
            Object.assign(this.orderInfo, data);

            this.orderInfo.repay = 1;
            const {operatorPhone, insuredIdCard, insurance, repay,} = this.orderInfo;
            this.orderInfo.mTel = operatorPhone;
            this.orderInfo.totalPremium = premiumCalculation(insuredIdCard, insurance, repay);

            if (this.orderInfo.infoKey.endsWith('_upgrade')) {
                return this.pushToResult();
            }

            this.setExpirationTime();
        },

        setExpirationTime() {
            let expirationTime = this.orderInfo.expirationTime || '2019-09-02 00:00:00';
            this.countDown = Math.floor((moment(expirationTime, 'YYYY-MM-DD HH:mm:ss') - moment()) / 1000);
            if (this.countDown < 60) {
                expirationTime = moment(Date.now() + 3599 * 1000).format('YYYY-MM-DD HH:mm:ss');
                this.countDown = 3599;
            }
            this.orderInfo.expirationTime = expirationTime;
            this.saveExpirationTime(expirationTime);

            this.intervalTimer = setInterval(() => {
                this.countDown--;
                if (this.countDown < 0) {
                    this.countDown = 3599;
                    this.orderInfo.expirationTime = moment(Date.now() + 3599 * 1000).format('YYYY-MM-DD HH:mm:ss');
                    this.saveExpirationTime(this.orderInfo.expirationTime);
                }
            }, 1000);
        },

        confirmTerms(isSubmit) {
            this.orderInfo.isChecked = true;
            if (isSubmit) {
                this.onSubmit(true);
            }
        },

        seePolicy(policy) {
            this.$refs.BTPopupRef && this.$refs.BTPopupRef.onShow(policy.page, policy);
        },

        lookupTerm(val, isSubmit) {
            if (['费率表', '常见问题', '保障详情'].includes(val)) {
                this.$refs.BTPopupRef && this.$refs.BTPopupRef.onShow(val);
            } else {
                this.$refs.TabPopupRef && this.$refs.TabPopupRef.onShow(val, isSubmit);
            }
        },

        onEdit() {
            this._actionTracking('点击编辑按钮');
            this.editOrderInfo();
        },

        editOrderInfo(delay, msg) {
            const { channel, mTel } = this.orderInfo;
            const params = { channel, mTel, action:'promotion', source: 'promotion' }
            const bizParams = url_safe_b64_encode(JSON.stringify(params));
			const href = domainPathMap['ZAZY07Index1'] + `?bizParams=${bizParams}`;

			if (!delay) {
				return setTimeout(() => {
					return window.location.href = href;
				}, 250);
			}

            const message = (msg || '').indexOf('已') >= 0 ? '此保单已支付，您可以投保其它产品' : msg;
            msgToast(message || '该订单无效，正在跳转到投保页');

            setTimeout(() => {
                return window.location.href = href;
            }, 2000);
        },

        onSubmit(errNotice) {
            const {infoNo, channel, isChecked} = this.orderInfo;
            if (!isChecked) {
                return this.lookupTerm('健康告知', true);
            }
            this._actionTracking('点击促活立即支付按钮');

            const params = {infoNo: infoNo, page: this.orderPage, channelId: channel};
            submitPromoteOrderInfo(params, this.orderInfo).then(url => {

            }).catch(err => {
                const {reason, msg} = err;
                const message = msg || '';
                this.editOrderInfo(true, message);
            });
        },

        pushToResult() {
            setTimeout(() => {
                const { channel, mTel, infoNo } = this.orderInfo;
                const bizParams = url_safe_b64_encode(JSON.stringify({ channel, mTel, infoNo }));
                const href = window.location.href.replace(/\/Promotion.*/, '/Result') + `?bizParams=${bizParams}`;
                window.location.href = href;
            }, 250);
        },

        _uploadPagePerformance() {
            const {timing} = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const {domContentLoadedEventEnd, fetchStart} = timing || {};
            this._actionTracking('支付促活页', domContentLoadedEventEnd - fetchStart);
        },

        _actionTracking(name, time = 0) {
            eventTracking(this.orderInfo, name, time);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.promotion {
    padding-bottom: 0.50rem;
    width: 3.75rem;
    min-height: 100%;
    font-size: 0.16rem;
    background-color: #F2F2F2;

    .flexRow {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between
    }


    .header {
        padding-left: 0.15rem;
        height: 0.8rem;
        background: url("../../../../assets/imgs/ZhongAn/promotion1.png") no-repeat;
        background-size: 100%;

        p {
            color: #FFFFFF;
            line-height: 0.5rem;
            font-size: 0.20rem;
            font-weight: 700;
        }

        div {
            display: flex;
            align-items: center;

            color: #FFFFFF;
            line-height: 0.2rem;
            font-size: 0.14rem;
            font-weight: 700;

            span {
                width: 0.25rem;
                height: 0.2rem;
                text-align: center;
                border-radius: 0.04rem;
                background-color: #FD5B02;
            }
        }
    }

    .plan {
        display: block;
        margin: 0 auto;
        width: 3.50rem;
    }

    .section {
        margin-bottom: 0.1rem;
        background-color: #FFFFFF;

        .section-header {
            padding: 0 0.15rem;
            height: 0.45rem;
            font-size: 0.17rem;
            font-weight: 700;

            span:nth-child(2) {
                color: #0B78FF;
            }
        }

        .section-item {
            padding: 0 0.15rem;
            height: 0.35rem;
            line-height: 0.35rem;
            font-size: 0.15rem;
            font-weight: 500;
            color: #666666;

            span:nth-child(2) {
                color: #191919;
                font-size: 0.16rem;
                font-weight: 500;
            }

            div:nth-child(2) {
                color: #FD8A25;
                font-weight: bold;
            }
        }
    }

    .premium {
        margin: 0.15rem auto 0.05rem;
        color: #333333;
        //font-weight: 500;
        font-size: 0.14rem;
        text-align: center;

        .number {
            font-size: 0.16rem;
            //font-weight: 700;
            color: #FF491D;
        }
    }

    .policyField {
        padding: 0.08rem;
        border-radius: 0.06rem;
        //background-color: #FFF7F7;

        .normal {
            font-size: 0.13rem;
            color: #333333;
            line-height: 0.2rem;
            text-align: justify;
        }

        .checkBox {
            position: relative;
            padding-left: 0.2rem;

            &.active {
                &::before {
                    background: url("../../../../assets/imgs/common/sprites.png") no-repeat -0.32rem 0;
                    background-size: 1.6rem 3.2rem;
                }
            }

            &::before {
                content: "";
                position: absolute;
                top: 0rem;
                left: 0rem;
                height: 0.16rem;
                width: 0.16rem;
                background: url("../../../../assets/imgs/common/sprites.png") no-repeat -0.48rem 0;
                background-size: 1.6rem 3.2rem;
            }
        }

        .read {
            color: #FF8C41;
            font-weight: 500;
        }
    }

    .bottom-button {
        position: fixed;
        bottom: 0;
        width: 3.75rem;

        color: #FFFFFF;
        height: 0.5rem;
        line-height: 0.5rem;
        font-size: 0.17rem;
        font-weight: 700;
        text-align: center;
        background-color: #FD8A25;
    }
}
</style>
