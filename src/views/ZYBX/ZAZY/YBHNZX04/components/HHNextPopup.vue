<template>
	<van-popup v-model="obj.visible" :close-on-click-overlay="false" class="container_2210121530" position="center">
		<div class="content">
			<img :src="popupImg" class="img">
			<div class="button" @click="okAction">
				立即领取<span v-if="timerId">（{{ countdown }}s）</span>
			</div>
		</div>
	</van-popup>
</template>

<script>
import img05 from "@/assets/imgs/common/popup600_5.png"

export default {
	name: "HHNextPopup",
	props: { obj: Object },
	data() {
		return {
			imgList: { img05 },
			timerId: null,
			countdown: 3,
		}
	},
	computed: {
		popupImg() {
			return this.imgList.img05;
		}
	},
	watch: {
		'obj.visible': {
			handler() {
				if (!this.obj.visible || !this.obj.showTimer) return;

				this.countdown = 3;
				this.timerId = setInterval(() => {
					if (this.countdown > 1) {
						return this.countdown--
					}
					this.okAction();
				}, 1000);
			}
		},
	},
	beforeDestroy() {
		this.clearTimer();
	},
	methods: {
		okAction() {
			this.clearTimer();
			this.obj.visible = false;
			this.$emit('click');
		},
		clearTimer() {
			this.timerId && clearInterval(this.timerId);
			this.timerId = null;
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2210121530 {
		width: 3.5rem;
		background-color: unset;

		.content {
			position: relative;

			.img {
				width: 100%;
				display: block;
			}

			.button {
				position: absolute;
				width: 2.2rem;
				height: 0.44rem;
				line-height: 0.44rem;
				border-radius: 0.22rem;
				text-align: center;
				background-color: #fd8a25;
				font-size: 0.16rem;
				color: #ffffff;
				bottom: 0.35rem;
				left: 50%;
				transform: translateX(-50%);
			}
		}
	}
</style>


