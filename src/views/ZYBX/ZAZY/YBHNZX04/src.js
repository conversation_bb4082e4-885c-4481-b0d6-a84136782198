import { Toast } from "vant";
import { isPhoneNum, TraceLogInfoKeys } from "@/assets/js/common";
import { actionTracking } from "@/assets/js/api";
import { bxStorage } from "@/utils/store_util";

export const orderInfo = {
    productKey: TraceLogInfoKeys.yb_hr_send,
    identifier: "",
    received: 0, // 是否已领取过
    hintVisible: true,
    checked: true,
    step: "step1",
    relation: 1,
    infoNo: "",
    m: "",
    tel: "",
    mTel: "",
    channel: "1",
    channelCode: "",
    sourcePage: "",
    starPhone: "",
    phoneNo: "",
    name1: "",
    idCard1: "",
    traceBackUuid: "",
    utmId: "",
    nsfCode: 1, // NSF评分
    city: '',
    province: '',
    ip: '',
    encryptNumber: '',
};

export const documentList = [
    { page: "客户告知书" },
    {
        page: "投保须知",
        type: 1,
        list: [
            "众惠意外投保须知",
        ],
    },
    {
        page: "免责条款",
        type: 1,
        list: [
            "众惠意外免责条款",
        ],
    },
    // { page: "健康告知" },
    {
        page: "保险条款",
        type: 1,
        list: [
            '众惠交通工具意外伤害保险B款（互联网专属）条款',
            '众惠附加保险事故限制特约保险条款',
            "个人信息保护政策",
            "众安经纪客户告知书",
            "经纪委托合同",
        ],
    },
    {
        page: "投保声明",
    },
    {
        page: "特别约定",
        type: 1,
        list: [
            "众惠意外特别约定",
        ],
    },
];

export const pdfFileObj = {
    "客户告知书": "/ZAZY/YBZHZX01/khgzs_ks.pdf",
    "众安经纪客户告知书": "/ZAZY/YBZHZX01/khgzs_za.pdf",
    "经纪委托合同": "/ZAZY/YBZHZX01/jjwtxy.pdf",
    "理赔流程": "/ZAZY/YBZHZX01/lplc.pdf",
    "个人信息保护政策": "/ZAZY/YBZHZX01/grxxbhzc.pdf",

    // 国任重疾
    "健康告知": "/ZAZY/YBZHZX01/jkgz_v1.pdf",
    "国任重疾投保须知": "/ZAZY/YBZHZX01/tbxz_v1.pdf",
    "国任重疾特别约定": "/ZAZY/YBZHZX01/tbyd_v1.pdf",
    "国任重疾免责条款": "/ZAZY/YBZHZX01/zrmc_v1.pdf",
    "国任个人重大疾病保险（互联网专属A款）条款": "/ZAZY/YBZHZX01/dir1.pdf",

    // 众惠意外
    "众惠意外重要提示": "/ZAZY/YBZHZX01/zyts_v2.pdf",
    "众惠意外投保须知": "/ZAZY/YBZHZX01/tbxz_v2.pdf",
    "投保声明": "/ZAZY/YBZHZX01/tbsm_v2.pdf",
    "众惠意外特别约定": "/ZAZY/YBZHZX01/tbyd_v2.pdf",
    "众惠意外免责条款": "/ZAZY/YBZHZX01/zrmc_v2.pdf",
    "众惠交通工具意外伤害保险B款（互联网专属）条款": "/ZAZY/YBZHZX01/dir2.pdf",
    "众惠附加保险事故限制特约保险条款": "/ZAZY/YBZHZX01/dir3.pdf",
};

export const policyList = [
    {
        name: '众惠交通工具意外伤害保险B款（互联网专属）条款',
        title: '主条款',
        // extra: 'C00022132312023080222721'
    },
    {
        name: '众惠附加保险事故限制特约保险条款',
        title: '附加条款',
        // extra: 'C00022131922021122224573'
    },
];


export const eventTracking = (name, time = 0) => {
    const { productKey, mTel, tel, channel, phoneNo, identifier, encryptNumber } = orderInfo;
    const phone = mTel || tel || (isPhoneNum(phoneNo) ? phoneNo : "");

    actionTracking({
        page: `优保交通赠险聚合${identifier}(${productKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: productKey,
        time: time,
        volcEncryptPhone: encryptNumber || undefined,
    }).then((res) => {
        const { code, data } = res;
        if (code == 2000) {
            const { mobileId } = data || {};
            if (mobileId) {
                orderInfo.mTel = mobileId;
            }
        }
    });
};

const showToast = (message = "", duration = 2000) => {
    Toast({
        forbidClick: true,
        message: message,
        position: "center",
        duration: duration, // 弹窗时间毫秒
    });
};

export const toastAndAction = (v = 0, message = "", duration = 2000) => {
    // 1 toast(1) 2输入框位置调整(1<<1)
    if (v & 0b1) {
        showToast(message, duration);
    }

    if (v & 0b10) {
        // 输入框居中
        setTimeout(() => {
            const node = document.getElementById("input_x_2302131855");
            node && node.scrollIntoView && node.scrollIntoView({ block: "center" });
        }, 500);
    }
};

export const loadOrderInfo = () => {
    return bxStorage.getObjItem("YBHNZX01_INFO") || {};
};

export const saveOrderInfo = () => {
    bxStorage.setObjItem("YBHNZX01_INFO", orderInfo);
};
