<template>
    <div class="container_2211011700">
<!--        <div class="time">-->
<!--            <van-icon name="clock-o"/>-->
<!--            工作日：9:00-18:00-->
<!--        </div>-->
        <div v-for="(item,index) in claimProcess" :key="index" class="process-item">
            <div class="process-item-title">{{ item.name }}</div>
            <div class="process-item-content">{{ item.text }}</div>
        </div>
    </div>
</template>

<script>
import {claimProcess} from "../src";

export default {
    name: "ClaimProcess",
    data() {
        return {
            claimProcess,
        }
    },
    methods: {},
}
</script>

<style lang="less" scoped type="text/less">

.container_2211011700 {
    color: #333333;
    font-size: 0.14rem;
    background-color: #FFFFFF;

    .header {
        padding: 0.12rem;
        font-size: 0.18rem;
        font-weight: 500;
    }

    .time {
        padding: 0.15rem 0 0.15rem 0.15rem;
        color: #FE5243;
        font-weight: 500;
    }

    .process-item {
        padding: 0 0.15rem 0.15rem;

        .process-item-title {
            display: flex;
            align-items: center;
            line-height: 0.20rem;
            font-weight: 600;

            &::before {
                content: " ";
                margin-right: 0.05rem;
                height: 0.08rem;
                width: 0.08rem;
                border-radius: 50%;
                background-color: #FE5243;
            }
        }

        .process-item-content {
            padding: 0.06rem 0.15rem 0;
            line-height: 1.5;
            text-align: justify;
            color: #888888;
        }
    }
}

</style>

