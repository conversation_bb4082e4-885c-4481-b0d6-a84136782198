<template>
    <div class="container_2304251150">
        <div class="header">
            <div class="item" v-for="(val,key) in planPoints" :key="key">
                <span class="text">{{ key }}</span>
                <span class="detailText">{{ val }}</span>
            </div>
        </div>

        <div class="content">
            <div class="content-item" v-for="(item) in planDetails" :key="item.name">
                <div class="content-item-header">
                    <span class="content-item-header-key" v-html="item.name"></span>
                    <span class="content-item-header-value">{{ item.val }}</span>
                </div>
                <div class="content-item-detail" v-html="item.text"></div>
            </div>
        </div>
    </div>
</template>

<script>
import {planPoints, planDetails, planPoints1, planDetails1} from '../src';

export default {
    props: {type: String},
    data() {
        return {}
    },
    computed: {
        planPoints() {
            return this.type == 'upgrade' ? planPoints1 : planPoints;
        },
        planDetails() {
            return this.type == 'upgrade' ? planDetails1 : planDetails;
        }
    },
    methods: {}
}
</script>

<style scoped type="text/less" lang="less">

.container_2304251150 {
    background-color: #FFFFFF;

    .header {
        .item {
            display: flex;
            align-items: center;
            padding: 0.12rem 0.15rem;
            font-size: 0.14rem;
            line-height: 1.50;
            color: #333333;
            border-bottom: #F2F2F2 1px solid;

            .text {
                font-weight: 500;
            }

            .detailText {
                flex: 1;
                text-align: right;
            }
        }
    }

    .content {

        .content-item {
            margin-bottom: 0.2rem;

            .content-item-header {
                display: flex;
                align-items: center;
                margin: 0.12rem 0.15rem;
                padding-left: 0.10rem;
                font-size: 0.15rem;
                font-weight: 700;
                color: #333333;
                border-left: #FE5243 0.04rem solid;

                .content-item-header-key {
                    flex: 1;
                }

                .content-item-header-value {
                    margin-left: 0.10rem;
                    flex-shrink: 0;
                    color: #FE5243;
                }
            }

            .content-item-detail {
                padding: 0 0.15rem;
                text-align: justify;
                font-size: 0.13rem;
                line-height: 1.5;
                color: #666666;
            }
        }
    }
}
</style>
