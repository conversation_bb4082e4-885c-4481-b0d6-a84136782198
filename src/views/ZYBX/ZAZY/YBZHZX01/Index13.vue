<template>
    <div class="container_2302131350">
        <!-- <audio id="id_audio_2310081000" preload="auto" loop>
            <source
                src="https://cdns.bountech.com/marketfront/file/insurance/cdn/picture/ZYBX/YB/YBHNZX03/audio/intro.mp3"
                type="audio/mp3" />
        </audio> -->
        <div class="banner">
            <img src="@/assets/imgs/ZAZY/YBHNZX03/header_1.png">
            <div class="logo-x">
                <img v-if="!isGRHidden1" class="logo logo-zh" src="@/assets/imgs/logo/guoren_2.png">
                <img v-if="!isKSHidden1" class="logo logo-ks" src="@/assets/imgs/logo/kaisen_1.png">
            </div>
            <van-notice-bar :scrollable="false" class="notice-bar">
                <van-swipe :autoplay="2000" :show-indicators="false" class="notice-swipe" vertical>
                    <van-swipe-item v-for="(notice, idx) in noticeList" :key="idx">尾号{{ notice }}刚刚已领取</van-swipe-item>
                </van-swipe>
            </van-notice-bar>
        </div>
        <HHInputBox1 :obj="orderInfo" class="input-x" @focus="onTextFocus" @input="onTextInput" @submit="submit"
            @onViewPolicy="onViewPolicy"></HHInputBox1>
        <div class="policy-x">
            <van-icon class="policy-icon" :name="orderInfo.checked ? 'checked' : 'circle'"
                @click="orderInfo.checked = !orderInfo.checked" />
            我已阅读并同意
            <span class="policy-txt" @click="onViewPolicy('客户告知书')">《客户告知书》</span>
            <span class="policy-txt" @click="onViewPolicy('投保须知')">《投保须知》</span>
            <span class="policy-txt" @click="onViewPolicy('免责条款')">《免责条款》</span>
            <span class="policy-txt" @click="onViewPolicy('健康告知')">《健康告知》</span>
            <span class="policy-txt" @click="onViewPolicy('保险条款')">《保险条款》</span>
            <span class="policy-txt" @click="onViewPolicy('投保声明')">《投保声明》</span>
            <span class="policy-txt" @click="onViewPolicy('特别约定')">《特别约定》</span>
            <span class="policy-txt" @click="onViewPolicy('个人信息保护政策')">《个人信息保护政策》</span>
            ，您同意您在赠险页面所填写的信息可经凯森给到其他凯森合作机构用于优化您的投保流程。
        </div>
        <van-swipe class="detail-box" indicator-color="#FE4F3B">
            <van-swipe-item><img alt="" src="@/assets/imgs/ZAZY/YBZHZX01/img11.png"></van-swipe-item>
            <van-swipe-item><img alt="" src="@/assets/imgs/ZAZY/YBZHZX01/img12.png"></van-swipe-item>
        </van-swipe>
        <div class="introduce-x">
            <img alt="活动规则" src="@/assets/imgs/ZAZY/YBHNZX03/img02.png">
        </div>
        <HHPolicyWrapper @view="onViewDetail"></HHPolicyWrapper>
        <div class="copyright">
            由陕西凯森保险代理有限公司提供服务<br>
            陕ICP备13000203号-7
        </div>
        <div v-if="jumpObj.bottomVisible" class="bottom-button" @click="submit">
            <img alt="投保" class="hand" src="@/assets/imgs/common/icon_hand2.png">
        </div>
        <van-popup v-model="isMask" class="mask" :close-on-click-overlay="false">
            <img src="@/assets/imgs/common/icon_browser.png" />
        </van-popup>
        <HHTabViewer :obj="policyObj" @ok="checkPolicy" @view="onViewDetail"></HHTabViewer>
        <HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
        <HHPrevPopup :obj="jumpObj" @click="jumpPrevLink"></HHPrevPopup>
        <HHNextPopup :obj="jumpObj" @click="jumpNextLink"></HHNextPopup>
        <HHWarnHint :obj="orderInfo" @view="onViewDetail"></HHWarnHint>
        <!--录屏-->
        <ZALYRecord1 page="优保众惠聚合赠险YBZHZX01Index13" @SetIseeBiz="SetIseeBiz"></ZALYRecord1>
    </div>
</template>

<script>
import ZALYRecord1 from "@/views/components/ZALYRecord1";
import HHInputBox1 from "./components/HHInputBox2";
import HHPolicyViewer from "./components/HHPolicyViewer";
import HHPrevPopup from "./components/HHPrevPopup";
import HHNextPopup from "./components/HHNextPopup";
import HHTabViewer from "./components/HHTabViewer";
import HHPolicyWrapper from "./components/HHPolicyWrapper";
import HHWarnHint from "./components/HHWarnHint";
import { domainPathMap, is_server_phone } from "@/views/ZYBX/src";
import {
    GetAge,
    isAndroid,
    isCardNo,
    isInWx,
    isMaskedAndT1Phone,
    isPersonName,
    isPhoneNum,
    reviseIdCard,
    reviseName,
    TraceLogInfoKeys,
    Url,
    url_safe_b64_encode,
} from "@/assets/js/common";
import {
    createMultiFreeOrderForZA,
    fetchNsfInfo,
    fetchRoundRobinWithAgeZXResultForZA,
    fetchStarPhoneV4,
    fetchAddressByIP,
    bindPhoneAndPrivacy,
} from '@/api/insurance-api';
import { eventTracking, loadOrderInfo, orderInfo, saveOrderInfo, toastAndAction, domainTracking } from './src';
import { createNoticeList } from './function'

export default {
    name: "Index13",
    data() {
        return {
            isGRHidden1: false,
            isKSHidden1: false,
            prevName: '',
            orderInfo,
            isMask: false,
            prevIsRequesting: false,
            noticeList: [],
            policyObj: { visible: false, page: '', visible1: false, page1: '', },
            jumpObj: {
                visible: false,
                visible1: false,
                type: 0,
                showTimer: true,
                showTimer1: false,
                path: '',
                path1: '',
                bottomVisible: false
            },
        };
    },
    components: {
        HHWarnHint,
        HHTabViewer,
        HHNextPopup,
        HHInputBox1,
        HHPolicyViewer,
        HHPrevPopup,
        HHPolicyWrapper,
        ZALYRecord1,
    },
    computed: {
        isStep2() {
            return this.orderInfo.step == 'step2';
        }
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.init();

        this.$nextTick(() => {
            this.pushViewHistory();
            this.observeIntersection();
            window.addEventListener("popstate", this.popViewHistory);
        });

        if (Math.random() > 1) {
            this.jumpObj.type = 1;
        }

        this.noticeList = createNoticeList();

        this.makeAudioWork();
    },
    destroyed() {
        window.removeEventListener("popstate", this.popViewHistory);
    },
    methods: {
        makeAudioWork() {
            const func = () => {
                try {
                    const oAudio = document.querySelector('#id_audio_2310081000');
                    if (oAudio && !oAudio.alreadyPlay) {
                        oAudio.play().then(() => {
                            oAudio.alreadyPlay = true;
                        });
                    }
                } catch (e) {

                }
            };

            window.onclick = func;
            window.ontouchstart = func;
        },
        SetIseeBiz(value) {
            this.orderInfo.traceBackUuid = value;
        },
        // 初始化
        init() {
            const inQry = this.$route.query || {};
            const orderInfo = loadOrderInfo();

            this.isGRHidden1 = inQry.isShow1 != 1;
            this.isKSHidden1 = inQry.isShow2 != 1;

            Object.assign(this.orderInfo, orderInfo);
            this.orderInfo.identifier = 'YBZHZX01Index13';
            this.orderInfo.sourcePage = '优保众惠赠险直投V250417';
            this.orderInfo.channel = inQry.channel || orderInfo.channel || '1';
            this.orderInfo.channelCode = inQry.cld || orderInfo.channelCode || '';
            this.orderInfo.encryptNumber = inQry.encryptNumber || orderInfo.encryptNumber || '';
            this.orderInfo.utmId = '1699001103614';
            this.orderInfo.productKey = TraceLogInfoKeys.cqb_zh_traffic_send;

            if (isInWx() && (inQry.isMask == 1)) {
                if (isAndroid()) {
                    const url = url_safe_b64_encode(window.location.href);
                    setTimeout(() => {
                        window.location.href = `${Url}/Insurance/info/checkIsJump?url=${url}`;
                    }, 500);
                }
                this._entryBehaviorReport();
                this.orderInfo.hintVisible = false;
                this.isMask = true;
                return;
            }

            fetchAddressByIP().then(res => {
                const { city, province, ip } = res.data || {};
                this.orderInfo.city = city || '';
                this.orderInfo.province = province || '';
                this.orderInfo.ip = ip || '';
            });

            if (this.orderInfo.received) {
                this._entryBehaviorReport();
                return this.fetchNsfCode();
            }

            Object.assign(this.orderInfo, inQry);

            this.orderInfo.checked = this.orderInfo.channel >= 1000;

            const { phoneNo, starPhone, mTel } = this.orderInfo;
            if (!isPhoneNum(phoneNo) && isMaskedAndT1Phone(starPhone, mTel)) {
                this.orderInfo.phoneNo = starPhone;
            } else {
                this.orderInfo.starPhone = '';
            }
            if (isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
                this.orderInfo.step = 'step2';
            } else {
                this.orderInfo.step = 'step1';
            }
            this.fetchPhoneNumber();
        },

        fetchPhoneNumber() {
            const { m, phoneNo, starPhone, mTel, channelEnMContent, channelEnMCode } = this.orderInfo;
            if ((!m && !channelEnMContent) || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
                return this._entryBehaviorReport();
            }

            const params = {};
            if (m) {
                params.encryptContent = m;
            } else {
                params.channelEnMContent = channelEnMContent;
                params.channelEnMCode = channelEnMCode;
            }
            fetchStarPhoneV4(params).then(res => {
                const { encryptPhone, showPhone } = res.data || {};
                this.orderInfo.mTel = encryptPhone;
                this.orderInfo.starPhone = showPhone;
                this.orderInfo.phoneNo = showPhone;
                if (isMaskedAndT1Phone(showPhone, encryptPhone)) {
                    this.orderInfo.step = 'step2';
                }
                saveOrderInfo();
            }).finally(() => {
                return this._entryBehaviorReport();
            });
        },

        bindPhoneAction() {
            const { channel, phoneNo, mTel, encryptNumber } = this.orderInfo;
            if (!encryptNumber) {
                return;
            }

            const params = {
                channelId: channel,
                relatePhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                volcEncryptPhone: encryptNumber,
            }

            bindPhoneAndPrivacy(params).finally(() => {

            });
        },

        onViewPolicy(page) {
            this.policyObj.page = page;
            this.policyObj.visible = true;
        },

        onViewDetail(page) {
            this.policyObj.page1 = page;
            this.policyObj.visible1 = true;
        },

        checkPolicy() {
            this.orderInfo.checked = true;
            this.submit();
        },

        onTextInput({ key, value }) {
            let isCompleted = false;
            const channel = this.orderInfo.channel;
            if (key === 'phone' && isPhoneNum(value)) {
                if (is_server_phone(value)) {
                    this.orderInfo.phoneNo = '';
                    return toastAndAction(3, '请输入您本人的手机号码');
                }

                isCompleted = true;
                eventTracking('首页-完成输入手机号');
            } else if (key === 'certNo' && isCardNo(value)) {
                isCompleted = true;
                eventTracking('首页-完成输入身份证');
            } else if (key === 'certName' && isPersonName(value)) {
                isCompleted = true;
                if (this.prevName != value) {
                    this.prevName = value;
                    eventTracking(`首页-完成输入姓名`);
                }
            }
            if (!isCompleted) return;

            saveOrderInfo();
        },

        onTextFocus({ key, value }) {
            const channel = this.orderInfo.channel;

            if (key === 'certNo' && !value) {
                eventTracking('首页-开始输入身份证');
            } else if (key === 'certName' && !value) {
                eventTracking('首页-开始输入姓名');
            }
        },

        submit() {
            this.orderInfo.name1 = reviseName(this.orderInfo.name1);
            this.orderInfo.idCard1 = reviseIdCard(this.orderInfo.idCard1);

            const { name1, phoneNo, idCard1, checked, starPhone, mTel } = this.orderInfo;

            let message = '';

            if (this.isStep2) {
                if (!isPhoneNum(phoneNo) && !isMaskedAndT1Phone(starPhone, mTel)) {
                    message = '请输入正确的手机号';
                }
            } else {
                if (!isPhoneNum(phoneNo) && !(isMaskedAndT1Phone(starPhone, mTel) && phoneNo == starPhone)) {
                    message = '请输入正确的手机号';
                }
            }

            if (message) {
                this.orderInfo.step = 'step1';
                saveOrderInfo();
                return toastAndAction(3, message);
            }

            if (!this.isStep2) {
                this.orderInfo.step = 'step2';
                saveOrderInfo();
                this.bindPhoneAction();
                domainTracking();
                return eventTracking('首页-点击第一步立即领取');
            }

            if (!isPersonName(name1)) {
                message = '请输入正确的姓名';
            } else if (!isCardNo(idCard1)) {
                message = '请输入正确的身份证号码';
            }

            if (message) {
                return toastAndAction(3, message);
            }

            saveOrderInfo();

            if (!checked) {
                return this.onViewPolicy('客户告知书');
            }

            eventTracking('首页-点击第二步立即领取');

            this.submitOrder();

            this.fetchNsfCode();
        },

        fetchNsfCode() {
            const { channel, phoneNo, mTel, productKey } = this.orderInfo;
            const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;

            this.$toast.loading({
                message: '数据处理中\n请稍候',
                position: 'middle',
                forbidClick: true,
                duration: 0,
            });

            const params = {
                phone,
                channelId: channel,
                infoKey: productKey,
                nsfKey: 'zx_to_mf_nsf_key',
            };

            fetchNsfInfo(params).then(res => {
                const { code, data } = res.data || {};
                if (code == 2000) {
                    this.orderInfo.nsfCode = data;
                }
            }).finally(() => {
                this.pushToResult(true);
            });
        },

        submitOrder() {
            const {
                productKey,
                name1,
                idCard1,
                phoneNo,
                relation,
                traceBackUuid,
                sourcePage,
                infoNo,
                channel,
                identifier,
                mTel,
                channelCode,
                utmId
            } = this.orderInfo;

            const extendParams = { utmId };

            const params = {
                infoNo,
                sourcePage,
                traceBackUuid,
                relation,
                channelCode,
                channelId: channel,
                page: identifier,
                holderName: name1,
                holderIdCard: idCard1,
                holderPhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                planKeys: [productKey],
                operatorPhone: mTel || phoneNo,
                // extendParams: JSON.stringify(extendParams),
            };

            createMultiFreeOrderForZA(params).catch(err => {
                console.log(err);
            }).finally(() => {

            });
        },

        pushToResult(isSaved) {
            if (isSaved) {
                this.orderInfo.received = 1;
                saveOrderInfo();
            }

            this.fetchNextJump();
            this.bindPhoneAction();
        },

        fetchNextJump() {
            const { channel, phoneNo, idCard1, mTel, nsfCode, } = this.orderInfo;

            const robinKey = nsfCode == 1 ? 'rxh_zx_to_next' : 'zx_common_to_mf';

            const params = {
                channelId: channel,
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                idCard: idCard1,
                robinKey: robinKey,
            };

            let path = '';
            fetchRoundRobinWithAgeZXResultForZA(params).then(res => {
                path = res.data.path || '';
            }).finally(() => {
                this.$toast.clear(true);

                if (channel < 1000) {
                    path = 'YBGR03Index2';
                }

                if (!domainPathMap[path]) {
                    path = 'YBGR03Index2';
                }

                this.jumpObj.path = path;
                this.jumpObj.visible = true;
            });
        },


        jumpNextLink() {
            if (this.orderInfo.city.indexOf('北京') >= 0 || this.orderInfo.province.indexOf('北京') >= 0) {
                this.jumpObj.path = 'ZAZY07Index1';
            }

            const nextPath = this.jumpObj.path;

            eventTracking(`首页-点击弹框好的马上去(${nextPath})`);

            const {
                channelCode,
                channel,
                identifier,
                sourcePage,
                name1,
                idCard1,
                phoneNo,
                mTel,
                starPhone,
                utmId,
            } = this.orderInfo;

            // 计算年龄
            const relation = GetAge(idCard1) < 18 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: identifier, sourcePage, mTel, utmId, action: 'follow',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            };

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = channel == 101 ? 'http://61.155.210.166/marketfront/insurance/ZYBX/TK37/Index1' : domainPathMap[nextPath];
            setTimeout(() => {
                window.location.href = `${href}?bizParams=${bizParams}`;
            }, 250);
        },

        async fetchPrevJump() {
            if (this.jumpObj.path1) {
                return this.jumpObj.visible1 = true;
            }

            if (this.prevIsRequesting) {
                return;
            }
            this.prevIsRequesting = true;

            const { channel, phoneNo, idCard1, mTel } = this.orderInfo;

            const params = {
                channelId: channel,
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                robinKey: 'rxh_zx_to_back',
            };

            let path = '';

            try {
                let res;

                if (isCardNo(idCard1)) {
                    params.idCard = idCard1;
                }
                res = await fetchRoundRobinWithAgeZXResultForZA(params);
                path = res.data.path || '';
            } catch (e) {
            }

            this.prevIsRequesting = false;

            if (!domainPathMap[path]) {
                path = 'YBGR03Index2';
            }

            this.jumpObj.path1 = path;
            this.jumpObj.visible1 = true;

            eventTracking(`首页-点击返回按钮(${this.jumpObj.path1})-${this.jumpObj.type}`);
        },

        jumpPrevLink() {
            const prevPath = this.jumpObj.path1;

            eventTracking(`首页-点击返回弹框图片(${prevPath})-${this.jumpObj.type}`);

            const {
                channelCode,
                channel,
                identifier,
                sourcePage,
                name1,
                idCard1,
                phoneNo,
                mTel,
                starPhone,
                utmId
            } = this.orderInfo;
            const relation = GetAge(idCard1) < 18 && GetAge(idCard1) > 0 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: identifier, sourcePage, mTel, utmId, action: 'back',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            };

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap[prevPath];
            setTimeout(() => {
                window.location.href = `${href}?bizParams=${bizParams}`;
            }, 250);
        },

        observeIntersection() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries, observer) => {
                    const entry1 = entries.find(v => v.target.id == 'id_action_button');
                    if (entry1) {
                        this.jumpObj.bottomVisible = !entry1.isIntersecting;
                    }
                }, { threshold: 0.10 });

                const node1 = document.getElementById('id_action_button');
                node1 && observer.observe(node1);
            }
        },

        pushViewHistory() {
            const state = { title: "title", url: "#" };
            window.history.pushState(state, "title", "");
        },

        popViewHistory() {
            if (this.isStep2) {
                this.orderInfo.step = 'step1';
                saveOrderInfo();
            } else {
                if (this.orderInfo.channel >= 1000) {
                    this.fetchPrevJump();
                }
            }
            this.pushViewHistory();
        },

        _entryBehaviorReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            eventTracking('首页', domContentLoadedEventEnd - fetchStart);

            domainTracking();

            if (!/^\d+$/.test(this.orderInfo.channel)) {
                this.orderInfo.channel = '333411';
            }
        },
    },
};
</script>

<style lang="less" scoped>
/deep/ .van-swipe__indicators {
    bottom: 4px;
}

/deep/ .van-swipe__indicator {
    background-color: #999999;
}

.container_2302131350 {
    min-height: 100%;
    font-size: 0.15rem;
    background: #f8f8f8;

    img {
        display: block;
        max-width: 100%;
    }

    .banner {
        position: relative;
        width: 3.75rem;

        .logo-x {
            position: absolute;
            left: 0.15rem;
            top: 0.10rem;

            display: flex;
            flex-direction: row;
            align-items: center;

            .logo-zh {
                width: 0.58rem;
            }

            .logo-hr {
                width: 0.74rem;
            }

            .logo-ks {
                width: 0.47rem;
            }

            .logo+.logo {
                margin-left: 0.10rem;
            }
        }

        .notice-bar {
            position: absolute;
            top: 0.10rem;
            right: 0.16rem;
            width: 1.16rem;
            height: 0.2rem;
            padding: 0 0.1rem;
            border-radius: 0.18rem;
            color: rgba(255, 255, 255, 0.8);
            background-color: rgba(255, 255, 255, 0.2);

            .notice-swipe {
                font-size: 0.12rem;
                height: 0.16rem;
                line-height: 0.16rem;
                text-align: center;
            }
        }
    }

    .input-x {
        margin-top: -0.15rem;
    }

    .detail-box {
        margin: 0.15rem auto 0;
        width: 3.45rem;
        // border-radius: 0.07rem;
    }

    .introduce-x {
        margin: 0.15rem 0.15rem 0;
    }

    .policy-x {
        margin: 0.15rem;
        color: #666;
        text-align: justify;
        font-size: 0.12rem;
        line-height: 1.6;

        .policy-icon {
            color: #fe4f3b;
            font-size: 0.16rem;
            vertical-align: -0.01rem;
        }

        .policy-txt {
            color: #fe4f3b;
            font-weight: 500;
        }
    }

    .copyright {
        padding: 0.2rem 0 1.1rem;
        text-align: center;
        font-size: 0.12rem;
        font-weight: 500;
        line-height: 1.8;
        color: #888;

        a {
            color: #888;
            text-decoration: underline;
        }
    }

    .bottom-button {
        position: fixed;
        inset: auto 0 0.25rem 0;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 3.2rem;
        height: 0.52rem;
        margin: 0.2rem auto;
        text-align: center;
        font-size: 0.24rem;
        font-weight: 800;
        line-height: 0.27rem;
        color: #ffffff;
        border-radius: 0.25rem;
        animation: button_animation 1.5s linear infinite;
        background: url("~@/assets/imgs/ZAZY/YBHNZX03/button.gif") no-repeat;
        background-size: contain;

        .hand {
            position: absolute;
            top: 0.2rem;
            left: 75%;
            width: 18%;
            animation: hand_animation 1.25s linear infinite;
        }
    }

    .mask {
        width: 100%;
        height: 100%;
        background-color: unset;

        img {
            display: block;
            width: 3.5rem;
            margin: 0 auto;
        }
    }
}

@keyframes button_animation {
    0% {
        transform: scale(1);
    }

    40% {
        transform: scale(0.95);
    }

    60% {
        transform: scale(0.95);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes hand_animation {
    0% {
        transform: translate(-0.1rem, -0.1rem);
    }

    45% {
        transform: translate(0.1rem, 0.1rem);
    }

    70% {
        transform: translate(0.1rem, 0.1rem);
    }

    100% {
        transform: translate(-0.1rem, -0.1rem);
    }
}
</style>
