import { Toast } from "vant";
import { isPhoneNum, TraceLogInfoKeys, star_marked_phone, AESEncrypt } from "@/assets/js/common";
import { actionTracking } from "@/assets/js/api";
import { bxStorage } from "@/utils/store_util";

export const orderInfo = {
    productKey: TraceLogInfoKeys.cqb_zh_traffic_send,
    identifier: "",
    received: 0, // 是否已领取过
    hintVisible: true,
    checked: true,
    step: "step1",
    relation: 1,
    infoNo: "",
    m: "",
    tel: "",
    mTel: "",
    channel: "1",
    channelCode: "",
    sourcePage: "",
    starPhone: "",
    phoneNo: "",
    name1: "",
    idCard1: "",
    traceBackUuid: "",
    utmId: "",
    nsfCode: 1, // NSF评分
    city: '',
    province: '',
    ip: '',
    encryptNumber: '',
};

export const documentList = [
    { page: "客户告知书" },
    {
        page: "投保须知",
        type: 1,
        list: [
            "国任重疾投保须知",
            "国任百医投保须知",
        ],
    },
    {
        page: "免责条款",
        type: 1,
        list: [
            "国任重疾免责条款",
            "国任百医免责条款",
        ],
    },
    {
        page: "健康告知",
        type: 1,
        list: [
            "国任重疾健康告知",
            "国任百医健康告知",
        ],
    },
    {
        page: "保险条款",
        type: 2,
        list: [
            {
                tabName: "国任重疾条款",
                list: [
                    "国任个人重大疾病保险（互联网专属A款）条款",
                ],
            },
            {
                tabName: "国任百医条款",
                list: [
                    '国任个人医疗保险（互联网专属B款）条款',
                    '国任职业类型表',
                    "众安经纪个人信息保护政策",
                    "众安经纪客户告知书",
                    "经纪委托合同",
                ],
            },
        ],
    },
    {
        page: "投保声明",
    },
    {
        page: "特别约定",
        type: 1,
        list: [
            "国任重疾特别约定",
            "国任百医特别约定",
        ],
    },
    {
        page: "个人信息保护政策",
    },
];

export const pdfFileObj = {
    "客户告知书": "/ZAZY/YBZHZX01/khgzs_ks.pdf",
    "众安经纪客户告知书": "/ZAZY/YBZHZX01/khgzs_za.pdf",
    "经纪委托合同": "/ZAZY/YBZHZX01/jjwtxy.pdf",
    "理赔流程": "/ZAZY/YBZHZX01/lplc.pdf",
    "众安经纪个人信息保护政策": "/ZAZY/YBZHZX01/grxxbhzc.pdf",
    "个人信息保护政策": "/ZAZY/YBZHZX01/ksxxbhzc.pdf",

    // 国任重疾
    "国任重疾健康告知": "/ZAZY/YBZHZX01/jkgz_v1.pdf",
    "国任重疾投保须知": "/ZAZY/YBZHZX01/tbxz_v1.pdf",
    "国任重疾特别约定": "/ZAZY/YBZHZX01/tbyd_v1.pdf",
    "国任重疾免责条款": "/ZAZY/YBZHZX01/zrmc_v1.pdf",
    "国任个人重大疾病保险（互联网专属A款）条款": "/ZAZY/YBZHZX01/dir1.pdf",

     // 国任百医
    "国任百医健康告知": "/ZAZY/YBZHZX01/jkgz_v3.pdf",
    "国任百医投保须知": "/ZAZY/YBZHZX01/tbxz_v3.pdf",
    "国任百医特别约定": "/ZAZY/YBZHZX01/tbyd_v3.pdf",
    "国任百医免责条款": "/ZAZY/YBZHZX01/zrmc_v3.pdf",
    "国任职业类型表": "/ZAZY/YBZHZX01/zylxb_v3.pdf",
    "国任个人医疗保险（互联网专属B款）条款": "/ZAZY/YBZHZX01/dir4.pdf",
    "投保声明": "/ZAZY/YBZHZX01/tbsm_v3.pdf",
};

export const policyList = [
    {
        name: "国任个人重大疾病保险（互联网专属A款）条款",
        title: "主条款",
        // extra: "C00022132612021122225673",
    },
    {
        name: '国任个人医疗保险（互联网专属B款）条款',
        title: '主条款',
        // extra: 'C00022132312023080222721'
    },
];

export const eventTracking = (name, time = 0) => {
    const { productKey, mTel, tel, channel, phoneNo, identifier, encryptNumber } = orderInfo;
    const phone = mTel || tel || (isPhoneNum(phoneNo) ? phoneNo : "");

    actionTracking({
        page: `优保众惠国任聚合赠险${identifier}(${productKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: productKey,
        time: time,
        volcEncryptPhone: encryptNumber || undefined,
    }).then((res) => {
        const { code, data } = res;
        if (code == 2000) {
            const { mobileId } = data || {};
            if (mobileId) {
                orderInfo.mTel = mobileId;
            }
        }
    });
};

export const domainTracking = () => {
    const phone = star_marked_phone(orderInfo.phoneNo);
    let temp = '';
    if (phone) {
        temp = `(${AESEncrypt(phone)})`
    }
    const host = window.location.host;
    eventTracking(`域名(${host})${temp}`);
}

const showToast = (message = "", duration = 2000) => {
    Toast({
        forbidClick: true,
        message: message,
        position: "center",
        duration: duration, // 弹窗时间毫秒
    });
};

export const toastAndAction = (v = 0, message = "", duration = 2000) => {
    // 1 toast(1) 2输入框位置调整(1<<1)
    if (v & 0b1) {
        showToast(message, duration);
    }

    if (v & 0b10) {
        // 输入框居中
        setTimeout(() => {
            const node = document.getElementById("input_x_2302131855");
            node && node.scrollIntoView && node.scrollIntoView({ block: "center" });
        }, 500);
    }
};

export const loadOrderInfo = () => {
    return bxStorage.getObjItem("YBHNZX01_INFO") || {};
};

export const saveOrderInfo = () => {
    bxStorage.setObjItem("YBHNZX01_INFO", orderInfo);
};
