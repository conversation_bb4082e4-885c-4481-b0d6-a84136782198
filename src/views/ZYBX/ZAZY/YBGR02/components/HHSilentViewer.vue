<template>
    <van-popup v-model="obj.v2" class="container_2212271130" position="bottom" round>
        <div class="header">
            <img class="header-img" src="@/assets/imgs/ZAZY/YBGR02/img04.png">
        </div>
        <div class="header-premium">
            <p>变更保障后每月保费由<span>{{ premiumObj.first }}</span>元变更为<span>{{ premiumObj.next }}</span>元，自次月开始扣费并生效</p>
            <p>变更后的保障责任、赔付比例、免赔额详见<span class="view-txt" @click.stop="onViewPolicy('投保须知', true)">《投保须知》</span>
            </p>
        </div>
        <div id="id_2212271130" class="content">
            <Policy_Health></Policy_Health>
        </div>
        <div class="footer">
            <div class="footer-text">
                我同意次月变更保障
                <span class="view-txt" @click.stop="onViewPolicy('投保须知', true)">《投保须知》</span>，并接受
                <span class="view-txt" @click.stop="onViewPolicy('保险条款', true)">《保险条款》</span>
                <span class="view-txt" @click.stop="onViewPolicy('健康告知', true)">《健康告知》</span>
                <span class="view-txt" @click.stop="onViewPolicy('责任免除', true)">《责任免除》</span>
                <span class="view-txt" @click.stop="onViewPolicy('特别约定', true)">《特别约定》</span>
                <span class="view-txt" @click.stop="onViewPolicy('保障计划', true)">《保障计划》</span>
                <span class="view-txt" @click.stop="onViewPolicy('费率表', false)">《费率表》</span>。
            </div>
            <div class="button-container">
                <div class="button" @click="onclick(false)">
                    不同意
                </div>
                <div class="button color" @click="onclick(true)">
                    同意并继续
                </div>
            </div>
        </div>
    </van-popup>
</template>

<script>
import Policy_Health from "./Policy/Policy_Health";

export default {
    name: "HHSilentViewer",
    components: { Policy_Health, },
    props: { obj: Object, obj1: Object },
    watch: {
        'obj.v2': {
            handler() {
                this.scrollToTop();
            },
        },
    },
    computed: {
        premiumObj() {
            const { firstPremium, nextPremium } = this.obj1;
            return { first: firstPremium, next: nextPremium };
        },
    },
    methods: {
        onclick(ret) {
            this.obj.v2 = false;
            this.$emit('click', ret);
        },
        onViewPolicy(name, isPolicy) {
            this.obj.belongs = 'v2';
            this.obj[isPolicy ? 'v1' : 'v'] = true;
            this.obj[isPolicy ? 'page1' : 'page'] = name;
        },
        scrollToTop() {
            const node = document.getElementById('id_2212271130');
            node && (node.scrollTop = 0);
        }
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2212271130 {
    height: 90%;
    width: 3.75rem;
    inset: auto 0 0 0;
    margin: 0 auto;

    display: flex;
    flex-direction: column;

    .view-txt {
        color: #F34015;
        font-weight: 500;
    }

    .header {
        .header-img {
            display: block;
            width: 100%;
        }
    }

    .header-premium {
        font-size: 0.12rem;
        color: #333333;
        text-align: center;
        line-height: 0.2rem;
    }

    .content {
        flex: 1;
        overflow: auto;
    }

    .footer {
        background-color: #ffffff;
        border-top: #f2f2f2 1px solid;

        .footer-text {
            padding: 0.05rem 0.1rem 0;
            font-size: 0.13rem;
            line-height: 1.5;
            text-align: justify;
        }

        .button-container {
            display: flex;
            justify-content: space-around;

            .button {
                margin: 0.05rem 0.2rem 0.1rem;
                padding: 0.12rem 0;
                width: 1.5rem;
                border-radius: 999px;
                font-size: 0.16rem;
                font-weight: 500;
                text-align: center;
                color: #666666;
                border: #eeeeee 1px solid;
                background-color: #ffffff;
            }

            .color {
                color: #ffffff;
                background: linear-gradient(270deg, #FF7235 0%, #FF6727 100%);
            }
        }
    }
}
</style>
