<template>
    <div class="TEST-Index1">
        <div class="top">
            <div class="video-box">
              <video id="video" :autoplay="true" class="player" playsinline :muted="true" webkit-playsinline x5-playsinline preload
                x5-video-player-fullscreen="false" x5-video-player-type="h5">
                <source
                  src="https://cdns.bountech.com/marketfront/file/insurance/cdn/picture/TaiKang/TK01/video.mp4"
                  type="video/mp4" />
              </video>
            </div>
            <van-form class="form" label-width="0.8rem" label-align="right" colon>
              <h3>域名测试页面</h3>
              <div class="form-item"><van-cell title="当前域名:" :label="url"></van-cell></div>
            </van-form>
        </div>
    </div>
</template>

<script>
import '@/assets/icons/selected.svg';
import '@/assets/icons/unselected.svg';
import {updateMonitor} from '@/api/insurance-api'
export default {
    name: "Index1",
    data() {
        return {
            url: window.location.href,
            timer: null,
          }
    },
    mounted() {
      this.init()
    },
    methods: {
        //初始化
        init() {
            const query = this.$route.query || {};
            const curIndex = parseInt(query.curIndex) || 0;
            this.id = parseInt(query.id) || 0;
            let environment = process.env.VUE_APP_TITLE
            const prefix = ['prod','production','iybprod', 'kdprod'].includes(environment)?'https://js-bprod.tiancaibaoxian.com/marketfront/insurancecdn':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
            this.monitor()
            setTimeout(() => {
              window.location.href = prefix + `/test-link?curIndex=${curIndex + 1}`
            }, 60000)
        },
         monitor() {
            const params = {
              id: this.id,
              url: this.url,
              status: '成功'
            }
            updateMonitor(params).then(res => {
                if (res.data.code != '2000') {
                  this.$toast('接口出错')
                }
            }).catch(() => {
              this.$toast('接口出错')
            });
        },
    },
}
</script>

<style scoped lang="less" type="text/less">
#app {
  position: fixed;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
}
.TEST-Index1 {
    background:#343efe;
    position: fixed;
    height: 100%;
    width: 100%;
    bottom: 0;
    left: 0;
    overflow-y: auto;
    overflow-x: hidden;
    padding-bottom: 0.8rem;
    -webkit-overflow-scrolling: touch;
    div, p, span {
        box-sizing: border-box;
    }
    .video-box {
      width: 100%;
      height: 2.2rem;
      #video {
        width: 100%;
      }
    }
    .top-introduce {
      width: 100%;
      margin-top: 0.8rem;
      margin-bottom: -0.12rem;
      position: relative;
      z-index: 1;
    }

    img {
        width: 100%;
        display: block;
    }
    .introduction, .introduction-title {
        width: 90%;
        position: relative;
        z-index: 6;
        margin: auto;
    }
    .introduction-title {
      margin: 0.22rem auto 0.18rem;
    }
    .top {
        position: relative;
        z-index: 2;
        margin: -1.2rem auto 0;
        padding: 0.15rem 0.15rem;
        width: 90%;
        border-radius: 0.65rem 0.12rem 0.12rem;
        -webkit-box-shadow: 0 4px 10px 0 #fbd192;
        box-shadow: 0 4px 10px 0 #fbd192;
        background: #fff;
        .dot {
          height: 0.6rem;
          width: 0.6rem;
          position: absolute;
          right: -0.27rem;
          top: -0.2rem;
          z-index: 3;
        }
        .header {
            position: relative;
            z-index: 4;
            height: 0.35rem;
            margin-left: 0.05rem;
            img {
              width: 66%;
            }
            
        }

        .form {
            min-height: 1rem;
            margin-top: 0.2rem;
            background-color: #fff;
            h3 {
              text-align: center;
              font-size: 0.2rem;
            }
            .form-item {
                width: 100%;
                margin-bottom: 0.12rem;
                font-size: 0.14rem;
            }
        }
    }
    @keyframes weekcollor-staging-banner__btn {
        0% {
            -webkit-transform: scale(1.02);
            transform: scale(1.02);
        }
        40% {
            -webkit-transform: scale(1.02);
            transform: scale(1.02);
        }
        70% {
            -webkit-transform: scale(0.98);
            transform: scale(0.98);
        }
        100% {
            -webkit-transform: scale(1.02);
            transform: scale(1.02);
        }
    }
    @keyframes weekcollor-staging-banner__finger {
        0% {
            bottom: -0.3rem;
            right: 0.30rem;
            /*transform: rotate(-10deg);*/
            transform: translate(0);
        }
        45% {
            bottom: -0.2rem;
            right: 0.5rem;
            /*transform: rotate(-20deg);*/
            transform: translate(-0.05, -.1rem);
        }
        70% {
            bottom: -0.2rem;
            right: 0.5rem;
            /*transform: rotate(-20deg);*/
            transform: translate(-0.05, -.1rem);
        }
        100% {
            bottom: -0.3rem;
            right: 0.30rem;
            /*transform: rotate(-10deg);*/
            transform: translate(0);
        }
    }
}
.bottom {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
}


.hidden-scroll {
  overflow: hidden;
}
</style>


