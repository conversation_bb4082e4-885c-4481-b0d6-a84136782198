<template>
    <div class="container_2211021130">
        <div class="point-x">
            <div v-for="(item) in planPoints" :key="item.value" :class="{'font-size-small':!item.key}"
                 class="point-item">
                <span v-if="item.key" class="point-item-key">{{ item.key }}</span>
                <span class="point-item-value" v-html="item.value"></span>
            </div>
        </div>
        <div class="detail-x">
            <div v-for="(item) in planDetails" :key="item.key" class="detail-item">
                <div class="detail-item-header">
                    <span class="detail-item-header-key">{{ item.key }}</span>
                    <span class="detail-item-header-value">{{ item.value }}</span>
                </div>
                <div class="detail-item-content" v-html="item.text"></div>
            </div>
        </div>
    </div>
</template>

<script>
import {planDetails, planPoints,} from '../src';

export default {
    name: 'HHPlanDetail',
    data() {
        return {
            planDetails,
            planPoints,
        }
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2211021130 {
    padding: 0 0.15rem;
    color: #333333;
    font-size: 0.14rem;
    line-height: 1.50;
    text-align: justify;
    background-color: #FFFFFF;

    .font-size-small {
        font-size: 0.12rem;
    }

    .point-x {

        .point-item {
            display: flex;
            align-items: center;
            justify-content: space-between;

            padding: 0.10rem 0;
            border-bottom: #F2F2F2 1px solid;

            .point-item-key {
                margin-right: 0.15rem;
                font-weight: 500;
                flex-shrink: 0;
            }

            .point-item-value {
                color: #999999;
            }
        }

        .point-item:last-child {
            border-bottom-width: 0.04rem;
        }
    }

    .detail-x {

        .detail-item {
            margin-bottom: 0.15rem;

            .detail-item-header {
                display: flex;
                align-items: center;
                justify-content: space-between;

                padding: 0.10rem 0;
                font-weight: 500;

                .detail-item-header-value {
                    margin-left: 0.15rem;
                    flex-shrink: 0;
                    color: #FE5243;
                }
            }

            .detail-item-content {
                color: #666666;
            }
        }
    }
}

</style>
