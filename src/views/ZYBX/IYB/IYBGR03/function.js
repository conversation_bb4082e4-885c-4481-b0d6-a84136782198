import moment from "moment";
import CryptoJS from "crypto-js";
import { Toast } from "vant";
import { actionTracking } from "@/assets/js/api";
import { bxStorage, } from "@/utils/store_util";
import { PremiumRate, PremiumRate1, relations } from "./src";
import { createPromotionOrder, fetchCreateTKZengXianOrder, upgradeIYBProduct } from "@/api/insurance-api";
import { isCardNo, isMaskedAndT1Phone, isPersonName, isPhoneNum, TraceLogInfoKeys, url_safe_b64_encode, isInWx, GetAge } from "@/assets/js/common";

export const orderInfo = {
    checked: false,
    hintVisible: false,
    source: 'direct', // 页面来源
    action: 'direct', //  back: '返回',promotion: '促活跳转',follow: '结果跳转',direct: '直发',forward: '转发',result: '结果页',promote: '促活'
    planKey: TraceLogInfoKeys.iyb_gr_bw_health_high_cube_base,
    infoKey: TraceLogInfoKeys.iyb_gr_bw_health_high_cube_base,
    identifier: '',
    relation: 1,    // 为谁投保 1:本人；2:配偶；3:儿女；4:父母；
    insurance: 1,   // 有无社保
    repay: 1,       // 缴费方式
    channel: '1',
    channelCode: '',
    m: '',          // 链接自带的加密手机号
    mTel: '',       // 加密手机号
    infoNo: '',     // 订单号
    policyNo: '',    // 保单号
    firstPremium: 0,
    nextPremium: 0,
    callbackUrl: '',

    starPhone: '', // 带*手机号
    phoneNo: '',    // 手机号
    name1: '',      // 本人
    name2: '',      // 配偶
    name3: '',      // 儿女
    name4: '',      // 父母
    idCard1: '',
    idCard2: '',
    idCard3: '',
    idCard4: '',
    param: '',

    sourcePage: '',
    paymentCode: -1, // NSF评分
    recordSetId: '',
    visitorId: '',
    traceBackUuid: '',
    school: '0', // 0非前置升级；1前置升级

    openId: '', // 微信公众号
    biz_no: 'iyb_hs_bwbe', // 微信公众号
    mc: '',
    authFailObj: {}, // 实名认证失败
}

export const checkOrderParams = () => {
    const { infoKey, identifier, channel, channelCode, sourcePage, action, source, school } = orderInfo;
    const { infoNo, mTel, starPhone, name1, idCard1, phoneNo, relation, repay, insurance, } = orderInfo;
    const { checked, firstPremium, nextPremium, recordSetId, visitorId, traceBackUuid, mc, openId, biz_no } = orderInfo;

    const page = `infoKey:${infoKey}&page:${identifier}&act:${action}&src:${source}`;
    const relation1 = relations.find(item => item.value == relation).param;

    const params = {
        infoNo,
        insurance,
        planKey: infoKey,
        page,
        channelCode,
        sourcePage,
        traceBackUuid,
        relation: relation1,
        channelId: channel,
        paymentPlan: repay,
        operatorPhone: '',
        holderName: '',
        holderIdCard: '',
        holderPhone: '',
        insuredName: '',
        insuredIdCard: '',
        insuredPhone: '',
        origin: mc,
    };

    const certName = `name${relation}`;
    const insuredName = orderInfo[certName];
    if (!isPersonName(insuredName)) {
        return { code: 1, msg: '请填写正确的被保人姓名', };
    }
    params.insuredName = insuredName;

    const certNo = `idCard${relation}`;
    const insuredIdCard = orderInfo[certNo];
    if (!isCardNo(insuredIdCard)) {
        return { code: 1, msg: '请填写正确的被保人身份证号' };
    }
    params.insuredIdCard = insuredIdCard;

    if (!isPersonName(name1)) {
        return { code: 1, msg: '请填写正确的投保人姓名' };
    }
    params.holderName = name1;

    if (!isCardNo(idCard1)) {
        return { code: 1, msg: '请填写正确的投保人身份证号' };
    }
    params.holderIdCard = idCard1;

    if (!isPhoneNum(phoneNo) && !isMaskedAndT1Phone(starPhone, mTel)) {
        return { code: 1, msg: '请填写正确的手机号码' };
    }

    const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;
    params.holderPhone = phone;
    params.insuredPhone = phone;
    params.operatorPhone = mTel || phoneNo;

    const age = GetAge(idCard1)
    const age1 = GetAge(insuredIdCard)
    if (age < 18) {
        return { code: 1, msg: '投保人年龄小于18周岁，可选择为儿女投保的方式进行投保' };
    }

    if (relation != 1) {
        if (idCard1 == insuredIdCard) {
            return { code: 1, msg: '您与被保人身份证号码不能相同' };
        }
    }

    if (relation == 2) {
        const agender = (+idCard1.slice(16, 17)) % 2;
        const agender1 = (+insuredIdCard.slice(16, 17)) % 2;
        if (age1 < 18) {
            return { code: 1, msg: '配偶年龄不能小于18周岁' }
        }
        if (agender == agender1) {
            return { code: 1, msg: '配偶双方性别不能相同' };
        }
    }

    if (relation == 3 && (age - age1 < 16)) {
        return { code: 1, msg: '您与儿女年龄至少要相差16周岁' };
    }

    if (relation == 4 && (age1 - age < 16)) {
        return { code: 1, msg: '您与父母年龄至少要相差16周岁' };
    }

    const index = window.location.href.indexOf('/ZYBX/');
    let returnUrl = `${window.location.href.substring(0, index)}/ZYBX/IYBGR03/Upgrade`;
    returnUrl = `${returnUrl}?RECORD_SETID=${recordSetId}`;

    const obj = {
        channel,
        school,
        mTel,
        starPhone,
        firstPremium,
        nextPremium,
        relation,
        name1,
        idCard1,
        phoneNo,
        [certNo]: insuredIdCard,
        [certName]: insuredName,
    }

    const param = url_safe_b64_encode(JSON.stringify(obj));
    returnUrl = `${returnUrl}&bizParams=${param}`;
    orderInfo.callbackUrl = returnUrl;

    const extendParams = {recordSetId, visitorId, school, returnUrl, failUrl: window.location.href.split('?')[0], mc2: mc, platformId: isInWx() ? 'PUB_ONLINE' : 'WAP', };
    if (openId && biz_no) {
        extendParams.openId = openId;
        extendParams.wechatService = biz_no;
    }

    params.extendParams = extendParams;

    saveOrderInfo();

    if (!checked) {
        return { code: 1, msg: '用户协议未同意', params }; //
    }

    return { code: 0, params };
}

export const submitOrderInfo = (params) => {
    const v = getFailIdentity(orderInfo);
    if (v) {
        return Promise.reject({ code: 1, msg: v });
    }

    return new Promise((resolve, reject) => {
        fetchCreateTKZengXianOrder(params).then(result => {
            orderResultHandle(result, resolve, reject);
        }).catch(err => {
            reject({ code: 1, msg: JSON.stringify(err) });
        });
    });
}

export const submitPromoteOrderInfo = (params) => {
    return new Promise((resolve, reject) => {
        createPromotionOrder(params).then(result => {
            orderResultHandle(result, resolve, reject);
        }).catch(err => {
            reject({ code: 1, msg: JSON.stringify(err) });
        });
    });
}

const orderResultHandle = (result, resolve, reject) => {
    const { code, msg, data } = result.data || {};
    if (code != 2000) {
        return reject({ code: 1, msg: msg });
    }

    const { orderNo, success, payforURL, msg: message, extentMap} = data || {};
    if (orderNo) {
        orderInfo.infoNo = orderNo;
    }
    if (!success) {
        let v = message;
        if (v.indexOf('不通过') >= 0 || v.indexOf('未通过') >= 0) {
            v = '实名认证未通过，请核对修改姓名和身份证号';
            saveFailIdentity(orderInfo, v);
        }
        return reject({ code: 1, msg: v });
    }

    if (extentMap && extentMap.jdQPSession) {
        bxStorage.setRawItem('JDQPSession', extentMap.jdQPSession);
    }

    if (payforURL) {
        return resolve(payforURL);
    }

    return reject({ code: 1, msg: '未获取到支付链接' });
}

export const upgradeOrderInfo = (infoNo, page) => {
    return new Promise((resolve, reject) => {
        upgradeIYBProduct(infoNo, page).then(res => {
            const { code, msg, data } = res.data || {};
            if (code != 2000) {
                return reject({ code: 1, msg: msg });
            }

            const { code: code1, result, msg: message } = data || {};
            const message1 = result || message;
            if (code1 == 0 || message1.indexOf('已升级') >= 0) {
                return resolve();
            }
            return reject({ code: 1, msg: message1 });
        }).catch(err => {
            return reject({ code: 1, msg: '接口出错' });
        });
    });
}

export const calculatePremium = (idCardNo, insurance, repay) => {
    if (!idCardNo || idCardNo.length != 18) {
        return { first: 0, next: 0 };
    }

    const birthday = idCardNo.substr(6, 8);
    const age = moment().add(0, 'days').diff(moment(birthday), 'year'); // 计算年龄
    const premiumObj = PremiumRate.find(item => item.min <= age && item.max >= age);
    const premiumObj1 = PremiumRate1.find(item => item.min <= age && item.max >= age);

    // 年龄超出投保范围
    if (!premiumObj) {
        return { first: 0, next: 0 };
    }

    const obj = repay == 0 ? premiumObj.year : premiumObj.month;
    const obj1 = repay == 0 ? premiumObj1.year : premiumObj1.month;

    const first = insurance == 0 ? obj.data2 : obj.data1;
    const next = insurance == 0 ? obj1.data2 : obj1.data1;

    return { first, next };
}

export const eventTracking = (name, time = 0) => {
    const { action, planKey, mTel, channel, phoneNo, identifier } = orderInfo;
    const map = {
        back: '返回',
        promotion: '促活跳转',
        follow: '结果跳转',
        direct: '直发',
        forward: '转发',
        result: '结果页',
        promote: '促活'
    };
    const prefix = map[action] ? `${map[action]}-` : '';
    const page = `${prefix}保通国任百万魔方${identifier}`;
    const phone = mTel || (isPhoneNum(phoneNo) ? phoneNo : '');

    actionTracking({
        page: `${page}(${planKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: planKey,
        time: time,
    }).then(res => {
        const { code, data } = res;
        if (code == 2000) {
            const { mobileId } = data || {};
            mobileId && (orderInfo.mTel = mobileId);
        }
    });
}

export const loadOrderInfo = (key = 'IYBGR03Info') => {
    return bxStorage.getObjItem(key) || null;
}

export const saveOrderInfo = (key = 'IYBGR03Info') => {
    bxStorage.setObjItem(key, orderInfo);
}

const showToast = (message = '', duration = 2000) => {
    Toast({
        message: message,
        duration: duration, // 弹窗时间毫秒
        position: 'middle',
        forbidClick: true,
    });
}

const inputEndEditing = () => {
    const inputList = document.querySelectorAll('input');
    for (const input of inputList) {
        input.blur && input.blur();
    }
}

const adjustInputPosition = () => {
    const node = document.querySelector('#id_input_x');
    node && node.scrollIntoView && node.scrollIntoView(true);
}

export const dispatchWithBitwise = (v = 0, message = '', duration = 2000) => {
    // 1 toast(1) 2收键盘(1<<1) 4输入框位置调整(1<<2)
    if (v & 0b1) {
        showToast(message, duration);
    }

    if (v & 0b10) {
        inputEndEditing();
    }

    if (v & 0b100) {
        adjustInputPosition();
    }
}

export const createNoticeList = () => {
    const noticeList = [];
    for (let idx = 0; idx < 100; idx++) {
        const tailNumber = (Math.random() * 8999 + 1000).toFixed(0);
        noticeList.push(tailNumber);
    }
    return noticeList;
}

const saveFailIdentity = (orderInfo, reason) => {
    const { relation, name1, idCard1,} = orderInfo;
    const certName = orderInfo[`name${relation}`];
    const certNo = orderInfo[`idCard${relation}`];
    const md5 = CryptoJS.MD5(`${name1}${idCard1}${certName}${certNo}`).toString();
    orderInfo.authFailObj[md5] = reason;
    console.log(md5);
}

const getFailIdentity = (orderInfo) => {
    const { relation, name1, idCard1,} = orderInfo;
    const certName = orderInfo[`name${relation}`];
    const certNo = orderInfo[`idCard${relation}`];
    const md5 = CryptoJS.MD5(`${name1}${idCard1}${certName}${certNo}`).toString();
    return orderInfo.authFailObj[md5]
}