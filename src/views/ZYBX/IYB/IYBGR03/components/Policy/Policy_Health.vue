<template>
	<div class="container_2308281445">
		<p class="title">健康告知</p>
		<p><b>投保人应在对所有被保险人健康，职业，历史投保记录等以下情况充分了解的基础上履行如实告知义务。投保人承诺完全知晓所有被保险人以下情况。</b></p>
		<p><b>若被保险人实际情况与下述告知内容不符：</b></p>
		<p><b>（1）保险人有权解除保险合同。</b></p>
		<p><b>（2）若在合同解除前发生保险事故，保险人不承担赔偿或给付保险金的责任，并有权不退还保险费。</b></p>
		<p>投保人需确认被保险人是否符合以下情况？</p>
		<p>1.【既往投保情况】</p>
		<p>被保险人过去2年内投保人身保险或健康保险时，不存在被保险公司拒保、延期、加费或者附加条件承保？</p>
		<p>2.【从事职业】</p>
		<p>被保险人专职或兼职的职业/工种未超出《职业分类表》中所列1-4类职业？</p>
		<p>3.【就医行为】</p>
		<p>1）被保险人过去1年内未有健康检查结果异常【如病理检查、实验室检查、物理检查、内镜检查、心电图检查、影像学检查（包含但不限于超声检查、钼靶、X射线、CR、CT、MRI、PET-CT）等】？</p>
		<p>2）被保险人过去2年内未曾因健康异常发生过住院治疗或被医生建议住院治疗或手术（不包括剖腹产/顺产/鼻炎/急性胃肠炎/肺炎/外伤（非颅脑外伤、非多发外伤）已痊愈，无需后续治疗，无后遗症未遗留残障/急性阑尾炎已手术/急性胃肠炎（无慢性胃炎肠炎病史）/急性上呼吸道感染，无并发症/新生儿母乳性黄疸无其他并发症）？</p>
		<p>4.【被保险人现在没有患有或未曾患有或被诊断下列疾病或罹患下列症状体征】？</p>
		<p>1）良/恶性肿瘤、原位癌、癌前病变、尚未证实良性或恶性的肿瘤；2级及以上高血压（收缩压≥160mmHg，和/或舒张压≥100mmHg）、糖尿病、冠心病/冠状动脉狭窄、心脏瓣膜病、心肌病、心肌梗死、风湿性心脏病、肺源性心脏病、心力衰竭、心功能不全二级（含）以上、严重心律失常、主动脉瘤，主动脉夹层；脑梗死/脑出血、脑血管病、脑炎或脊髓炎后遗症、脑垂体病、脑外伤后遗症、阿尔茨海默病、帕金森氏病、癫痫、精神疾病；肾炎、肾功能不全、多囊肾、肾/输尿管结石、肾切除；肝炎（肝炎病毒携带者）、重度脂肪肝、酒精肝、肝硬化、肝功能衰竭；胰腺炎；白血病、再生障碍性贫血、系统性红斑狼疮、紫癜症、多发性硬化症、重症肌无力、结节病、类风湿性关节炎、痛风性关节炎；慢性酒精性中毒；肺结核、慢性阻塞性肺病、间质性肺病、肺纤维化、肺气肿、呼吸功能不全、支气管扩张、瘫痪、慢性胆囊炎、胆石症、胆囊息肉、下肢静脉曲张、甲亢、甲状腺结节、传导性耳聋、胃/十二指肠溃疡、慢性萎缩性胃炎、椎间盘突出症、股骨头坏死、溃疡性结肠炎或克罗恩病（节段性肠炎）、先天性或遗传性疾病、法定传染病（甲类及乙类）、性病、HIV阳性、严重视力障碍（高度近视：1000度以上）或听力障碍、严重烧伤、中重度残疾、接受过组织或器官移植过造血干细胞移植。</p>
		<p>2）被保险人过去1年内不存在下列症状：反复头痛、晕厥、胸痛、气急、紫绀、持续反复发热、抽搐、不明原因皮下出血点、咯血、反复呕吐、进食哽噎感或吞咽困难、呕血、浮肿、腹痛、黄疸（新生儿黄疸且已治愈的除外）、便血、血尿、蛋白尿、肿块、结节、消瘦（半年内非健身原因导致体重减轻5公斤以上）、不明原因的持续或间歇性疼痛（超过1个月）、黑痣增大、不明原因持续或反复发热（超过2周）、皮肤或黏膜的溃疡久治不愈、职业病、酒精中毒、其他药品中毒、智能障碍、五官/脊柱/胸廓/四肢畸形或功能障碍，成年人体格指数（体重kg/身高m^2）≥30或≤16？</p>
		<p>3）适用于女性被保险人：被保险人目前没有处在妊娠过程中或未曾患有乳腺囊肿/结节、卵巢囊肿、宫外孕、子宫内膜异位、子宫肌腺症、子宫肌瘤、葡萄胎或其他妊娠滋养细胞疾病、宫颈中重度糜烂、宫颈不典型增生等疾病；半年内不存在乳头异常溢液、乳房表面皮肤凹陷、皱褶或皮肤收缩等症状，半年内不存在阴道异常出血、疼痛、糜烂或回缩等症状？</p>
		<p>5、【其他告知】</p>
		<p>被保险人2周岁以下：出生时体重不低于2.5公斤（双胞胎除外），未有早产/窒息/颅内出血/缺血缺氧性脑病/发育迟缓/脑瘫情形？</p>
		<p>6、被保险人目前或既往未确诊过新型冠状病毒肺炎重型、危重型（临床分型根据国卫办医函［2022］71号《关于印发新型冠状病毒肺炎诊疗方案（试行第九版修订版）的通知》确定的临床分型）。</p>
	</div>
</template>

<script>

export default {
	name: "Policy_Health",
	props: { obj: Object },
	methods: {
		onViewPolicy(name) {
			if (!this.obj) return;
			this.obj.page = name;
			this.obj.visible = true;
		},
	},
}

</script>

<style lang="less" scoped type="text/less">
	.container_2308281445 {
		padding: 0 0.1rem 0.2rem;
		font-family: sans-serif;
		font-size: 0.14rem;
		line-height: 1.5;
		text-align: justify;
		color: #333333;
		word-break: break-all;

		.title {
			line-height: 0.35rem;
			font-size: 0.16rem;
			font-weight: 700;
			text-align: center;
		}

		a,
		.color {
			color: #ff5001;
		}
	}
</style>
