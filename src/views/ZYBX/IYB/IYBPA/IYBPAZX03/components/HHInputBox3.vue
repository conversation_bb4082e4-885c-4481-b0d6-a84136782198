<template>
	<div class="container_2302131855">
		<div class="header">
			<div class="header-top">
				{{ title.text1 }}
				<span class="header-top-color">&nbsp;&nbsp;{{ title.text2 }}</span>
			</div>
		</div>
		<div v-if="isStep2">
            <div class="box-item-wrapper">
                <div v-if="highlight.name" class="box-item-animation"></div>
                <div class="box-item">
                    <span class="label">真实姓名</span>
                    <input v-model="obj.name1" class="input" maxlength="25" placeholder="信息保密，仅用于生成保单" type="text" @focus="onFocus('name',$event.target.value)" @input="onInput('name',$event.target.value)">
                </div>
            </div>
            <div class="box-item-wrapper">
                <div v-if="highlight.idCard" class="box-item-animation"></div>
                <div class="box-item">
                    <span class="label">身份证号</span>
                    <input v-model="obj.idCard1" class="input" maxlength="18" placeholder="信息保密，仅用于生成保单" type="text" @focus="onFocus('idCard',$event.target.value)" @input="onInput('idCard',$event.target.value)">
                </div>
			</div>
            <div class="box-item-wrapper">
                <div v-if="highlight.phone" class="box-item-animation"></div>
                <div class="box-item">
                    <span class="label">手机号码</span>
                    <input v-model="phoneFormat" class="input" placeholder="您的信息将被严格保密" readonly type="tel">
                </div>
			</div>
		</div>
		<div v-else>
            <div class="box-item-wrapper">
                <div v-if="highlight.phone" class="box-item-animation"></div>
                <div class="box-item">
                    <span class="label">手机号码</span>
                    <input v-model="obj.phoneNo" class="input" maxlength="11" onkeyup="value=value.replace(/\D/g,'')" placeholder="您的信息将被严格保密" type="tel" @focus="onFocus('phone',$event.target.value)" @input="onInput('phone',$event.target.value)">
                </div>
            </div>
		</div>
		<div id="id_action_button" class="submitButton" @click="submit">
			免费领取
			<img alt="hand" class="hand" src="@/assets/imgs/common/icon_hand1.png">
		</div>
		<slot name="policy"></slot>
	</div>
</template>

<script>
import { isPhoneNum, star_marked_phone } from "@/assets/js/common";

export default {
	name: "HHInputBox3",
	props: {
		obj: Object
	},
    data() {
        return {
            // 高亮提示
            highlight: {
                name: false, // 姓名
                idCard: false, // 身份证号
                phone: true, // 手机号
            },
        }
    },
	computed: {
		isStep2() {
			return this.obj.step == 'step2';
		},
		title() {
			if (this.isStep2) {
				return { text1: '仅剩1步', text2: '免费领取' };
			}
			return { text1: '仅剩2步', text2: '免费领取' };
		},
		phoneFormat() {
			const { phoneNo, starPhone } = this.obj;
			return star_marked_phone(isPhoneNum(phoneNo) ? phoneNo : starPhone);
		}
	},
	methods: {
		submit() {
			this.$emit('submit');
		},
		onInput(key, value) {
			this.$emit('input', { key, value });
		},
		onFocus(key, value) {
			this.$emit('focus', { key, value });
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302131855 {
		position: relative;
		padding: 1px 0;
		font-size: 0.16rem;
		border-radius: 0.10rem;
		background: #ffffff;
		box-shadow: 0 1px 10px 0 rgba(200, 133, 85, 0.24);

		.header {
			margin: 0.15rem 0;

			.header-top {
				font-size: 0.2rem;
				font-weight: 500;
				color: #333333;

				display: flex;
				align-items: center;
				justify-content: center;

				&::before,
				&::after {
					content: " ";
					position: relative;
					top: -0.01rem;
					margin: 0 0.1rem;
					width: 0.25rem;
					height: 0.25rem;
					background: url("~@/assets/imgs/common/icon_arrows_down.png");
					background-size: contain;
				}

				.header-top-color {
					color: #f23f3d;
				}
			}

			.header-bottom {
				margin-top: 0.05rem;
				text-align: center;
				color: #f23f3d;
				font-size: 0.14rem;
			}
		}

        .box-item-wrapper {
            position: relative;
			margin: 0 0.15rem 0.12rem;
        }

        .box-item-animation {
            position: absolute;
            width: 100%;
            height: 100%;

            &::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 100%;
                height: 100%;
                background-color: transparent;
                border: 8px solid rgba(255, 130, 0, 0.8); /* 初始粗边框 */
                border-radius: 0.2rem;
                transform: translate(-50%, -50%) scale(1);
                opacity: 0;
                pointer-events: none; /* 确保波纹不影响按钮点击 */
                animation: borderRipple 1.5s ease-out infinite;
            }

            /* 边框粗细变化动画定义 */
            @keyframes borderRipple {
                0% {
                    transform: translate(-50%, -50%) scale(1);
                    border-width: 8px; /* 开始时非常粗的边框 */
                    opacity: 0.8; /* 开始时较明显 */
                }
                40% {
                    transform: translate(-50%, -50%) scale(1.1);
                    border-width: 4px; /* 中间时中等粗细 */
                    opacity: 0.6;
                }
                100% {
                    transform: translate(-50%, -50%) scale(1.2);
                    border-width: 1px; /* 结束时非常细的边框 */
                    opacity: 0; /* 逐渐消失 */
                }
            }
        }

		.box-item {
            position: relative;
			display: flex;
			align-items: center;
			border-radius: 0.2rem;
			border: 1px solid rgba(255, 130, 0, 0.6);
			background-color: #fff5e9;

			.label,
			.input {
				height: 0.5rem;
				line-height: 0.5rem;
				font-size: 0.15rem;
			}

			.label {
				width: 1rem;
				text-align: center;
				font-size: 0.15rem;
				font-weight: 500;
			}

			.input {
				flex: 1;
				border: 0;
				outline: none;
				width: 2rem;
				font-size: 0.16rem;
				background-color: unset;
			}
		}

		.submitButton {
			position: relative;
			margin: 0.2rem 0.25rem 0.2rem;
			padding: 0.15rem 0;
			text-align: center;
			font-size: 0.2rem;
			font-weight: 700;
			color: #ffffff;
			border-radius: 0.25rem;
			box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
			background: linear-gradient(
				270deg,
				rgb(255, 16, 46),
				rgb(253, 123, 69)
			);
			animation: button_animation 1.35s linear infinite;

			.hand {
				position: absolute;
				top: 0.3rem;
				left: 75%;
				width: 18%;
				animation: hand_animation 1.35s linear infinite;
			}
		}

		@keyframes button_animation {
			0% {
				transform: scale(1);
			}
			40% {
				transform: scale(1);
			}
			70% {
				transform: scale(0.95);
			}
			100% {
				transform: scale(1);
			}
		}

		@keyframes hand_animation {
			0% {
				transform: translate(-0.1rem, -0.1rem);
			}
			45% {
				transform: translate(0.1rem, 0.1rem);
			}
			70% {
				transform: translate(0.1rem, 0.1rem);
			}
			100% {
				transform: translate(-0.1rem, -0.1rem);
			}
		}
	}
</style>
