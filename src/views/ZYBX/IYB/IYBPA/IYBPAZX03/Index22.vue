<template>
    <div class="container_2208291100">
        <audio id="id_audio_2310081000" preload="auto" type="audio/wav">
        </audio>
        <div class="banner">
            <img src="@/assets/imgs/IYB/IYBPA/IYBPAZX03/img01_18.png">
            <div class="logo-x">
                <img class="logo-gr logo" src="@/assets/imgs/logo/guoren_4.png">
                <img class="logo-tk logo" v-if="!isTKHidden1" src="@/assets/imgs/logo/taikang_3.png">
                <img class="logo-pa logo" v-if="!isPAHidden1" src="@/assets/imgs/logo/pingan_4.png">
            </div>
            <div class="banner-tag" @click="onViewDetail('活动规则')">活动规则</div>
        </div>
        <HHInputBox :obj="orderInfo" class="input-x" @focus="onTextFocus" @input="onTextInput" @submit="submit">
            <template #policy>
                <div v-if="isStep2" class="policy-x">
                    <van-icon class="policy-icon" :name="orderInfo.checked ? 'checked' : 'circle'"
                        @click="orderInfo.checked = !orderInfo.checked" />
                    我已确认
                    <span class="policy-txt" @click.stop="onReadPolicy('健康告知')">《健康告知》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('投保须知')">《投保须知》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('保险条款')">《保险条款》</span>
                    <span v-if="!isPAHidden1" class="policy-txt" @click.stop="onReadPolicy('投保人声明')">《投保人声明》</span>
                    <span v-if="!isTPHidden1" class="policy-txt" @click.stop="onReadPolicy('产品说明')">《产品说明》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('责任免除')">《责任免除》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('隐私条款')">《隐私条款》</span>
                    <span class="policy-txt" @click.stop="onReadPolicy('活动规则')">《活动规则》</span>并接受承保保司提供保险咨询服务。
                </div>
            </template>
        </HHInputBox>
        <van-swipe class="introduce" indicator-color="#1a5deb">
            <van-swipe-item v-if="!isPAHidden1"><img alt=""
                    src="@/assets/imgs/IYB/IYBGRZX01/img03_1.png"></van-swipe-item>
            <van-swipe-item><img alt="" src="@/assets/imgs/IYB/IYBGRZX01/img02_1.png"></van-swipe-item>
            <van-swipe-item v-if="!isTPHidden1"><img alt=""
                    src="@/assets/imgs/IYB/IYBGRZX01/img04_1.png"></van-swipe-item>
            <van-swipe-item v-if="!isTKHidden1"><img alt=""
                    src="@/assets/imgs/IYB/IYBGRZX01/img09_1.png"></van-swipe-item>
        </van-swipe>
        <img v-if="!isTPHidden1" alt="" class="introduce" src="@/assets/imgs/IYB/IYBPA/IYBPAZX03/img04_3.png">
        <div class="copyright">
            <p v-if="insCompany.extra && isProviderShow">{{ insCompany.extra }}</p>
            <p v-if="!isPAHidden1">平安产险重大疾病保险条款注册号：<br />C00001732612024062802583</p>
            <p v-if="!isTPHidden1">太平任逍遥意外伤害保险（互联网专属）条款：<br />太平人寿[2024]意外伤害保险006号</p>
            <p>国任无忧重疾赠险条款注册号：<br />C00014232612023080323521</p>
            <p v-if="!isTKHidden1">泰康e顺短期交通意外伤害保险(互联网)：<br />泰康人寿（2021）意外伤害保险109号</p>
            <p v-if="!insCompany.isHidden">版权所有©{{ insCompany.name }}</p>
            <div class="beian">
                <p>互联网专属产品</p>
                <p>{{ insCompany.number }}</p>
            </div>
        </div>
        <div v-if="bottomButtonVisible" class="bottom-x" @click="submit">
            免费领取
            <img alt="" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <HHTabViewer :obj="policyObj" @ok="checkPolicy" @view="onViewDetail"></HHTabViewer>
        <HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
        <HHNextPopup :obj="nextObj" @click="jumpNextLink"></HHNextPopup>
        <HHPrevPopup :obj="prevObj" @click="jumpPrevLink"></HHPrevPopup>
        <HHWarnHint :obj="orderInfo"></HHWarnHint>
        <IYBRecord1 :obj="orderInfo"></IYBRecord1>
        <H5CallHint :enabled="h5CallEnabled" :channel="orderInfo.channel"></H5CallHint>
    </div>
</template>

<script>
import HHWarnHint from "./components/HHWarnHint";
import HHInputBox from "./components/HHInputBox1";
import HHNextPopup from "./components/HHNextPopup";
import HHPrevPopup from "./components/HHPrevPopup";
import HHPolicyViewer from "./components/HHPolicyViewer";
import HHTabViewer from "./components/HHTabViewer";
import IYBRecord1 from "@/views/components/IYBRecord1";
import H5CallHint from "@/components/H5CallHint";
import { fetchIYBWXOpenId } from "@/api/wx-api";
import { domainPathMap, is_server_phone, findDomainAbrr, audioObj1 } from "@/views/ZYBX/src";
import { createOrderInfo, eventTracking, loadOrderInfo, saveOrderInfo, showToast, domainTracking } from "./src";
import { reviseIdCard, reviseName, GetAge, isAIChannel, isCardNo, isMaskedAndT1Phone, isPersonName, isPhoneNum, TraceLogInfoKeys, url_safe_b64_encode } from "@/assets/js/common";
import { createMultiFreeOrder, fetchRoundRobinWithAgeZXResultNew, fetchStarPhoneV4, userWeComInfo, fetchNsfInfo, bindPhoneAndPrivacy } from '@/api/insurance-api';
import { openNewTab } from '@/utils/window';
export default {
    name: "IYBPAZX03Index22",
    data() {
        const orderInfo = createOrderInfo();
        return {
            isTPHidden: false,
            isTPHidden1: false,
            isPAHidden1: false,
            isTKHidden1: false,
            isProviderShow: false,
            orderInfo: orderInfo,
            policyObj: { visible: false, page: '', visible1: false, page1: '', isTPHidden1: false, isPAHidden1: false, isTKHidden1: false },
            prevObj: { visible: false, showTimer: false, path: '' },
            nextObj: { visible: false, showTimer: false, path: '' },
            bottomButtonVisible: false,
            prevName: '',
            audioObj: audioObj1,
            h5CallEnabled: false,
        }
    },
    components: {
        IYBRecord1,
        HHPrevPopup,
        HHNextPopup,
        HHInputBox,
        HHPolicyViewer,
        HHTabViewer,
        HHWarnHint,
        H5CallHint,
    },
    computed: {
        isStep2() {
            return this.orderInfo.step == 'step2';
        },
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.init();

        this.$nextTick(() => {
            this.actionButtonObserver();
            this.pushHistory();
            window.addEventListener("popstate", this.stackPopHandle);
        });

        // this.makeAudioWork();
    },
    beforeDestroy() {
        window.removeEventListener("popstate", this.stackPopHandle);
    },
    methods: {
        makeAudioWork() {
            const func = () => {
                try {
                    const oAudio = document.querySelector('#id_audio_2310081000');
                    if (oAudio && !oAudio.alreadyPlay) {
                        oAudio.play().then(() => {
                            oAudio.alreadyPlay = true;
                        });

                        oAudio.addEventListener("canplay", () => {
                            console.log('音频可播放', Date.now());
                            oAudio.play(); // 音频可流畅播放时触发
                        });

                        // 监听播放结束事件
                        oAudio.addEventListener('ended', () => {
                            console.log('音频播放结束', Date.now());
                            for (let key in this.audioObj) {
                                const arr = this.audioObj[key];
                                const idx = arr.indexOf(oAudio.src);
                                if (idx >= 0) {
                                    setTimeout(() => {
                                        const tmp = arr[idx + 1];
                                        if (tmp) {
                                            oAudio.src = tmp;
                                            oAudio.load();
                                        }
                                    }, 3000);

                                    return;
                                }
                            }
                        });
                    }
                } catch (e) {

                }
            };

            window.onclick = func;
            window.ontouchstart = func;
        },
        refreshAudioSrc(key) {
            const oAudio = document.querySelector('#id_audio_2310081000');
            if (!oAudio) {
                return;
            }
            if (oAudio.src.indexOf(key) >= 0) {
                return;
            }
            const arr = this.audioObj[key];
            if (!arr) {
                return '';
            }
            oAudio.src = arr[0];
            oAudio.load();
        },
        //初始化
        init() {
            const inQry = this.$route.query || {};
            const orderInfo = loadOrderInfo();

            this.isTPHidden = inQry.isShow != 1;
            this.isTPHidden1 = inQry.isShow1 != 1;
            this.isPAHidden1 = inQry.isShow2 != 1;
            this.isTKHidden1 = inQry.isShow3 != 1;
            this.isProviderShow = inQry.isShow0 == 1;
            this.policyObj.isTPHidden1 = this.isTPHidden1;
            this.policyObj.isPAHidden1 = this.isPAHidden1;
            this.policyObj.isTKHidden1 = this.isTKHidden1;

            Object.assign(this.orderInfo, orderInfo);
            this.orderInfo.page = 'IYBPAZX03Index22';
            this.orderInfo.sourcePage = '保通平安交通重疾聚合V250703';
            this.orderInfo.channel = inQry.channel || orderInfo.channel || '1';
            this.orderInfo.channelCode = inQry.cld || orderInfo.channelCode || '';
            this.orderInfo.encryptNumber = inQry.encryptNumber || orderInfo.encryptNumber || '';
            this.orderInfo.mc = inQry.mc || this.orderInfo.mc || '';
            this.orderInfo.checked = this.orderInfo.channel >= 1000;

            // 检查是否启用H5通话功能
            this.h5CallEnabled = !!inQry.h5CallEnabled;

            if (this.orderInfo.received) {
                this._entryReport();
                return this.fetchNsfCode();
            }

            inQry.externalUserId = inQry.externalUserId || ''; // 保通企微
            inQry.userId = inQry.userId || ''; // 保通企微

            Object.assign(this.orderInfo, inQry);
            const { phoneNo, starPhone, mTel } = this.orderInfo;
            if (isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
                this.orderInfo.step = 'step2';
            } else {
                this.orderInfo.step = 'step1';
            }

            this.fetchPhoneNumber();

            this.fetchOpenId();
        },

        fetchOpenId() {
            const { sid, openId, biz_no } = this.orderInfo;
            if (!sid || !biz_no || openId) return;

            fetchIYBWXOpenId(sid, biz_no).then(res => {
                const { code, result } = res.data;
                if (code == 0) {
                    if (result && result.openid) {
                        this.orderInfo.openId = result.openid;
                        saveOrderInfo(this.orderInfo);
                    }
                }
            });
        },

        actionButtonObserver() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries,) => {
                    this.bottomButtonVisible = !entries[0].isIntersecting;
                }, { threshold: 0.01 });

                const buttonNode = document.getElementById('id_action_button');
                buttonNode && observer.observe(buttonNode);
            }
        },

        fetchPhoneNumber() {
            const { m, phoneNo, starPhone, mTel, channelEnMContent, channelEnMCode } = this.orderInfo;
            if ((!m && !channelEnMContent) || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
                this.orderInfo.step == 'step2' ? this.refreshAudioSrc('shenfenzheng') : this.refreshAudioSrc('shoujihao');
                return this._entryReport();
            }

            const params = {};
            if (m) {
                params.encryptContent = m;
            } else {
                params.channelEnMContent = channelEnMContent;
                params.channelEnMCode = channelEnMCode;
            }
            fetchStarPhoneV4(params).then(res => {
                const { encryptPhone, showPhone } = res.data;
                this.orderInfo.mTel = encryptPhone;
                this.orderInfo.starPhone = showPhone;
                this.orderInfo.phoneNo = showPhone;
                if (isMaskedAndT1Phone(showPhone, encryptPhone)) {
                    this.orderInfo.step = 'step2';
                }
                saveOrderInfo(this.orderInfo);
            }).finally(() => {
                this.orderInfo.step == 'step2' ? this.refreshAudioSrc('shenfenzheng') : this.refreshAudioSrc('shoujihao');
                return this._entryReport();
            });
        },

        bindPhoneAction() {
            const { channel, phoneNo, mTel, encryptNumber } = this.orderInfo;
            if (!encryptNumber) {
                return;
            }

            const params = {
                channelId: channel,
                relatePhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                volcEncryptPhone: encryptNumber,
            }

            bindPhoneAndPrivacy(params).finally(() => {

            });
        },

        onReadPolicy(page) {
            this.policyObj.page = page;
            this.policyObj.visible = true;
        },

        onViewDetail(page) {
            this.policyObj.page1 = page;
            this.policyObj.visible1 = true;
        },

        checkPolicy() {
            this.orderInfo.checked = true;
            this.submit();
        },

        onTextInput({ key, value }) {
            let isChanged = false;
            const channel = this.orderInfo.channel;
            if (key === 'phone' && isPhoneNum(value)) {
                if (is_server_phone(value)) {
                    this.orderInfo.phoneNo = '';
                    return showToast('请输入您本人的手机号码');
                }

                isChanged = true;
                if (isAIChannel(channel)) {
                    this._actionTracking('首页-完成输入手机号');
                }
            } else if (key === 'idCard' && isCardNo(value)) {
                isChanged = true;
                if (isAIChannel(channel)) {
                    this._actionTracking('首页-完成输入身份证');
                }
            } else if (key === 'name' && isPersonName(value)) {
                isChanged = true;
                if (this.prevName != value) {
                    this.prevName = value;
                    if (isAIChannel(channel)) {
                        this._actionTracking(`首页-完成输入姓名`);
                    }
                }
            }
            if (!isChanged) return;

            saveOrderInfo(this.orderInfo);
        },

        onTextFocus({ key, value }) {
            const channel = this.orderInfo.channel;
            if (!isAIChannel(channel)) return;

            if (key === 'idCard' && !value) {
                this._actionTracking('首页-开始输入身份证');
            } else if (key === 'name' && !value) {
                this._actionTracking('首页-开始输入姓名');
            }
        },

        submit() {
            let name = reviseName(this.orderInfo.name1);
            let idCard = reviseIdCard(this.orderInfo.idCard1);
            if (!isPersonName(name)) {
                const temp = reviseName(this.orderInfo.idCard1);
                if (isPersonName(temp)) {
                    name = temp;
                }
            }

            if (!isCardNo(idCard)) {
                const temp = reviseIdCard(this.orderInfo.name1);
                if (isCardNo(temp)) {
                    idCard = temp;
                }
            }

            this.orderInfo.name1 = name;
            this.orderInfo.idCard1 = idCard;

            const { name1, phoneNo, idCard1, starPhone, checked } = this.orderInfo;

            let message = '';
            if (this.orderInfo.step != 'step2') {
                if (!isPhoneNum(phoneNo) && (phoneNo.length != 11 || phoneNo != starPhone)) {
                    message = '请输入正确的手机号';
                }
            }

            if (message) {
                this.orderInfo.step = 'step1';
                return showToast(message);
            }

            if (this.orderInfo.step != 'step2') {
                this.orderInfo.step = 'step2';
                this.userWeComInfo();
                saveOrderInfo(this.orderInfo);
                this.bindPhoneAction();
                domainTracking(this.orderInfo);
                this.refreshAudioSrc('shenfenzheng');
                return this._actionTracking('首页-点击第一步立即领取');
            }

            if (!isPersonName(name1)) {
                message = '请输入正确的姓名';
            } else if (!isCardNo(idCard1)) {
                message = '请输入正确的身份证号码';
            }

            if (message) {
                return showToast(message);
            }

            if (!checked) {
                return this.onReadPolicy('投保须知');
            }

            this._actionTracking('首页-点击第二步立即领取');

            this.submitOrder();

            this.fetchNsfCode();
        },

        fetchNsfCode() {
            const { channel, phoneNo, mTel } = this.orderInfo;
            const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;

            this.$toast.loading({
                message: '数据处理中\n请稍候',
                position: 'middle',
                forbidClick: true,
                duration: 0,
            });

            const params = {
                phone,
                channelId: channel,
                infoKey: TraceLogInfoKeys.iyb_pa_money_disease_send,
                nsfKey: 'zx_to_mf_nsf_key',
            }

            fetchNsfInfo(params).then(res => {
                const { code, data } = res.data || {};
                if (code == 2000) {
                    this.orderInfo.nsfCode = data;
                }
            }).finally(() => {
                this.pushToResult(true);
            });
        },

        submitOrder() {
            const { name1, idCard1, phoneNo, relation, recordSetId, visitorId, traceBackUuid, openId, biz_no } = this.orderInfo;
            const { infoNo, channel, page, mTel, channelCode, sourcePage, mc } = this.orderInfo;

            const planKeys = [TraceLogInfoKeys.iyb_gr_disease_send, TraceLogInfoKeys.iyb_tp_rxy_send, TraceLogInfoKeys.iyb_zy_tk_life_ftb_send];
            if (!this.isPAHidden1) {
                planKeys.push(TraceLogInfoKeys.iyb_pa_money_disease_send);
            }
            // if (!this.isTPHidden1) {
            //     planKeys.push(TraceLogInfoKeys.iyb_tp_rxy_send);
            // }

            const params = {
                page,
                sourcePage,
                infoNo,
                holderName: name1,
                holderIdCard: idCard1,
                holderPhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                relation,
                planKeys: planKeys,
                channelId: channel,
                operatorPhone: mTel || phoneNo,
                channelCode: channelCode || '',
                traceBackUuid: traceBackUuid,
                origin: mc,
            }

            const domain = findDomainAbrr();
            const extendParams = { mc2: mc, domain, recordSetId, visitorId };
            if (openId && biz_no) {
                extendParams.openId = openId;
                extendParams.wechatService = biz_no;
            }
            params.extendParams = JSON.stringify(extendParams);

            createMultiFreeOrder(params).finally(() => {
            });
        },

        userWeComInfo() {
            const { externalUserId, userId, phoneNo, mTel, channel } = this.orderInfo;
            if (!externalUserId) return;

            const params = {
                channelId: channel,
                externalUserId,
                serviceId: userId,
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
            }
            userWeComInfo(params).then(res => {

            });
        },

        pushToResult(save) {
            if (save) {
                this.orderInfo.received = 1;
                saveOrderInfo(this.orderInfo);
            }

            this.fetchNextPath();
            this.bindPhoneAction();
        },

        fetchNextPath() {
            const { channel, phoneNo, idCard1, mTel, nsfCode } = this.orderInfo;

            let robinKey = nsfCode == 1 ? 'iyb_pa_to_mf' : 'iyb_pa_to_mf';

            if (robinKey == 'iyb_pa_to_mf' && window.location.href.indexOf('zhelibao.com') >= 0) {
                robinKey = 'iyb_zlb_pa_to_mf'
            }

            const params = {
                channelId: channel,
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                idCard: idCard1,
                robinKey: robinKey,
            }

            fetchRoundRobinWithAgeZXResultNew(params).then(result => {
                const { path } = result.data || {};
                this.nextObj.path = path || '';
            }).finally(() => {
                this.$toast.clear(true);

                if (channel == 101) {
                    this.nextObj.path = 'IYBTK10Index1';
                }

                if (!domainPathMap[this.nextObj.path]) {
                    this.nextObj.path = 'IYBTK10Index1';
                }

                this.nextObj = { ...this.nextObj, visible: true, showTimer: true };
            });
        },

        jumpNextLink() {
            const { channelCode, channel, page, sourcePage, } = this.orderInfo;
            const { name1, idCard1, phoneNo, mTel, starPhone, openId, biz_no, mc } = this.orderInfo;

            this._actionTracking(`首页-点击弹框好的马上去(${this.nextObj.path})`);

            // 计算年龄
            const relation = GetAge(idCard1) < 18 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: page, sourcePage, mTel, openId, biz_no, mc, action: 'follow',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap[this.nextObj.path];
            setTimeout(() => {
                // window.location.href = `${href}?bizParams=${bizParams}`;
                openNewTab(`${href}?bizParams=${bizParams}`);
            }, 250);
        },

        fetchPrevPath() {
            const { channel, phoneNo, idCard1, mTel } = this.orderInfo;

            const params = {
                channelId: channel,
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                robinKey: 'iyb_pa_send_back',
            }

            if (isCardNo(idCard1)) {
                params.idCard = idCard1;
            }

            fetchRoundRobinWithAgeZXResultNew(params).then(res => {
                const { path } = res.data || {};
                this.prevObj.path = path || '';
            }).finally(() => {
                this.backResultHandler();
            });
        },

        backResultHandler() {
            if (!domainPathMap[this.prevObj.path]) {
                this.prevObj.path = 'IYBTK10Index1';
            }

            this.prevObj = { ...this.prevObj, visible: true };
            this._actionTracking(`首页-点击返回按钮(${this.prevObj.path})`);
        },

        jumpPrevLink() {
            this._actionTracking(`首页-点击返回弹框图片(${this.prevObj.path})`);

            const { channel, name1, phoneNo, idCard1, mTel, page, channelCode, sourcePage, starPhone, openId, mc } = this.orderInfo;

            const relation = GetAge(idCard1) < 18 && GetAge(idCard1) > 0 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: page, sourcePage, mTel, openId, mc, action: 'back',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap[this.prevObj.path];
            setTimeout(() => {
                // window.location.href = `${href}?bizParams=${bizParams}`;
                openNewTab(`${href}?bizParams=${bizParams}`);
            }, 250);
        },

        _entryReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);

            domainTracking(this.orderInfo);

            if (!/^\d+$/.test(this.orderInfo.channel)) {
                this.orderInfo.channel = '333411';
            }
        },

        _actionTracking(name, time) {
            eventTracking(this.orderInfo, name, time);
        },

        pushHistory() {
            const state = { title: "title", url: "#" };
            window.history.pushState(state, "title", "");
        },

        stackPopHandle() {
            if (this.isStep2) {
                this.orderInfo.step = 'step1';
                this.refreshAudioSrc('shoujihao');
                saveOrderInfo(this.orderInfo);
            } else {
                this.refreshAudioSrc('wanliu');
                this.fetchPrevPath();
            }
            this.pushHistory();
        },
    },
}

</script>

<style lang="less" scoped type="text/less">
/deep/ .van-swipe__indicator {
    background-color: #999999;
}

.container_2208291100 {
    width: 3.75rem;
    min-height: 100%;
    font-size: 0.15rem;
    background-color: #F8F8F8;

    img {
        display: block;
        max-width: 100%;
    }

    .banner {
        position: relative;

        .logo-x {
            position: absolute;
            left: 0.15rem;
            top: 0.10rem;

            display: flex;
            flex-direction: row;
            align-items: center;

            .logo-gr {
                width: 0.61rem;
            }

            .logo-tk {
                width: 0.64rem;
            }

            .logo-pa {
                width: 1.24rem;
            }

            .logo+.logo {
                margin-left: 0.10rem;
            }
        }

        .banner-tag {
            position: absolute;
            top: 0.12rem;
            right: 0;
            width: 0.7rem;
            padding: 0.04rem 0 0.03rem 0;
            font-size: 0.12rem;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 0.1rem 0 0 0.1rem;
            text-align: center;
            color: #fff;
        }
    }

    .input-x {
        margin: -0.65rem 0.15rem 0.1rem;
    }

    .introduce {
        margin: 0.15rem auto 0;
        width: 3.45rem;
        border-radius: 0.07rem;
    }

    .policy-x {
        padding: 0 0.15rem 0.15rem;
        font-size: 0.13rem;
        color: #333333;
        line-height: 0.2rem;
        text-align: justify;

        .policy-icon {
            color: #1a5deb;
            font-size: 0.15rem;
            vertical-align: -0.01rem;
        }

        .policy-txt {
            color: #1a5deb;
            font-weight: 500;
        }
    }

    .copyright {
        padding: 0.2rem 0 0.75rem;
        text-align: center;
        color: #888888;
        font-size: 0.12rem;
        font-weight: 500;
        line-height: 1.8;

        .beian {
            margin-top: 0.10rem;
            color: #888888;
        }
    }

    .bottom-x {
        position: fixed;
        margin: 0 auto;
        padding: 0.15rem 0;
        inset: auto 0 0.2rem 0;
        width: 3rem;
        text-align: center;
        font-size: 0.2rem;
        font-weight: 700;
        color: #ffffff;
        border-radius: 0.25rem;
        box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
        background: linear-gradient(270deg,
                rgb(255, 16, 46),
                rgb(253, 123, 69));
        animation: banner_btn 1.35s linear infinite;

        .hand {
            position: absolute;
            top: 0.15rem;
            left: 75%;
            width: 18%;
            animation: banner_hand 1.25s linear infinite;
        }

        @keyframes banner_btn {
            0% {
                transform: scale(1);
            }

            40% {
                transform: scale(1);
            }

            70% {
                transform: scale(0.95);
            }

            100% {
                transform: scale(1);
            }
        }

        @keyframes banner_hand {
            0% {
                transform: translate(-0.1rem, -0.1rem);
            }

            45% {
                transform: translate(0.1rem, 0.1rem);
            }

            70% {
                transform: translate(0.1rem, 0.1rem);
            }

            100% {
                transform: translate(-0.1rem, -0.1rem);
            }
        }
    }
}
</style>
