<template>
    <div class="container_2208310930">
        <audio id="id_audio_2310081000" preload="auto" type="audio/wav"></audio>
        <div class="header" id="id_header_banner">
            <div class="header-mark">
                <img class="header-mark-icon" src="@/assets/imgs/common/icon_selected.png" />
                <span class="header-mark-text">你已成功加强保障</span>
            </div>
            <div class="header-board">
                <div class="header-board-name">{{ preOrderInfo.name }}</div>
                <div class="header-board-list">
                    <div v-for="item in preOrderInfo.list" :key="item.key" class="header-board-list-item">
                        <div class="header-board-list-item-key">{{ item.key }}</div>
                        <div class="header-board-list-item-value">{{ item.value }}</div>
                    </div>
                </div>
                <div class="header-board-img">
                    <div class="header-board-img-upgrade">{{ preOrderInfo.upgradeDate }}</div>
                    <div class="header-board-img-end">{{ preOrderInfo.endDate }}</div>
                </div>
            </div>
        </div>
        <img src="@/assets/imgs/ZhongAn/ZA15/img03.png" class="banner">
        <HHInputBox :obj="orderInfo" class="main-x" @input="onTextInput" @submit="submitAction" @view="onViewPolicy">
        </HHInputBox>
        <van-tabs v-model="currentTab" scrollspy sticky>
            <van-tab key="保障内容" name="保障内容" title="保障内容">
                <div class="section">
                    <HHPlan @click="onViewPolicy({ name: '保障详情' })"></HHPlan>
                </div>
            </van-tab>
            <van-tab key="产品特色" name="产品特色" title="产品特色">
                <div class="section">
                    <div class="section-title">产品特色</div>
                    <img alt="产品特色" src="@/assets/imgs/IYB/IYBGR04/img02.png">
                </div>
            </van-tab>
            <van-tab key="理赔说明" name="理赔说明" title="理赔说明">
                <div class="section">
                    <div class="section-title">理赔流程说明</div>
                    <HHClaimProcess></HHClaimProcess>
                    <div class="section-title">常见问题</div>
                    <HHAskAnswer></HHAskAnswer>
                </div>
            </van-tab>
        </van-tabs>
        <div class="copyright">
            <div class="wrapper">
                <img src="@/assets/imgs/logo/baotong_3.png">
                <img src="@/assets/imgs/logo/guoren.png">
            </div>
            <p>产品名称：国任优选特定疾病保险</p>
            <p>本产品由国任财产保险股份有限公司授权<br>保通保险代理有限公司销售服务</p>
            <p style="margin-top: 0.10rem;">互联网专属产品<br>本页面由保通保险代理有限公司提供</p>
        </div>
        <div v-if="bottomButtonVisible" class="button-x" @click="submitAction">
            立即投保<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <HHSilentViewer :obj="policyObj" :obj1="orderInfo" @click="onSilentUpgrade"></HHSilentViewer>
        <HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
        <HHTabViewer :obj="policyObj" @ok="onAcceptPolicy"></HHTabViewer>
        <HHWarnHint :obj="orderInfo" @view="onViewPolicy({ name: '客户告知书' })"></HHWarnHint>
        <HHFailPopup :obj="failObj" @click="pushFailLink"></HHFailPopup>
        <!--录屏-->
        <IYBRecord1 :obj="orderInfo"></IYBRecord1>
    </div>
</template>

<script>
import moment from "moment";
import HHPlan from "./components/HHPlan";
import HHClaimProcess from "./components/HHClaimProcess";
import HHTabViewer from "./components/HHTabViewer";
import HHInputBox from "./components/HHInputBox";
import HHPolicyViewer from "./components/HHPolicyViewer";
import HHSilentViewer from "./components/HHSilentViewer";
import HHWarnHint from "./components/HHWarnHint";
import HHAskAnswer from "./components/HHAskAnswer";
import HHFailPopup from "./components/HHFailPopup";
import IYBRecord1 from "@/views/components/IYBRecord1";
import { domainPathMap, audioObj2 } from "@/views/ZYBX/src";
import { isMaskedAndT1Phone, isPhoneNum, TraceLogInfoKeys, url_safe_b64_encode, url_safe_b64_decode, } from "@/assets/js/common";
import { fetchRoundRobinWithAgeCommonFail, fetchStarPhoneV4, fetchInfoByOrderNo } from "@/api/insurance-api";
import { checkOrderParams, dispatchWithBitwise, eventTracking, loadOrderInfo, orderInfo, saveOrderInfo, submitOrderInfo } from "./function";

export default {
    name: "Index4",
    components: {
        HHAskAnswer, HHSilentViewer,
        HHPlan, HHClaimProcess, HHWarnHint, HHFailPopup,
        HHPolicyViewer, HHInputBox, HHTabViewer, IYBRecord1,
    },
    data() {
        return {
            orderInfo,
            currentTab: '',       // 切换的tab
            isAutoSubmit: true,    // 自动拉起支付
            bottomButtonVisible: false,
            failObj: { v: false, path: '' },
            policyObj: { v: false, page: '', v1: false, page1: '', v2: false, isAccept: false, belongs: 'v1' },
            preOrderInfo: {
                name: '',
                list: [
                    { key: '被保险人', value: '' },
                    { key: '保单号', value: '' },
                    { key: '保障期限', value: '' },
                ],
                startDate: '',
                upgradeDate: '',
                endDate: '',
            },
            audioObj: audioObj2,
        }
    },
    created() {
        this.viewDidLoad();
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.$nextTick(() => { // 监听滚动事件
            this.addIntersectionObserver();
        });

        this.makeAudioWork();

        this.refreshAudioSrc('jiabao');
    },
    methods: {
        makeAudioWork() {
            const func = () => {
                try {
                    const oAudio = document.querySelector('#id_audio_2310081000');
                    if (oAudio && !oAudio.alreadyPlay) {
                        oAudio.play().then(() => {
                            oAudio.alreadyPlay = true;
                        });

                        oAudio.addEventListener("canplay", () => {
                            console.log('音频可播放', Date.now());
                            oAudio.play(); // 音频可流畅播放时触发
                        });

                        // 监听播放结束事件
                        oAudio.addEventListener('ended', () => {
                            console.log('音频播放结束', Date.now());
                            for (let key in this.audioObj) {
                                const arr = this.audioObj[key];
                                const idx = arr.indexOf(oAudio.src);
                                if (idx >= 0) {
                                    setTimeout(() => {
                                        const tmp = arr[idx + 1];
                                        if (tmp) {
                                            oAudio.src = tmp;
                                            oAudio.load();
                                        }
                                    }, 3000);

                                    return;
                                }
                            }
                        });
                    }
                } catch (e) {

                }
            };

            window.onclick = func;
            window.ontouchstart = func;
        },

        refreshAudioSrc(key) {
            const oAudio = document.querySelector('#id_audio_2310081000');
            if (!oAudio) {
                return;
            }
            if (oAudio.src.indexOf(key) >= 0) {
                return;
            }
            const arr = this.audioObj[key];
            if (!arr) {
                return '';
            }
            oAudio.src = arr[0];
            oAudio.load();
        },

        addIntersectionObserver() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries, observer) => {
                    const entry1 = entries.find(v => v.target.id == 'id_action_button');
                    if (entry1) {
                        this.bottomButtonVisible = !entry1.isIntersecting;
                    }

                    const entry2 = entries.find(v => v.target.id == 'id_header_banner');
                    if (entry2 && !entry2.isIntersecting) {
                        observer.unobserve(entry2.target);
                        this._scrollBehaviorReport();
                    }
                }, { threshold: 0.10 });

                const node1 = document.getElementById('id_action_button');
                const node2 = document.getElementById('id_header_banner');
                node1 && observer.observe(node1);
                node2 && observer.observe(node2);
            }
        },

        onAcceptPolicy() {
            this.orderInfo.checked = true;
            if (this.orderInfo.v2) {
                return;
            }
            if (this.policyObj.isAccept) {
                return this.policyObj.v2 = true;
            }
            this.submitAction();
        },

        // 初始化
        viewDidLoad() {
            const query = this.$route.query || {};
            query.source = query.source || 'direct'; // source要以链接携带的参数为准
            query.action = query.action || 'direct'; // action要以链接携带的参数为准
            query.sourcePage = query.sourcePage || ''; // sourcePage要以链接携带的参数为准
            query.channelCode = query.cld || query.channelCode || '';
            query.openId = query.openId || '';
            query.biz_no = query.biz_no || '';
            query.mc = query.mc || '';

            const inStore = loadOrderInfo() || {};
            Object.assign(this.orderInfo, inStore, query);

            try {
                if (query.bizParams) {
                    const params = JSON.parse(url_safe_b64_decode(query.bizParams));
                    Object.assign(this.orderInfo, params);
                }
            } catch (error) {

            }

            this.orderInfo.planKey = TraceLogInfoKeys.iyb_gr_disease_low_jd_default_cube_base;
            this.orderInfo.infoKey = TraceLogInfoKeys.iyb_gr_disease_low_jd_default_cube_base;
            this.orderInfo.identifier = 'IYBGR04Index4';
            this.orderInfo.school = '1';
            this.orderInfo.checked = (this.orderInfo.channel + '').length >= 4;

            const { phoneNo, starPhone, mTel } = this.orderInfo;
            if (!isPhoneNum(phoneNo) && isMaskedAndT1Phone(starPhone, mTel)) {
                this.orderInfo.phoneNo = starPhone;
            } else {
                this.orderInfo.starPhone = '';
            }

            const { prevName, prevDate, prevPolicy, name1 } = this.orderInfo;
            this.preOrderInfo.name = prevName || '';
            if (prevDate) {
                this.preOrderInfo.startDate = prevDate;
                this.preOrderInfo.upgradeDate = moment(prevDate).add(1, 'months').format('YYYY-MM-DD');
                this.preOrderInfo.endDate = moment(prevDate).add(1, 'years').subtract(1, 'days').format('YYYY-MM-DD');
            }
            const list = [
                { key: '保单号', value: prevPolicy || '' },
                { key: '保障期限', value: `${this.preOrderInfo.startDate}至${this.preOrderInfo.endDate}` }
            ]
            if (name1) {
                list.unshift({ key: '投保人', value: name1 || '' })
            }
            this.preOrderInfo.list = list;

            this.fetchPhoneNumber();
        },
        // 手机号解密
        fetchPhoneNumber() {
            const { m, phoneNo, starPhone, mTel } = this.orderInfo;
            if (!m || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
                return this._entryBehaviorReport();
            }

            const params = { encryptContent: m };
            fetchStarPhoneV4(params).then(res => {
                const { encryptPhone, showPhone } = res.data;
                if (isMaskedAndT1Phone(showPhone, encryptPhone)) {
                    this.orderInfo.mTel = encryptPhone;
                    this.orderInfo.starPhone = showPhone;
                    this.orderInfo.phoneNo = showPhone;
                    saveOrderInfo();
                }
            }).finally(() => {
                return this._entryBehaviorReport();
            });
        },
        // 输入框输入，自动拉起下单
        onTextInput() {
            saveOrderInfo();

            if (this.isAutoSubmit) {
                this.submitAction('AUTO');
            }
        },
        // 点击提交按钮
        submitAction(from) {
            const { code, msg, } = checkOrderParams();
            if (from != 'AUTO' || code == 0 || msg == '用户协议未同意') {
                this._actionTracking('点击立即投保按钮');
            }
            if (code != 0) {
                if (msg == '用户协议未同意') {
                    dispatchWithBitwise(2);
                    return this.onViewPolicy({ name: '健康告知', isPolicy: true, isAccept: true });
                }
                return (from != 'AUTO') && dispatchWithBitwise(7, msg);
            }

            dispatchWithBitwise(2);
            this.policyObj.v2 = true;
        },

        onSilentUpgrade(ret) {
            this._actionTracking(ret ? '默认升级' : '正常升级');
            this.orderInfo.school = ret ? '1' : '0';
            this.orderInfo.infoKey = ret ? TraceLogInfoKeys.iyb_gr_disease_low_jd_default_cube_base : TraceLogInfoKeys.iyb_gr_disease_low_jd_cube_base;
            this.onSubmitOrder();
        },

        onSubmitOrder() {
            dispatchWithBitwise(2);

            this._actionTracking('点击立即领取按钮');

            const { params } = checkOrderParams();
            const extendParams = params.extendParams || {};
            extendParams.payMode = 'jdpay';
            params.extendParams = JSON.stringify(extendParams);

            this.$toast.loading({
                message: '订单提交中\n请稍候',
                forbidClick: true,
                duration: 0,
            });

            submitOrderInfo(params,).then(url => {
                this.$toast.clear(true);
                return window.location.href = url;
            }).catch(err => {
                this.$toast.clear(true);

                const message = err.msg || '';
                if (message.includes('未通过')) {
                    return dispatchWithBitwise(1, message);
                }
                if (message.indexOf('不能重复购买') < 0 && message.indexOf('已存在保单') < 0 && message.indexOf('投保份数') < 0) {
                    return this.fetchFailLink(message);
                }
                this.fetchInfoByOrderNo(message);
            }).finally(() => {
                saveOrderInfo();
            });
        },

        // 根据orderNo查询订单信息
        fetchInfoByOrderNo(message, enterUpgrade) {
            const { relation, callbackUrl } = this.orderInfo;
            if (!callbackUrl || callbackUrl.indexOf('http') < 0) {
                return !enterUpgrade && this.fetchFailLink(message);
            }

            const idCardNo = this.orderInfo[`idCard${relation}`];
            const params = { infoKey: TraceLogInfoKeys.iyb_gr_disease_low_jd_cube_base, insuredIdCard: idCardNo };

            fetchInfoByOrderNo(params).then(r => {
                const { code, data } = r.data;
                if (code != 2000 || !data) {
                    return !enterUpgrade && this.fetchFailLink(message);
                }

                const { infoNo } = data || {};
                if (infoNo) {
                    const params = `infoNo=${infoNo}`;
                    let href = callbackUrl.replace(/&?infoNo=[^?&]*/ig, '');
                    href = href.indexOf('?') > 0 ? `${href}&${params}` : `${href}?${params}`;

                    return window.location.href = href;
                }
                return !enterUpgrade && this.fetchFailLink(message);
            }).catch(() => {
                return !enterUpgrade && this.fetchFailLink(message);
            });
        },

        fetchFailLink(message) {
            const { channel, phoneNo, mTel, relation } = this.orderInfo;

            const params = {
                channelId: channel,
                idCard: this.orderInfo[`idCard${relation}`],
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                robinKey: 'iyb_mf_common_fail',
                currentPage: 'IYBGR04',
            }

            fetchRoundRobinWithAgeCommonFail(params).then(r => {
                this.failObj.path = r.data.path || '';
            }).finally(() => {
                if (this.orderInfo.channel.length < 4) {
                    return dispatchWithBitwise(1, message);
                }

                if (this.failObj.path == 'NotFound') {
                    return dispatchWithBitwise(1, '投保失败，您可以选择为家人投保');
                }

                this.failObj.v = true;
                this._actionTracking(`显示核保失败弹窗(${this.failObj.path})`);
            });
        },

        pushFailLink() {
            let path = this.failObj.path;
            if (!domainPathMap[path]) {
                path = 'IYBTK10Index1';
            }
            this._actionTracking(`点击核保失败图片(${path})`);

            const { channel, relation, mTel, phoneNo, channelCode, starPhone, identifier, name1, idCard1 } = this.orderInfo;

            const params = {
                channel, cld: channelCode, mTel, relation, source: identifier, action: 'forward',
                name1, idCard1, [`name${relation}`]: this.orderInfo[`name${relation}`], [`idCard${relation}`]: this.orderInfo[`idCard${relation}`]
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else if (isMaskedAndT1Phone(starPhone, mTel)) {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));

            setTimeout(() => {
                const href = domainPathMap[path];
                window.location.href = `${href}?bizParams=${bizParams}`;
            }, 250);
        },

        onViewPolicy({ name, isPolicy, isAccept }) {
            this.policyObj.isAccept = isAccept;
            this.policyObj.belongs = 'v1';
            this.policyObj[isPolicy ? 'v1' : 'v'] = true;
            this.policyObj[isPolicy ? 'page1' : 'page'] = name;
        },

        _scrollBehaviorReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd } = timing || {};
            const detentionTime = moment() - domContentLoadedEventEnd;
            this._actionTracking('首页滚动', detentionTime);
        },

        _entryBehaviorReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);
        },

        _actionTracking(name, time = 0) {
            eventTracking(name, time);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2208310930 {
    width: 3.75rem;
    min-height: 100vh;
    font-size: 0.15rem;
    background-color: #f2f2f2;

    img {
        display: block;
        max-width: 100%;
    }

    .header {
        padding: 0.2rem 0;
        font-family: sans-serif;
        background: url("~@/assets/imgs/ZhongAn/ZA15/img02.png") no-repeat top;
        background-size: cover;

        .header-mark {
            padding: 0 0 0.15rem;
            display: flex;
            align-items: center;
            justify-content: center;

            .header-mark-icon {
                width: 0.2rem;
            }

            .header-mark-text {
                margin-left: 0.1rem;
                font-size: 0.18rem;
                font-weight: 600;
                color: #fe5c00;
            }
        }

        .header-board {
            box-sizing: border-box;
            margin: 0 auto;
            padding: 0 0.15rem 0.05rem;
            width: 3.4rem;
            background-color: #ffffff;
            border-radius: 0.12rem;
            box-shadow: 0 0 0.1rem 0 #ffb596;

            .header-board-name {
                padding: 0.15rem 0 0.1rem;
                font-size: 0.18rem;
                font-weight: 500;
                color: #1d243e;
            }

            .header-board-list {
                .header-board-list-item {
                    display: flex;
                    justify-content: space-between;
                    padding: 0.05rem 0;

                    .header-board-list-item-key {
                        color: #999999;
                    }

                    .header-board-list-item-value {
                        color: #474e66;
                    }
                }
            }

            .header-board-img {
                position: relative;
                margin-top: 0.1rem;
                width: 100%;
                height: 1.05rem;
                background: url("~@/assets/imgs/common/upgrade_result.png") no-repeat top;
                background-size: 100%;
                color: #a0a4b3;
                font-size: 0.12rem;

                .header-board-img-upgrade {
                    position: absolute;
                    left: 0.85rem;
                    top: 0.6rem;
                }

                .header-board-img-end {
                    position: absolute;
                    right: 0.1rem;
                    top: 0.6rem;
                }
            }
        }
    }

    .main-x {
        position: relative;
        margin: 0 auto 0.1rem;
    }

    .section {
        background-color: #ffffff;

        .section-title {
            display: flex;
            justify-content: center;
            align-items: center;

            color: #333333;
            font-size: 0.18rem;
            font-weight: 500;
            line-height: 0.45rem;

            &::before,
            &::after {
                content: " ";
                width: 0.55rem;
                height: 0.13rem;
                background: no-repeat center/100%;
            }

            &::before {
                margin-right: 0.1rem;
                background-image: url("~@/assets/imgs/common/icon_needle_left.png");
            }

            &::after {
                margin-left: 0.1rem;
                background-image: url("~@/assets/imgs/common/icon_needle_right.png");
            }
        }
    }

    .button-x {
        position: fixed;
        inset: auto 0 0.25rem 0;
        margin: 0 auto;
        padding: 0.15rem 0;
        width: 3rem;
        font-size: 0.2rem;
        color: #ffffff;
        font-weight: 700;
        text-align: center;
        border-radius: 999px;
        box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
        background: linear-gradient(270deg,
                rgb(255, 16, 46),
                rgb(253, 123, 69));
        animation: button_animate 1.35s linear infinite;

        .hand {
            position: absolute;
            top: 0.25rem;
            left: 75%;
            width: 18%;
            animation: hand_animate 1s linear infinite;
        }
    }

    .copyright {
        padding: 0 0 1rem;
        color: #969799;
        font-size: 0.12rem;
        line-height: 1.6;
        text-align: center;
        background-color: #f8f8f8;

        .wrapper {
            padding: 0.2rem 0 0.15rem;
            display: flex;
            align-items: center;
            justify-content: space-evenly;

            img {
                width: 20%;
            }
        }
    }

    @keyframes button_animate {
        0% {
            transform: scale(1);
        }

        40% {
            transform: scale(1);
        }

        70% {
            transform: scale(0.95);
        }

        100% {
            transform: scale(1);
        }
    }

    @keyframes hand_animate {
        0% {
            transform: translate(-0.1rem, -0.1rem);
        }

        45% {
            transform: translate(0.1rem, 0);
        }

        70% {
            transform: translate(0.1rem, 0);
        }

        100% {
            transform: translate(-0.1rem, -0.1rem);
        }
    }
}
</style>
