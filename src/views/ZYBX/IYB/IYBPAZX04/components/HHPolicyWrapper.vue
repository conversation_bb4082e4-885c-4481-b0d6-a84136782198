<template>
    <div class="container_2307181640">
        <p class="header">保险条款</p>
        <div v-for="(item) in policyList" :key="item.name" class="section" @click="onViewDetail(item.name)">
            <p v-if="item.title" class="section-title">{{ item.title }}</p>
            <div class="section-main">
                <span>{{ item.name }}</span>
                <van-icon name="arrow"/>
            </div>
            <p v-if="item.extra" class="section-extra">{{ item.extra }}</p>
        </div>
    </div>
</template>

<script>
import {policyList} from '../src';

export default {
    data() {
        return {policyList,}
    },
    methods: {
        onViewDetail(v) {
            this.$emit('view', v);
        }
    }
}
</script>

<style lang="less" scoped type="text/less">

.container_2307181640 {
    margin: 0.15rem 0.15rem 0;
    padding: 0.20rem 0 0.10rem;
    border-radius: 0.10rem;
    background-color: #FFFFFF;

    .header {
        text-align: center;
        font-size: 0.18rem;
        font-weight: bold;
    }

    .section {
        padding: 0.10rem 0.18rem 0;
        font-size: 0.14rem;
        line-height: 0.18rem;
        color: #333333;

        .section-title {
            padding: 0.10rem 0 0.10rem;
            font-size: 0.16rem;
            line-height: 1.5;
            font-weight: 500;
        }

        .section-main {
            padding: 0.05rem 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: #737680;
        }

        .section-extra {
            font-size: 0.13rem;
            color: #a0a4b3;
        }
    }
}
</style>
