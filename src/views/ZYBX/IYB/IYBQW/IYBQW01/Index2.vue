<template>
  <div class="container_2305091400"></div>
</template>

<script>
import { userWeComOrderInfo } from "@/api/insurance-api";

export default {
  name: "Index2",
  data() {
    return {
      href: "https://channel.zhelibao.com/mktprod/rp/ZYBX/TKZX02/Index11",
    };
  },

  mounted() {
    const inQry = this.$route.query || {};
    const { externalUserId, channel } = inQry;

    const params = {
      channelId: channel,
      externalUserId,
    };
    userWeComOrderInfo(params)
      .then((res) => {
        const { code, data } = res.data;
        if (code == 2000) {
          const callbackUrl = data.callbackUrl + "";
          if (callbackUrl.startsWith("http")) {
            this.href = callbackUrl;
          }
        }
      })
      .finally(() => {
        return this.jumpNextLink();
      });
  },

  methods: {
    jumpNextLink() {
      const inQry = this.$route.query || {};
      const { userId, externalUserId, channel = "252199" } = inQry;

      this.href = this.href.replace(/&?channel=[^?&]+/gi, "");

      let params = `channel=${channel}`;
      if (externalUserId) {
        params = `${params}&externalUserId=${externalUserId}`;
        if (userId) {
          params = `${params}&userId=${userId}`;
        }
      }

      this.href =
        this.href.indexOf("?") > 0
          ? `${this.href}&${params}`
          : `${this.href}?${params}`;

      window.location.href = this.href;
    },
  },
};
</script>

<style lang="less" scoped>
.container_2305091400 {
  min-height: 100vh;
  background-color: #ffffff;
}
</style>
