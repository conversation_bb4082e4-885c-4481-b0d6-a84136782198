<template>
	<div class="container_2301031055">
		<img v-lazy="rateSheet" />
	</div>
</template>

<script>
import rate1 from '@/assets/imgs/NW/NWZA06/rate1.png';
import rate2 from '@/assets/imgs/NW/NWZA06/rate2.png';

export default {
	name: "UURateSheet",
	props: { obj: Object },
	computed: {
		rateSheet() {
			return this.obj.belongs == 'v2' ? rate2 : rate1;
		}
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2301031055 {
		background-color: #ffffff;
        
		img {
			display: block;
			width: 100%;
		}
	}
</style>
