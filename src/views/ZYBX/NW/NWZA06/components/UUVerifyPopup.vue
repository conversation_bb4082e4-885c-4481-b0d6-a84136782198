<template>
	<van-popup v-model="obj.visible" class="container_2212271130" :close-on-click-overlay="false">
		<img class="verify-img" :src="imgUrl" alt="投保中" />
		<div class="verify-text">
			投保中，请稍候
		</div>
	</van-popup>
</template>

<script>
import imgUrl from '@/assets/imgs/NW/NWZA12/verify.png';

export default {
	name: "UUVerifyPopup",
	props: { obj: { visible: Boolean } },
	data() {
		return {
			imgUrl,
		};
	},
};
</script>

<style scoped lang="less">
.container_2212271130 {
	width: 3.75rem;
	background-color: transparent;
}

.verify-img {
	display: block;
	width: 100%;
	height: auto;
	object-fit: fill;
}

.verify-text {
	position: absolute;
	top: 1.78rem;
	left: 0.36rem;
	font-size: 0.2rem;
	font-weight: 600;
	color: #000;

	&::after {
		content: "...";
		display: inline-block;
		animation: loading 1s infinite linear;
	}
	@keyframes loading {
		0% {
			content: "...";
		}
		33% {
			content: ".";
		}
		66% {
			content: "..";
		}
	}
}
</style>
