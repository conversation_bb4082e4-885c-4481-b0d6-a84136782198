import {Toast} from "vant";
import {isPhoneNum, TraceLogInfoKeys} from "@/assets/js/common";
import {actionTracking} from "@/assets/js/api";
import {bxStorage} from "@/utils/store_util";

export const createOrderInfo = () => {
    const orderInfo = {
        productKey: TraceLogInfoKeys.nw_health_send,
        identifier: '',
        received: 0,     // 是否已领取过
        hintVisible: true,
        checked: true,
        step: 'step1',
        relation: 1,
        infoNo: '',
        m: '',
        tel: '',
        mTel: '',
        channel: '1',
        channelCode: '',
        sourcePage: '',
        starPhone: '',
        phoneNo: '',
        name1: '',
        idCard1: '',
        traceBackUuid: '',
        channelEnMCode: '',
        channelEnMContent: '',

        userId:'', // 企微
        externalUserId: '', // 企微
        partner: '',
        nsfCode: 1, // NSF评分
    };

    return orderInfo;
}

export const documentList = [
        {page: '健康告知'},
        {page: '免责说明'},
        {page: '投保须知'},
        {
            page: '保险条款', list: [
                {page: '个人重大疾病保险条款（互联网2022版A款）',},
            ]
        },
        {page: '重要提示'},
        {page: '个人信息保护政策'},
        {page: '客户告知书'},
]

export const pdfFileObj = {
    '个人重大疾病保险条款（互联网2022版A款）': '/NW/NWZAZX03/dir1.pdf',
    '众安保险特殊职业类别表': '/NW/NWZAZX03/dir2.pdf',
    '个人信息保护政策': '/NW/Common/za_gerenxinxibaohuzhengce.pdf',
}

export const eventTracking = (orderInfo, name, time = 0) => {
    const {productKey, mTel, tel, channel, phoneNo, identifier} = orderInfo;
    const phone = mTel || tel || (isPhoneNum(phoneNo) ? phoneNo : '');

    actionTracking({
        page: `暖哇众安赠险${identifier}(${productKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: productKey,
        time: time,
    }).then(res => {
        const {code, data} = res;
        if (code == 2000) {
            const {mobileId} = data || {};
            if (mobileId) {
                orderInfo.mTel = mobileId;
                saveOrderInfo(orderInfo);
            }
        }
    });
}

export const showToast = (message = '', duration = 2000) => {
    Toast({
        forbidClick: true,
        message: message,
        position: 'center',
        duration: duration, // 弹窗时间毫秒
    });
}

export const loadOrderInfo = () => {
    return bxStorage.getObjItem('NWZAZX01_INFO') || {};
}

export const saveOrderInfo = (orderInfo) => {
    bxStorage.setObjItem('NWZAZX01_INFO', orderInfo);
}

