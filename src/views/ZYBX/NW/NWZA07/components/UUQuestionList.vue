<template>
	<div class="container_2209010900">
		<van-collapse v-model="collapseName" accordion>
			<van-collapse-item v-for="item in questionList" :key="item.q" :name="item.q" :title="item.q">
				<span v-html="item.a"></span>
			</van-collapse-item>
		</van-collapse>
		<div v-if="!showAll" class="showAll" @click="showAll= true">更多问题&nbsp;</div>
	</div>
</template>

<script>
import { askedQuestions, askedQuestions1, } from "../src";

export default {
	name: "UUQuestionList",
	props: {
		obj: Object,
	},
	data() {
		const questions = this.obj.belongs == 'v2' ? askedQuestions1 : askedQuestions;
		return {
			questions,
			showAll: questions.length <= 4,
			collapseName: '',
		}
	},
	computed: {
		questionList() {
			if (this.showAll) {
				return this.questions;
			}
			return this.questions.slice(0, 4);
		},
	},
	methods: {},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2209010900 {
		color: #333333;
		font-size: 0.14rem;
		text-align: justify;
		background-color: #ffffff;

		.showAll {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #f77f3c;
			line-height: 0.35rem;
			font-size: 0.13rem;

			&::after {
				content: " ";
				height: 0.08rem;
				width: 0.08rem;
				background: url("~@/assets/imgs/common/sprites.png") no-repeat 0 -0.32rem;
				background-size: 1.6rem 3.2rem;
			}
		}
	}
</style>
