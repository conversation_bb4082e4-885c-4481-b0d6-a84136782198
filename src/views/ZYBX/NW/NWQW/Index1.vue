<template>
    <div class="container_2305091400">
        <img v-if="isMask" src="@/assets/imgs/NW/NWQW/img01.png"/>
    </div>
</template>

<script>

import {bxStorage} from "@/utils/store_util";
import {actionTracking} from "@/assets/js/api";
import {fetchIYBQwChainMode, userWeComOrderInfo} from "@/api/insurance-api";
import {isAndroid, isIos, isInAliPay, isInWx, TraceLogInfoKeys, Url, url_safe_b64_encode} from "@/assets/js/common";

export default {
    name: "Index1",
    data() {
        return {
            isMask: false,
            href: 'https://sr0lLef8bo_5zb19brqip8t-3rz_7c0.aibangbaoxian.net/marketfront/insurancecdn/ZYBX/NWZAZX01/Index',
            obj: {externalUserId: '', channel: '', identifier: 'NWQWIndex1',},
        }
    },

    mounted() {
        const query = this.$route.query || {};
        let {externalUserId, channel} = query;
        let obj = null;

        if (!externalUserId) {
            obj = this.loadLocalData();
            externalUserId = obj.externalUserId;
            !channel && externalUserId && (channel = obj.channel);
        }

        if (!externalUserId) {
            obj = this.loadLocalData('NWZAZX01_INFO');
            externalUserId = obj.externalUserId;
            !channel && externalUserId && (channel = obj.channel);
        }

        if (!externalUserId) {
            obj = this.loadLocalData('NWZAZX03_INFO');
            externalUserId = obj.externalUserId;
            !channel && externalUserId && (channel = obj.channel);
        }

        const temp = this.loadLocalData('NWZAZX01_INFO');
        if (temp.received) {
            bxStorage.setRawItem('NWZAZX01_Random_Path', 'NWZAZX01Index1');
        }

        this.obj.externalUserId = externalUserId || '';
        this.obj.channel = channel || '252405';

        Object.assign(query, this.obj);
        this.saveLocalData(query);

        const platform = isAndroid() ? 'android' : isIos() ? 'iOS' : 'other';
        const envCode = isInWx() ? 'wechat' : isInAliPay() ? 'alipay' : 'browser';

        this.eventTracking(`首页(${platform}_${envCode})`);

        if (!isInWx()) {
            return this.fetchNextJump();
        }

        let chainMode = 0;
        fetchIYBQwChainMode(this.obj.channel).then(r => {
            const {mode} = r.data || {};
            chainMode = mode;
        }).finally(() => {
            if (chainMode == 1) {
                if (isAndroid()) {
                    const url = url_safe_b64_encode(window.location.href);
                    setTimeout(() => {
                        window.location.href = `${Url}/Insurance/info/checkIsJump?url=${url}`;
                    }, 500);
                }
                return this.isMask = true;
            }

            this.fetchNextJump();
        });
    },

    methods: {
        fetchNextJump() {
            const externalUserId = this.obj.externalUserId;

            if (!externalUserId) {
                return this.jumpNextLink();
            }

            const params = {
                externalUserId,
                platform: 'nw_za',
            }
            userWeComOrderInfo(params).then(res => {
                const {code, data} = res.data;
                if (code == 2000) {
                    const callbackUrl = data.callbackUrl + '';
                    if (callbackUrl.startsWith('http')) {
                        this.href = callbackUrl;
                    }
                }
            }).finally(() => {
                return this.jumpNextLink();
            });
        },

        jumpNextLink() {
            const externalUserId = this.obj.externalUserId;
            const channel = this.obj.channel || '252405';
            let params = `channel=${channel}`;
            if (externalUserId) {
                params = `${params}&externalUserId=${externalUserId}`;
            }

            this.href = this.href.replace(/&?channel=[^?&]+/ig, '');
            this.href = this.href.indexOf('?') > 0 ? `${this.href}&${params}` : `${this.href}?${params}`;

            setTimeout(() => {
                window.location.href = this.href;
            }, 250);
        },

        saveLocalData(obj, key = 'NWQWIndex1_INFO') {
            bxStorage.setObjItem(key, obj);
        },

        loadLocalData(key = 'NWQWIndex1_INFO') {
            return bxStorage.getObjItem(key) || {};
        },

        eventTracking(name, time = 0) {
            const {channel, identifier} = this.obj;

            const infoKey = TraceLogInfoKeys.nw_tp_za_jh_send;

            actionTracking({
                page: `暖哇企微中间页${identifier}(${infoKey})-${name}`,
                channel: channel,
                infoKey: infoKey,
                time: time,
            }).then(res => {

            });
        }
    }
}
</script>

<style lang="less" scoped>

.container_2305091400 {
    height: 100%;
    overflow: hidden;

    img {
        display: block;
        max-width: 100%;
    }
}

</style>
