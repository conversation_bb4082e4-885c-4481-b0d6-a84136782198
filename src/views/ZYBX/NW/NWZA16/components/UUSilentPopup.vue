<template>
    <van-popup v-model="obj.v2" class="container_2212271130" position="bottom" round>
        <div class="header">
            <div class="header-title"><span class="view-txt">99%</span>用户选择优化，享更多保障</div>
            <img class="header-img" :src="bannerUrl">
            <div class="header-premium">
                <p>变更保障后每月保费由<span>{{premiumObj.first}}</span>元变更为<span>{{premiumObj.next}}</span>元，自次月开始扣费并生效</p>
                <p>变更后产品计划的保障责任、赔付比例、免赔额详见<span class="view-txt" @click.stop="onViewPolicy('投保须知',true)">《投保须知》</span></p>
            </div>
        </div>
        <div id="id_2212271130" class="content">
            <Policy_Health></Policy_Health>
        </div>
        <div class="footer">
            <div class="footer-text">
                我同意次月变更保障
                <span class="view-txt" @click.stop="onViewPolicy('免责条款',true)">《免责条款》</span>
                <span class="view-txt" @click.stop="onViewPolicy('投保须知',true)">《投保须知》</span>
                <span class="view-txt" @click.stop="onViewPolicy('保险条款',true)">《保险条款》</span>
                <span class="view-txt" @click.stop="onViewPolicy('重要提示',true)">《重要提示》</span>
                <span class="view-txt" @click.stop="onViewPolicy('特别约定',true)">《特别约定》</span>
                <span class="view-txt" @click.stop="onViewPolicy('个人信息保护政策',true)">《个人信息保护政策》</span>
                <span class="view-txt" @click.stop="onViewPolicy('客户告知书',true)">《客户告知书》</span>
                <span class="view-txt" @click.stop="onViewPolicy('授权委托书与代扣服务协议',true)">《授权委托书与代扣服务协议》</span>
                <span class="view-txt" @click.stop="onViewPolicy('个人健康险变更申请书',true)">《个人健康险变更申请书》</span>
                <span class="view-txt" @click.stop="onViewPolicy('保障详情',false)">《保障详情》</span>
                <span class="view-txt" @click.stop="onViewPolicy('费率表',false)">《费率表》</span>
            </div>
            <div class="button-container">
                <div class="button" @click="onclick(false)">
                    不同意
                </div>
                <div class="button color" @click="onclick(true)">
                    同意并继续
                </div>
            </div>
        </div>
    </van-popup>
</template>

<script>
import Policy_Health from "./Policy/Policy_Health";
import ImgUrl1 from '@/assets/imgs/NW/NWZA10/upgrade01.png'
import ImgUrl2 from '@/assets/imgs/NW/NWZA10/upgrade02.png'
import ImgUrl3 from '@/assets/imgs/NW/NWZA10/upgrade03.png'
import { getPolicyVersion } from '../function'
export default {
    name: "HHSilentViewer",
    components: { Policy_Health, },
    props: { obj: Object, obj1: Object },
    watch: {
        'obj.v2': {
            handler(v) {
                this.obj1.isLowPricePage = !v;
                this.scrollToTop();
            },
        },
    },
    computed: {
        premiumObj() {
            const { totalPremium, upgradePremium } = this.obj1;
            return { first: totalPremium, next: upgradePremium };
        },
        bannerUrl() {
            const belongs = getPolicyVersion(this.obj1[`idCard${this.obj1.relation}`], false)
            return belongs == 'v5' ? ImgUrl2 : belongs == 'v6' ? ImgUrl3 : ImgUrl1
        },
    },
    methods: {
        onclick(ret) {
            this.obj.v2 = false;
            this.$emit('click', ret);
        },
        onViewPolicy(name, isPolicy) {
            this.obj.belongs = 'v2';
            this.obj[isPolicy ? 'v1' : 'v'] = true;
            this.obj[isPolicy ? 'page1' : 'page'] = name;
        },
        scrollToTop() {
            const node = document.getElementById('id_2212271130');
            node && (node.scrollTop = 0);
        }
    },
}
</script>

<style lang="less" scoped type="text/less">
    .container_2212271130 {
        height: 95%;
        width: 3.75rem;
        inset: auto 0 0 0;
        margin: 0 auto;

        display: flex;
        flex-direction: column;

        .view-txt {
            color: #ff8c41;
            font-weight: 500;
        }

        .header {
            background: url("~@/assets/imgs/common/mask.png") top center no-repeat;
            background-size: 100%;
            
            position: relative;

            .header-title {
                padding: 0.2rem 0 0;
                font-size: 0.17rem;
                font-weight: 500;
                text-align: center;
            }

            .header-img {
                margin: 0 auto 0;
                display: block;
                width: 70%;
            }

            .header-premium {
                font-size: 0.12rem;
                color: #333333;
                text-align: center;
                line-height: 0.2rem;
            }
        }

        .content {
            flex: 1;
            overflow: auto;
        }

        .footer {
            background-color: #ffffff;
            border-top: #f2f2f2 1px solid;

            .footer-text {
                padding: 0.05rem 0.1rem 0;
                font-size: 0.13rem;
                line-height: 1.5;
                text-align: justify;
            }

            .button-container {
                display: flex;
                justify-content: space-around;

                .button {
                    margin: 0.05rem 0.2rem 0.1rem;
                    padding: 0.12rem 0;
                    width: 1.5rem;
                    border-radius: 999px;
                    font-size: 0.16rem;
                    font-weight: 500;
                    text-align: center;
                    color: #666666;
                    border: #eeeeee 1px solid;
                    background-color: #ffffff;
                }

                .color {
                    color: #ffffff;
                    background: linear-gradient(to right, #ffcc00, #ff9500);
                }
            }
        }
    }
</style>
