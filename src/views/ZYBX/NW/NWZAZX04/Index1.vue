<template>
	<div class="container_2302131350" :class="{ container_2302131350_step1: !isStep2 }">
		<div class="banner">
			<img v-if="!isStep2" src="@/assets/imgs/NW/NWZAZX04/img01.png">
			<img v-else src="@/assets/imgs/NW/NWZAZX04/img01_1.png">
			<img :src="bannerLogo" class="banner-logo" />
		</div>
		<HHInputBox :obj="orderInfo" class="inputBox" @focus="onTextFocus" @input="onTextInput" @submit="submit"></HHInputBox>
		<div class="policy-x">
			<van-icon :name="orderInfo.checked ?'checked':'circle'" color="#FFFFFF" size="0.16rem" @click="orderInfo.checked = !orderInfo.checked" />
			我已阅读并同意
			<span class="policy-x-read" @click="onLookPolicy('健康告知')">《健康告知》</span>
			<span class="policy-x-read" @click="onLookPolicy('免责说明')">《免责说明》</span>
			<span class="policy-x-read" @click="onLookPolicy('投保须知')">《投保须知》</span>
			<span class="policy-x-read" @click="onLookPolicy('产品说明')">《产品说明》</span>
			<span class="policy-x-read" @click="onLookPolicy('保险条款')">《保险条款》</span>
			<span class="policy-x-read" @click="onLookPolicy('重要提示')">《重要提示》</span>
			<span class="policy-x-read" @click="onLookPolicy('个人信息保护政策')">《个人信息保护政策》</span>
			<span class="policy-x-read" @click="onLookPolicy('服务协议')">《服务协议》</span>
			<span class="policy-x-read" @click="onLookPolicy('客户告知书')">《客户告知书》</span>
		</div>

        <div class="step1" v-if="!isStep2">
            <img alt="" src="@/assets/imgs/NW/NWZAZX04/img02.png">
        </div>

        <div class="step2 detail-box" v-if="isStep2">
            <img alt="" src="@/assets/imgs/IYBZA/IYBZAZX01/feature_zj.png">
        </div>

        <div class="copyright" v-if="isStep2">
            <p>该保险产品由众安在线财产保险股份有限公司承保并负责理赔，产品页面仅供参考，具体责任描述以保险合同为准。</p>
            <p>众安保险最近季度偿付能力符合监管要求，详情请参见众安保险官网(www.zhongan.com) 偿付能力信息披露。</p>
            <p>本产品由爱邦保险经纪有限公司销售，爱邦保险经纪有限公司版权所有</p>
			<p @click="onClickNwFilingNumber">{{ insCompany.number }}</p>
			<p>上海暖哇科技有限公司提供技术支持</p>
		</div>

        <div class="copyright" v-else>
            <p>本产品由爱邦保险经纪有限公司销售，爱邦保险经纪有限公司版权所有</p>
			<p @click="onClickNwFilingNumber">{{ insCompany.number }}</p>
			<p>上海暖哇科技有限公司提供技术支持</p>
		</div>

		<van-popup v-model="isMask" class="mask" :close-on-click-overlay="false">
			<img src="@/assets/imgs/common/icon_browser.png" />
		</van-popup>
		<HHTabPopup :obj="policyObj" @clickPolicy="onLookDetail" @okAction="checkPolicy"></HHTabPopup>
		<HHPolicyPopup :obj="popObj"  @clickPolicy="onLookDetail"></HHPolicyPopup>
		<HHPrevPopup :obj="prevObj" @click="jumpPrevLink"></HHPrevPopup>
		<HHNextPopup :obj="nextObj" @click="jumpNextLink"></HHNextPopup>
		<NwRecord @SetIseeBiz="SetIseeBiz"></NwRecord>
	</div>
</template>

<script>
import {
    TraceLogInfoKeys,
    reviseIdCard,
    reviseName,
	GetAge,
	isAIChannel,
	isCardNo,
	isMaskedAndT1Phone,
	isPersonName,
	isPhoneNum,
	isInWx,
	isAndroid,
	url_safe_b64_encode,
	Url,
} from "@/assets/js/common";
import {
	createMultiFreeOrder,
	fetchRoundRobinWithAgeZXResultNew,
	fetchRoundRobinWithPhoneOrderFilter,
	fetchStarPhoneV4,
	userWeComInfo,
    fetchNsfInfo,
} from '@/api/insurance-api';
import { createNoticeList, createOrderInfo, eventTracking, loadOrderInfo, saveOrderInfo, showToast } from './src';
import HHInputBox from "./components/HHInputBox";
import HHPolicyPopup from "./components/HHPolicyPopup";
import HHPrevPopup from "./components/HHPrevPopup";
import HHNextPopup from "./components/HHNextPopup";
import HHTabPopup from "./components/HHTabPopup";
import { domainPathMap, is_server_phone } from "@/views/ZYBX/src";
import NwRecord from "@/views/components/NwRecord";
import { onClickNwFilingNumber } from '@/utils/filing_number';

const iconObj = {
	'ksb': require('@/assets/imgs/common/icon_kuansongbao.png'),
	'kxb': require('@/assets/imgs/common/icon_kuanxinbao.png'),
	'zgb': require('@/assets/imgs/common/icon_zhenguibao.png'),
	'nbyx': require('@/assets/imgs/common/icon_nuanbaoyanxuan.png'),
}

export default {
	name: "Index1",
	data() {
		const orderInfo = createOrderInfo();
		return {
			prevName: '',
			orderInfo,
			noticeList: [],
			policyObj: { visible: false, page: '', idx: 0, type: 'A' },
			popObj: { visible: false, page: '', dir: '' },
			prevObj: { visible: false, showTimer: false, path: '' },
			nextObj: { visible: false, showTimer: true, },
			isMask: false,
		}
	},
	components: {
		HHTabPopup,
		HHNextPopup,
		HHInputBox,
		HHPolicyPopup,
		HHPrevPopup,
		NwRecord,
	},
	computed: {
		bannerLogo() {
			return iconObj[this.orderInfo.partner];
		},
		isStep2() {
			return this.orderInfo.step == 'step2';
		},
	},
	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.init();

		this.noticeList = createNoticeList();

		this.$nextTick(() => {
			this.pushHistory();
			window.addEventListener("popstate", this.stackPopHandle);
		});
	},
	destroyed() {
		window.removeEventListener("popstate", this.stackPopHandle);
	},
	methods: {
		onClickNwFilingNumber,
		SetIseeBiz(value) {
			this.orderInfo.traceBackUuid = value
		},
		//初始化
		init() {
			const inQry = this.$route.query || {};
			inQry.partner = inQry.partner || 'za';
			const orderInfo = loadOrderInfo();

			Object.assign(this.orderInfo, orderInfo);
            this.orderInfo.productKey = TraceLogInfoKeys.nw_tp_jh_006_send;
			this.orderInfo.identifier = 'NWZAZX04Index1';
			this.orderInfo.sourcePage = '暖哇众安太平聚合V240905';
			this.orderInfo.channel = inQry.channel || orderInfo.channel || '1';
			this.orderInfo.channelCode = inQry.cld || orderInfo.channelCode || '';
			this.orderInfo.partner = inQry.partner;

			if (isInWx() && (inQry.isMask == 1)) {
				if (isAndroid()) {
					const url = url_safe_b64_encode(window.location.href);
					setTimeout(() => {
						window.location.href = `${Url}/Insurance/info/checkIsJump?url=${url}`;
					}, 500);
				}
				this._entryReport();
				return this.isMask = true;
			}

			if (this.orderInfo.received) {
				this._entryReport();
                return this.fetchNsfCode();
			}

			inQry.externalUserId = inQry.externalUserId || ''; // 企微
			inQry.userId = inQry.userId || ''; // 企微

			Object.assign(this.orderInfo, inQry);

			this.orderInfo.checked = false;

			// const {phoneNo, starPhone, mTel} = this.orderInfo;
			// if (isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
			//     this.orderInfo.step = 'step2';
			// } else {
			//     this.orderInfo.step = 'step1';
			// }
			this.orderInfo.step = 'step1';
			this.fetchPhoneNumber();
		},

		fetchPhoneNumber() {
			const { m, phoneNo, starPhone, mTel, channelEnMContent, channelEnMCode } = this.orderInfo;
			if ((!m && !channelEnMContent) || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
				return this._entryReport();
			}

			const params = {};
			if (m) {
				params.encryptContent = m;
			} else {
				params.channelEnMContent = channelEnMContent;
				params.channelEnMCode = channelEnMCode;
			}
			fetchStarPhoneV4(params).then(res => {
				const { encryptPhone, showPhone } = res.data;
				this.orderInfo.mTel = encryptPhone;
				this.orderInfo.starPhone = showPhone;
				this.orderInfo.phoneNo = showPhone;
				// if (isMaskedAndT1Phone(showPhone, encryptPhone)) {
				//     this.orderInfo.step = 'step2';
				// }
				saveOrderInfo(this.orderInfo);
			}).finally(() => {
				return this._entryReport();
			});
		},

		onLookPolicy(page) {
			this.policyObj.idx = this.isStep2 ? 1 : 0;
			this.$nextTick(() => {
				this.policyObj = { ...this.policyObj, visible: true, page };
			});
		},

		onLookDetail(page) {
			this.popObj = { ...this.popObj, page, visible: true };
		},

		checkPolicy() {
            this._actionTracking('点击协议弹窗确认按钮');
			this.orderInfo.checked = true;
			this.submit();
		},

		onTextInput({ key, value }) {
			let isChanged = false;
			const channel = this.orderInfo.channel;
			if (key === 'phone' && isPhoneNum(value)) {
				if (is_server_phone(value)) {
					this.orderInfo.phoneNo = '';
					return showToast('请输入您本人的手机号码');
				}

				isChanged = true;
				if (isAIChannel(channel)) {
					this._actionTracking('首页-完成输入手机号');
				}
			} else if (key === 'idCard' && isCardNo(value)) {
				isChanged = true;
				if (isAIChannel(channel)) {
					this._actionTracking('首页-完成输入身份证');
				}
			} else if (key === 'name' && isPersonName(value)) {
				isChanged = true;
				if (this.prevName != value) {
					this.prevName = value;
					if (isAIChannel(channel)) {
						this._actionTracking(`首页-完成输入姓名`);
					}
				}
			}
			if (!isChanged) return;

			saveOrderInfo(this.orderInfo);
		},

		onTextFocus({ key, value }) {
			const channel = this.orderInfo.channel;
			if (!isAIChannel(channel)) return;

			if (key === 'idCard' && !value) {
				this._actionTracking('首页-开始输入身份证');
			} else if (key === 'name' && !value) {
				this._actionTracking('首页-开始输入姓名');
			}
		},

		submit() {
			let name = reviseName(this.orderInfo.name1);
			let idCard = reviseIdCard(this.orderInfo.idCard1);
			if (!isPersonName(name)) {
				const temp = reviseName(this.orderInfo.idCard1);
				if (isPersonName(temp)) {
					name = temp;
				}
			}

			if (!isCardNo(idCard)) {
				const temp = reviseIdCard(this.orderInfo.name1);
				if (isCardNo(temp)) {
					idCard = temp;
				}
			}

			this.orderInfo.name1 = name;
			this.orderInfo.idCard1 = idCard;

			const { name1, phoneNo, idCard1, checked, starPhone, mTel } = this.orderInfo;

			let message = '';

			if (this.isStep2) {
				if (!isPhoneNum(phoneNo) && !isMaskedAndT1Phone(starPhone, mTel)) {
					message = '请输入正确的手机号';
				}
			} else {
				if (!isPhoneNum(phoneNo) && !(isMaskedAndT1Phone(starPhone, mTel) && phoneNo == starPhone)) {
					message = '请输入正确的手机号';
				}
			}

			if (message) {
				this.orderInfo.step = 'step1';
				saveOrderInfo(this.orderInfo);
				return showToast(message);
			}

			if (!this.isStep2) {
				// if (!checked) {
				// 	return this.onLookPolicy('个人信息保护政策');
				// }
				this.orderInfo.step = 'step2';
                this.orderInfo.checked = false;
				saveOrderInfo(this.orderInfo);
				this.userWeComInfo();
				return this._actionTracking('首页-点击第一步立即领取');
			}

			if (!isPersonName(name1)) {
				message = '请输入正确的姓名';
			} else if (!isCardNo(idCard1)) {
				message = '请输入正确的身份证号码';
			}

			if (message) {
				return showToast(message);
			}

			saveOrderInfo(this.orderInfo);

			if (!checked) {
				return this.onLookPolicy('健康告知');
			}

			this._actionTracking('首页-点击第二步立即领取');

			this.submitOrder();

            this.fetchNsfCode();
		},

        fetchNsfCode() {
			const {channel, phoneNo, mTel, productKey } = this.orderInfo;
			const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;

			this.$toast.loading({
                message: '数据处理中\n请稍候',
				position: 'middle',
				forbidClick: true,
				duration: 0,
			});

			const params = {
				phone,
                channelId: channel,
				infoKey: productKey,
                nsfKey: 'nw_zx_to_mf_nsf_key',
			}

            fetchNsfInfo(params).then(res => {
				const { code, data } = res.data || {};
				if (code == 2000) {
                    this.orderInfo.nsfCode = data;
				}
			}).finally(() => {
				return this.pushToResult('success', true);
			});
		},

		submitOrder() {
			const { name1, idCard1, phoneNo, relation, traceBackUuid, sourcePage, productKey } = this.orderInfo;
			const { infoNo, channel, identifier, mTel, channelCode } = this.orderInfo;

			const params = {
				infoNo,
				sourcePage,
				traceBackUuid,
				relation,
				channelCode,
				channelId: channel,
				page: identifier,
				holderName: name1,
				holderIdCard: idCard1,
				holderPhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				planKeys: [productKey],
				operatorPhone: mTel || phoneNo,
			}

			createMultiFreeOrder(params).finally(err => {

            });
		},

		userWeComInfo() {
			const { externalUserId, userId, phoneNo, mTel, channel } = this.orderInfo;
			if (!externalUserId) return;

			const params = {
				channelId: channel,
				externalUserId,
				serviceId: userId,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
			}
			userWeComInfo(params).then(res => {

			});
		},

		pushToResult(state, save) {
			if (save) {
				this.orderInfo.received = 1;
				saveOrderInfo(this.orderInfo);
			}

			this.fetchNextProduct();
		},

		fetchNextProduct() {
            const { channel, phoneNo, idCard1, mTel, nsfCode } = this.orderInfo;

            const robinKey = nsfCode == 1 ? 'za_to_za_mf' : 'za_to_za_mf';

			const params = {
				channelId: channel,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				idCard: idCard1,
				robinKey: robinKey,
			}

			fetchRoundRobinWithAgeZXResultNew(params).then(res => {
				const { path } = res.data || {};
				this.nextObj.path = path || '';
			}).finally(() => {
				this.$toast.clear(true);

				if (channel == 101) {
					this.nextObj.path = 'NWZA10Index1';
				}

				if (!domainPathMap[this.nextObj.path]) {
					this.nextObj.path = 'NWZA10Index1';
				}

				this.nextObj = { ...this.nextObj, visible: true, showTimer: true };
			});
		},

		jumpNextLink() {
			const {channelCode, channel, identifier, sourcePage, name1, idCard1, phoneNo, mTel, starPhone} = this.orderInfo;
            this._actionTracking(`首页-点击弹框好的马上去(${this.nextObj.path})`);

            // 计算年龄
            const relation = GetAge(idCard1) < 18 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: identifier, sourcePage, mTel, action: 'follow',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap[this.nextObj.path];
            setTimeout(() => {
                window.location.href = `${href}?bizParams=${bizParams}`;
            }, 250);
		},
		fetchPrevProduct() {
			const { channel, phoneNo, idCard1, mTel } = this.orderInfo;

			const params = {
				channelId: channel,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				robinKey: 'nw_send_community_back',
			}

			if (isCardNo(idCard1)) {
				params.idCard = idCard1;
				fetchRoundRobinWithAgeZXResultNew(params).then(res => {
					this.prevProductHandle(res.data.path);
				}).catch(() => {
					this.prevProductHandle();
				});
			} else {
				fetchRoundRobinWithPhoneOrderFilter(params).then(res => {
					this.prevProductHandle(res.data.path);
				}).catch(() => {
					this.prevProductHandle();
				});
			}
		},

		prevProductHandle(path = '') {
			if (!domainPathMap[path]) {
				path = 'NWZA10Index1';
			}

			this.prevObj = { ...this.prevObj, visible: true, path };
			this._actionTracking(`首页-点击返回按钮(${this.prevObj.path})`);
		},

		jumpPrevLink() {
			this._actionTracking(`首页-点击返回弹框图片(${this.prevObj.path})`);

            const { channelCode, channel, identifier, sourcePage, name1, idCard1, phoneNo, mTel, starPhone } = this.orderInfo;
            const relation = GetAge(idCard1) < 18 && GetAge(idCard1) > 0 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: identifier, sourcePage, mTel, action: 'back',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap[this.prevObj.path];
            setTimeout(() => {
                window.location.href = `${href}?bizParams=${bizParams}`;
            }, 250);
		},

		pushHistory() {
			const state = { title: "title", url: "#" };
			window.history.pushState(state, "title", "");
		},

		stackPopHandle() {
			if (this.isStep2) {
				this.orderInfo.step = 'step1';
				saveOrderInfo(this.orderInfo);
			} else {
				this.fetchPrevProduct();
			}
			this.pushHistory();
		},

		_entryReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);

            const host = window.location.host;
            this._actionTracking(`域名(${host})`);
            
			if (!/^\d+$/.test(this.orderInfo.channel)) {
				this.orderInfo.channel = '333411';
			}
		},

		_actionTracking(name, time) {
			eventTracking(this.orderInfo, name, time);
		},
	},
}
</script>

<style lang="less" scope type="text/less">
	.container_2302131350 {
		min-height: 100%;
		font-size: 0.15rem;
		background: linear-gradient(to bottom, #1a6cf5 30%, #2e5be3);

		img {
			display: block;
			max-width: 100%;
		}

		.mask {
			width: 100%;
			height: 100%;
			background-color: unset;

			img {
				display: block;
				width: 3.5rem;
				margin: 0 auto;
			}
		}

		.banner {
			position: relative;

			.banner-logo {
				position: absolute;
				top: 0.1rem;
				left: 1.2rem;
				width: 0.2rem;
			}
		}

        .step1{
            margin: 0.15rem;
        }

		.detail-box {
			margin: 0.15rem 0.15rem;
		}

		.policy-x {
			margin: 0.15rem 0.15rem 0;
			color: rgba(255, 255, 255, 0.8);
			text-align: justify;
			font-size: 0.12rem;
			line-height: 1.6;

			.policy-x-read {
				color: #ffdd95;
				font-weight: 500;
			}

			.policy-x-trace {
				margin-top: 0.15rem;
				color: rgba(255, 255, 255, 0.75);
			}
		}

		.copyright {
			padding: 0.1rem 0.20rem 0.2rem;
			text-align: center;
			font-size: 0.12rem;
			font-weight: 400;
			line-height: 1.8;
			color: rgba(255, 255, 255, 0.75);
		}
        .inputBox{
            margin-top: -0.15rem;
        }
	}

    .container_2302131350_step1{
        background:#3845E8;
        .inputBox{
            margin-top: -0.15rem;
        }
    }
</style>
