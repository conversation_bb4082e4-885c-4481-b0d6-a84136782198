<template>
	<div class="container_2302131350">
		<div class="banner">
			<img src="@/assets/imgs/NW/NWTPZX02/img01.png">
            <!-- <div class="banner-txt">
                已有 <span style="color:#ffffaa">11,440</span> 人免费领取
            </div> -->
		</div>
		<HHInputBox :obj="orderInfo" @focus="onTextFocus" @input="onTextInput" @submit="submit" @onViewPolicy="onViewPolicy"></HHInputBox>
		<img class="introduct-x" src="@/assets/imgs/NW/NWTPZX02/img02.png" />
		<div v-if="jumpObj.bottomVisible" class="bottom-button" @click="submit">
			免费投保
			<img alt="hand" class="hand" src="@/assets/imgs/common/icon_hand1.png">
		</div>
		<van-popup v-model="isMask" class="mask" :close-on-click-overlay="false">
			<img src="@/assets/imgs/common/icon_browser.png" />
		</van-popup>
		<HHTabViewer :obj="policyObj" @ok="checkPolicy" @view="onViewDetail"></HHTabViewer>
		<HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
		<HHWarnHint :obj="orderInfo" @onViewPolicy="onViewPolicy1"></HHWarnHint>
		<HHPrevPopup :obj="jumpObj" @click="jumpPrevLink"></HHPrevPopup>
		<HHNextPopup :obj="jumpObj" @click="jumpNextLink"></HHNextPopup>
		<!--录屏-->
		<NwRecord @SetIseeBiz="SetIseeBiz"></NwRecord>
	</div>
</template>

<script>
import NwRecord from "@/views/components/NwRecord";
import HHInputBox from "./components/HHInputBox";
import HHPolicyViewer from "./components/HHPolicyViewer";
import HHPrevPopup from "./components/HHPrevPopup";
import HHNextPopup from "./components/HHNextPopup";
import HHTabViewer from "./components/HHTabViewer";
import HHWarnHint from "./components/HHWarnHint";
import { domainPathMap, is_server_phone, } from "@/views/ZYBX/src";
import { reviseIdCard, reviseName, GetAge, isCardNo, isMaskedAndT1Phone, isPersonName, isPhoneNum, isInWx, isAndroid, url_safe_b64_encode, Url } from "@/assets/js/common";
import { createMultiFreeOrder, fetchRoundRobinWithAgeZXResultNew, fetchStarPhoneV4, fetchNsfInfo,} from '@/api/insurance-api';
import { toastAndAction, eventTracking, loadOrderInfo, orderInfo, saveOrderInfo } from './src';


export default {
	name: "Index10",
	data() {
		return {
			isMask: false,
			prevName: '',
			orderInfo,
			prevIsRequesting: false,
			policyObj: { visible: false, page: '', visible1: false, page1: '', belongs: 'v1' },
			jumpObj: { visible: false, visible1: false, showTimer: true, showTimer1: false, path: '', path1: '', bottomVisible: false },
		}
	},
	components: {
		HHTabViewer,
		HHNextPopup,
		HHInputBox,
		HHPolicyViewer,
		HHPrevPopup,
		HHWarnHint,
		NwRecord,
	},
	computed: {
		isStep2() {
			return this.orderInfo.step == 'step2';
		},
	},
	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.init();

		this.$nextTick(() => {
			this.pushViewHistory();
			this.observeIntersection();
			window.addEventListener("popstate", this.popViewHistory);
		});
	},
	destroyed() {
		window.removeEventListener("popstate", this.popViewHistory);
	},
	methods: {
		SetIseeBiz(value) {
			this.orderInfo.traceBackUuid = value;
		},
		//初始化
		init() {
			const inQry = this.$route.query || {};
			const orderInfo = loadOrderInfo();

			Object.assign(this.orderInfo, orderInfo);
			this.orderInfo.identifier = 'NWTPZX02Index10';
			this.orderInfo.sourcePage = '暖哇太平聚合赠险直投V2400715';
			this.orderInfo.channel = inQry.channel || orderInfo.channel || '1';
			this.orderInfo.channelCode = inQry.cld || orderInfo.channelCode || '';

			if (isInWx() && (inQry.isMask == 1)) {
				if (isAndroid()) {
					const url = url_safe_b64_encode(window.location.href);
					setTimeout(() => {
						window.location.href = `${Url}/Insurance/info/checkIsJump?url=${url}`;
					}, 500);
				}
				this._entryBehaviorReport();
				this.orderInfo.hintVisible = false;
				return this.isMask = true;
			}

			if (this.orderInfo.received) {
				this._entryBehaviorReport();
                return this.fetchNsfCode();
			}

			Object.assign(this.orderInfo, inQry);

			this.orderInfo.checked = this.orderInfo.channel >= 1000;

			const { phoneNo, starPhone, mTel } = this.orderInfo;
			if (!isPhoneNum(phoneNo) && isMaskedAndT1Phone(starPhone, mTel)) {
				this.orderInfo.phoneNo = starPhone;
			} else {
				this.orderInfo.starPhone = '';
			}
			if (isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
				this.orderInfo.step = 'step2';
			} else {
				this.orderInfo.step = 'step1';
			}
			this.fetchPhoneNumber();
		},

		fetchPhoneNumber() {
			const { m, phoneNo, starPhone, mTel, channelEnMContent, channelEnMCode } = this.orderInfo;
			if ((!m && !channelEnMContent) || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
				return this._entryBehaviorReport();
			}

			const params = {};
			if (m) {
				params.encryptContent = m;
			} else {
				params.channelEnMContent = channelEnMContent;
				params.channelEnMCode = channelEnMCode;
			}
			fetchStarPhoneV4(params).then(res => {
				const { encryptPhone, showPhone } = res.data || {};
				this.orderInfo.mTel = encryptPhone;
				this.orderInfo.starPhone = showPhone;
				this.orderInfo.phoneNo = showPhone;
				saveOrderInfo();
			}).finally(() => {
				return this._entryBehaviorReport();
			});
		},

		onViewPolicy(page) {

			this.policyObj.belongs = this.isStep2 ? 'v2' : 'v1';
			this.$nextTick(() => {
				this.policyObj.page = page;
				this.policyObj.visible = true;
			});
		},
		onViewPolicy1(page) {
			this.policyObj.page1 = page;
			this.policyObj.visible1 = true;
			this.policyObj.belongs = this.isStep2 ? 'v2' : 'v1';
		},

		onViewDetail(page) {
			this.policyObj.page1 = page;
			this.policyObj.visible1 = true;
		},

		checkPolicy() {
			this.orderInfo.checked = true;
			this.submit();
		},

		onTextInput({ key, value }) {
			let isDone = false;
			if (key === 'phone' && isPhoneNum(value)) {
				if (is_server_phone(value)) {
					this.orderInfo.phoneNo = '';
					return toastAndAction(3, '请输入您本人的手机号码');
				}

				isDone = true;
				eventTracking('首页-完成输入手机号');
			} else if (key === 'certNo' && isCardNo(value)) {
				isDone = true;
				eventTracking('首页-完成输入身份证');
			} else if (key === 'certName' && isPersonName(value)) {
				isDone = true;
				if (this.prevName != value) {
					this.prevName = value;
					eventTracking(`首页-完成输入姓名`);
				}
			}
			if (!isDone) return;

			saveOrderInfo();
		},

		onTextFocus({ key, value }) {
			if (key === 'certNo' && !value) {
				eventTracking('首页-开始输入身份证');
			} else if (key === 'certName' && !value) {
				eventTracking('首页-开始输入姓名');
			}
		},

		submit() {
			let name = reviseName(this.orderInfo.name1);
			let idCard = reviseIdCard(this.orderInfo.idCard1);
			if (!isPersonName(name)) {
				const temp = reviseName(this.orderInfo.idCard1);
				if (isPersonName(temp)) {
					name = temp;
				}
			}

			if (!isCardNo(idCard)) {
				const temp = reviseIdCard(this.orderInfo.name1);
				if (isCardNo(temp)) {
					idCard = temp;
				}
			}

			this.orderInfo.name1 = name;
			this.orderInfo.idCard1 = idCard;

			const { name1, phoneNo, idCard1, checked, starPhone, mTel } = this.orderInfo;

			let message = '';

			if (this.isStep2) {
				if (!isPhoneNum(phoneNo) && !isMaskedAndT1Phone(starPhone, mTel)) {
					message = '请输入正确的手机号';
				}
			} else {
				if (!isPhoneNum(phoneNo) && !(isMaskedAndT1Phone(starPhone, mTel) && phoneNo == starPhone)) {
					message = '请输入正确的手机号';
				}
			}

			if (message) {
				this.orderInfo.step = 'step1';
				saveOrderInfo();
				return toastAndAction(3, message);
			}

			if (!this.isStep2) {
				this.orderInfo.step = 'step2';
				saveOrderInfo();
				return eventTracking('首页-点击第一步立即领取');
			}
			if (!isPersonName(name1)) {
				message = '请输入正确的姓名';
			} else if (!isCardNo(idCard1)) {
				message = '请输入正确的身份证号码';
			}

			if (message) {
				return toastAndAction(3, message);
			}

			saveOrderInfo();

			if (!checked) {
				return this.onViewPolicy('健康告知');
			}

			eventTracking('首页-点击第二步立即领取');

			this.submitOrder();

            this.fetchNsfCode();
		},

        fetchNsfCode() {
			const {channel, phoneNo, mTel, productKey } = this.orderInfo;
			const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;

			this.$toast.loading({
                message: '数据处理中\n请稍候',
				position: 'middle',
				forbidClick: true,
				duration: 0,
			});

			const params = {
				phone,
                channelId: channel,
				infoKey: productKey,
                nsfKey: 'nw_zx_to_mf_nsf_key',
			}

            fetchNsfInfo(params).then(res => {
				const { code, data } = res.data || {};
				if (code == 2000) {
                    this.orderInfo.nsfCode = data;
				}
			}).finally(() => {
				return this.pushToResult('success', true);
			});
		},

		submitOrder() {
			const { productKey, name1, idCard1, phoneNo, relation, traceBackUuid, sourcePage, infoNo, channel, identifier, mTel, channelCode } = this.orderInfo;

			const extendParams = {};

			const params = {
				infoNo,
				sourcePage,
				traceBackUuid,
				relation,
				channelCode,
				channelId: channel,
				page: identifier,
				holderName: name1,
				holderIdCard: idCard1,
				holderPhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				planKeys: [productKey],
				operatorPhone: mTel || phoneNo,
				extendParams: JSON.stringify(extendParams),
			}

			createMultiFreeOrder(params).finally(err => {

            });
		},

		pushToResult(isSaved) {
			if (isSaved) {
				this.orderInfo.received = 1;
				saveOrderInfo();
			}

			this.fetchNextLink();
		},

		fetchNextLink() {
            const { channel, phoneNo, idCard1, mTel, nsfCode } = this.orderInfo;

            const robinKey = nsfCode == 1 ? 'nw_tp_zx_to_next' : 'nw_tp_zx_to_next';

			const params = {
				channelId: channel,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				idCard: idCard1,
				robinKey: robinKey,
			}

			let path = '';
			fetchRoundRobinWithAgeZXResultNew(params).then(res => {
				path = res.data.path || '';
			}).finally(() => {
				this.$toast.clear(true);

				if (channel < 1000) {
					path = 'NWZA10Index1';
				}

				if (!domainPathMap[path]) {
					path = 'NWZA10Index1';
				}

				this.jumpObj.path = path;
				this.jumpObj.visible = true;
			});
		},

		jumpNextLink() {
			const nextPath = this.jumpObj.path;

			eventTracking(`首页-点击弹框好的马上去(${nextPath})`);

			const { channelCode, channel, identifier, sourcePage, name1, idCard1, phoneNo, mTel, starPhone } = this.orderInfo;

            // 计算年龄
            const relation = GetAge(idCard1) < 18 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: identifier, sourcePage, mTel, action: 'follow',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap[nextPath];
            setTimeout(() => {
                window.location.href = `${href}?bizParams=${bizParams}`;
            }, 250);
		},

		fetchPrevLink() {
			if (this.jumpObj.path1) {
				return this.jumpObj.visible1 = true;
			}

			if (this.prevIsRequesting) {
				return;
			}
			this.prevIsRequesting = true;

			const { channel, phoneNo, idCard1, mTel } = this.orderInfo;

			const params = {
				channelId: channel,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				idCard: isCardNo(idCard1) ? idCard1 : '',
				robinKey: 'nw_tp_zx_to_back',
			}

			let path = '';
			fetchRoundRobinWithAgeZXResultNew(params).then(res => {
				path = res.data.path || '';
			}).finally(() => {
				this.$toast.clear(true);

				if (!domainPathMap[path]) {
					path = 'NWZA10Index1';
				}

				this.prevIsRequesting = false;

				this.jumpObj.path1 = path;
				this.jumpObj.visible1 = true;

				eventTracking(`首页-点击返回按钮(${this.jumpObj.path1})`);
			});
		},

		jumpPrevLink() {
			const prevPath = this.jumpObj.path1;

			eventTracking(`首页-点击返回弹框图片(${prevPath})`);

			const { channelCode, channel, identifier, sourcePage, name1, idCard1, phoneNo, mTel, starPhone } = this.orderInfo;
            const relation = GetAge(idCard1) < 18 && GetAge(idCard1) > 0 ? 3 : 1;

            const params = {
                channel, cld: channelCode, relation, source: identifier, sourcePage, mTel, action: 'back',
                [`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
            }

            if (isPhoneNum(phoneNo)) {
                params.phoneNo = phoneNo;
                params.starPhone = '';
            } else {
                params.phoneNo = '';
                params.starPhone = starPhone;
            }

            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap[prevPath];
            setTimeout(() => {
                window.location.href = `${href}?bizParams=${bizParams}`;
            }, 250);
		},

		observeIntersection() {
			if (window.IntersectionObserver) {
				const observer = new IntersectionObserver((entries, observer) => {
					const entry1 = entries.find(v => v.target.id == 'id_action_button');
					if (entry1) {
						this.jumpObj.bottomVisible = !entry1.isIntersecting;
					}
				}, { threshold: 0.10 });

				const node1 = document.getElementById('id_action_button');
				node1 && observer.observe(node1);
			}
		},

		pushViewHistory() {
			const state = { title: "title", url: "#" };
			window.history.pushState(state, "title", "");
		},

		popViewHistory() {
			this.pushViewHistory();
			if (this.isStep2) {
				this.orderInfo.step = 'step1';
				return saveOrderInfo();
			}

			if (this.orderInfo.channel >= 1000) {
				this.fetchPrevLink();
			}
		},

		_entryBehaviorReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			eventTracking('首页', domContentLoadedEventEnd - fetchStart);

			if (!/^\d+$/.test(this.orderInfo.channel)) {
				this.orderInfo.channel = '333411';
			}
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302131350 {
		min-height: 100%;
		font-size: 0.15rem;
		background-color: #3845e8;

		/deep/ .van-swipe__indicator {
			background-color: #666666;
		}

		img {
			display: block;
			max-width: 100%;
		}

		.banner {
			position: relative;
            &-txt{
                position: absolute;
				left: 50%;
				transform: translateX(-50%);
				bottom: 0.28rem;

				width: 3.75rem;
                font-size: 0.12rem;
                color: #ffffff;
                text-align: center;
            }
		}

		.mask {
			width: 100%;
			height: 100%;
			background-color: unset;

			img {
				display: block;
				width: 3.5rem;
				margin: 0 auto;
			}
		}

		.introduct-x {
			margin-bottom: 0.6rem;
			margin-top: -0.1rem;
		}
		.copyright {
			padding: 0.22rem 0.24rem 0.85rem;
			text-align: center;
			font-size: 0.12rem;
			font-weight: 400;
			line-height: 1.8;
			color: #a79781;
			p {
				margin-bottom: 0.08rem;
			}
		}

		.bottom-button {
			position: fixed;
			inset: auto 0 0.25rem 0;
			margin: 0 auto;
			padding: 0.15rem 0;
			width: 3rem;
			font-size: 0.2rem;
			color: #ffffff;
			font-weight: 700;
			text-align: center;
			border-radius: 0.25rem;
			box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
			background: linear-gradient(
				270deg,
				rgb(255, 16, 46),
				rgb(253, 123, 69)
			);
			animation: button_animation 1.5s linear infinite;

			.hand {
				position: absolute;
				top: 0.15rem;
				left: 75%;
				width: 18%;
				animation: hand_animation 1.25s linear infinite;
			}
		}

		@keyframes button_animation {
			0% {
				transform: scale(1);
			}
			40% {
				transform: scale(0.95);
			}
			60% {
				transform: scale(0.95);
			}
			100% {
				transform: scale(1);
			}
		}
		@keyframes hand_animation {
			0% {
				transform: translate(-0.1rem, -0.1rem);
			}
			45% {
				transform: translate(0.1rem, 0.1rem);
			}
			70% {
				transform: translate(0.1rem, 0.1rem);
			}
			100% {
				transform: translate(-0.1rem, -0.1rem);
			}
		}
	}
</style>
