<template>
    <div class="container_2209011100">
        <div class="header">
            <span class="header-title">为谁投保（被保险人）</span>
        </div>
        <div class="relation-options">
            <UUOption v-for="item in relations" :key="item.key"
                :obj="{ text: item.key, active: item.value == orderInfo.relation, width: '0.70rem' }"
                @click="onRelationChange(item.value)">
            </UUOption>
        </div>
        <div id="被保人信息" class="box">
            <div class="box-item">
                <span class="box-item-label">{{ isOwn ? '您的姓名' : '被保人姓名' }}</span>
                <input v-model="orderInfo['name' + orderInfo.relation]" :placeholder="isOwn ? '请输入您的姓名' : '请输入被保人姓名'"
                    class="box-item-input" maxlength="20" type="text"
                    @input="onTextInput('被保人姓名', $event.target.value)">
            </div>
            <div class="box-item">
                <span class="box-item-label">身份证号</span>
                <input v-model="orderInfo['idCard' + orderInfo.relation]" class="cert_no_x box-item-input"
                    maxlength="18" placeholder="信息保密，仅用于投保" type="text"
                    @input="onTextInput('被保人身份证', $event.target.value)">
            </div>
            <div v-if="isOwn && isStarPhone" class="box-item">
                <span class="box-item-label">手机号码</span>
                <input v-model="orderInfo.starPhone" class="box-item-input" disabled type="tel">
            </div>
            <div v-if="isOwn && !isStarPhone" class="box-item">
                <span class="box-item-label">手机号码</span>
                <input v-model="orderInfo.phoneNo" class="box-item-input" maxlength="11"
                    onkeyup="value=value.replace(/\D/g,'')" placeholder="信息保密，仅用于投保" type="tel"
                    @input="onTextInput('手机号码', $event.target.value)">
            </div>
            <div class="box-care">
                <div class="box-care-label">
                    有无医保
                    <van-icon name="question-o" @click="careObj.visible = true" />
                </div>
                <div class="box-care-options">
                    <UUOption v-for="item in insurances" :key="item.key"
                        :obj="{ text: item.key, active: item.value == orderInfo.insurance, width: '1.30rem' }"
                        @click="OnInsuranceChange(item.value)">
                    </UUOption>
                </div>
            </div>
            <div class="box-renewal">
                <span>开通下一年自主重新投保&nbsp;&nbsp;&nbsp;</span>
                <UUOption :obj="{ width: '0.9rem', text: '立即开通', active: orderInfo.isAutoRenewal == 'Y' }"
                    @click="onRenewalChange">
                </UUOption>
            </div>
        </div>
        <div v-if="!isOwn" id="投保人信息" class="box">
            <div class="box-item">
                <span class="box-item-label">您的姓名</span>
                <input v-model="orderInfo.name1" class="box-item-input" maxlength="20" placeholder="请输入您的姓名" type="text"
                    @input="onTextInput('投保人姓名', $event.target.value)">
            </div>
            <div class="box-item">
                <span class="box-item-label">身份证号</span>
                <input v-model="orderInfo.idCard1" class="cert_no_x box-item-input" maxlength="18"
                    placeholder="信息保密，仅用于投保" type="text" @input="onTextInput('投保人身份证', $event.target.value)">
            </div>
            <div v-if="isStarPhone" class="box-item">
                <span class="box-item-label">手机号码</span>
                <input v-model="orderInfo.starPhone" class="box-item-input" disabled type="tel">
            </div>
            <div v-else class="box-item">
                <span class="box-item-label">手机号码</span>
                <input v-model="orderInfo.phoneNo" class="box-item-input" maxlength="11"
                    onkeyup="value=value.replace(/\D/g,'')" placeholder="信息保密，仅用于投保" type="tel"
                    @input="onTextInput('手机号码', $event.target.value)">
            </div>
        </div>
        <UUCarePopup :obj="careObj"></UUCarePopup>
    </div>
</template>

<script>

import { insurances, relations } from "../src";
import UUOption from "./UUOption";
import UUCarePopup from "./UUCarePopup";
import { calculatePremium, } from "../function";
import { isCardNo, isMaskedAndT1Phone, isPhoneNum, secureIdCard } from "@/assets/js/common";

export default {
    name: "UUInputBox",
    components: { UUCarePopup, UUOption },
    props: {
        orderInfo: Object,
    },
    data() {
        return {
            relations,
            insurances,
            careObj: { visible: false },
        }
    },
    computed: {
        isOwn() {
            const { relation } = this.orderInfo;
            return relation == 1;
        },
        isStarPhone() {
            const { starPhone, mTel } = this.orderInfo;
            return isMaskedAndT1Phone(starPhone, mTel);
        },
    },
    mounted() {
        this.premiumCalculate();

        const idCardDeal = () => {
            requestAnimationFrame(() => {
                const inputList = document.querySelectorAll('.cert_no_x');
                for (const input of inputList) {
                    if (input.value.includes('*') && isCardNo(input.value)) {
                        console.log(secureIdCard(input.value))
                        input.value = secureIdCard(input.value);
                    }
                }

                idCardDeal();
            });
        }

        idCardDeal();
    },
    methods: {
        onRelationChange(value) {
            if (this.orderInfo.relation == value) return;
            this.orderInfo.relation = value;
            this.premiumCalculate();
        },
        OnInsuranceChange(value) {
            if (this.orderInfo.insurance == value) return;
            this.orderInfo.insurance = value;
            this.premiumCalculate();
        },
        onRenewalChange() {
            this.orderInfo.isAutoRenewal = this.orderInfo.isAutoRenewal == 'Y' ? 'N' : 'Y';
        },
        premiumCalculate() {
            const { relation, insurance, repay } = this.orderInfo;
            const idCardNo = this.orderInfo[`idCard${relation}`];
            const { low, high } = calculatePremium(idCardNo, insurance, repay);
            this.orderInfo.totalPremium = low;
            this.orderInfo.upgradePremium = high;
        },
        onTextInput(key, value) {
            // console.log(`name => ${name}  value => ${value}`)
            let isFinished = false;
            if (key.includes('身份证')) {
                isFinished = isCardNo(value);
                this.premiumCalculate();
            } else if (key.includes('手机号码')) {
                isFinished = isPhoneNum(value);
            }

            if (key == '投保人姓名' || key == '投保人身份证') {
                const { name1, idCard1 } = this.orderInfo;
                if (name1.includes('*')) {
                    this.orderInfo.name1 = '';
                }
                if (idCard1.includes('*')) {
                    this.orderInfo.idCard1 = '';
                }
            }

            if (key == '被保人姓名' || key == '被保人身份证') {
                const { relation } = this.orderInfo;
                const name2 = this.orderInfo[`name${relation}`];
                const idCard2 = this.orderInfo[`idCard${relation}`];
                if (name2.includes('*')) {
                    this.orderInfo[`name${relation}`] = '';
                }
                if (idCard2.includes('*')) {
                    this.orderInfo[`idCard${relation}`] = '';
                }
                this.premiumCalculate();
            }


            if (!isFinished) return;

            this.$emit('input');
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2209011100 {
    font-size: 0.16rem;
    background-color: #FFFFFF;

    .header {
        padding: 0.20rem 0 0.20rem 0.15rem;

        .header-title {
            position: relative;
            font-size: 0.16rem;
            font-weight: bold;
            color: #1A1A1A;

            &::before {
                content: "";
                width: 100%;
                height: 0.16rem;
                opacity: 0.1;
                background: #f60;
                border-radius: 0.04rem;
                position: absolute;
                left: -0.02rem;
                bottom: -0.02rem;
            }
        }
    }

    .relation-options {
        display: flex;
        align-items: center;
        justify-content: space-evenly;
    }

    .box {
        margin: 0.15rem 0.10rem 0;
        padding: 0 0.15rem;
        border-radius: 0.06rem;
        box-shadow: #DDDDDD 0px 0px 12px;

        .box-item {
            display: flex;
            line-height: 0.5rem;
            color: #333333;
            border-bottom: #F2F2F2 1px solid;

            .box-item-label {
                width: 1.0rem;
            }

            .box-item-input {
                flex: 1;
                border: none;
                outline: none;
                width: 2.0rem;
                background-color: unset;
            }
        }

        .box-care {
            padding-bottom: 0.15rem;

            .box-care-label {
                color: #333333;
                width: 1.0rem;
                height: 0.5rem;
                line-height: 0.5rem;
                font-size: 0.16rem;
            }

            .box-care-options {
                display: flex;
                align-items: center;
                justify-content: space-around;
            }
        }

        .box-renewal {
            display: flex;
            align-items: center;
            padding-bottom: 0.15rem;
            color: #333333;
            font-size: 0.16rem;
        }
    }
}
</style>
