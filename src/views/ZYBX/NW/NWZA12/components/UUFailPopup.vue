<template>
    <van-popup v-model="obj.visible" :close-on-click-overlay="false" class="container_2302231345" position="center">
        <div class="content" @click="onOpen">
            <img src="@/assets/imgs/common/popup_failed.png">
            <div class="button">立即领取（{{ countDown }}s）</div>
        </div>
    </van-popup>
</template>

<script>

export default {
    name: "UUFailPopup",
    props: {obj: Object},
    data() {
        return {
            intervalTimer: null,
            countDown: 3,
        }
    },
    watch: {
        obj(data) {
            if (!data.showTimer) return;
            this.startCountDown();
        },
    },
    mounted() {

    },
    beforeDestroy() {
        this.clearCountDown();
    },
    methods: {
        startCountDown() {
            this.clearCountDown();
            this.countDown = 3;
            this.intervalTimer = setInterval(() => {
                this.countDown--;
                if (this.countDown <= 0) {
                    this.onOpen();
                }
            }, 1000);
        },
        clearCountDown() {
            this.intervalTimer && clearInterval(this.intervalTimer);
        },
        onOpen() {
            this.clearCountDown();
            this.obj.visible = false;
            this.$emit('click');
        },
        onClose() {
            this.clearCountDown();
            this.obj.visible = false;
            this.$emit('close');
        }
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2302231345 {
    width: 85%;
    border-radius: 0.10rem;
    background-color: #FFFFFF;

    .content {
        position: relative;

        img {
            width: 100%;
            display: block;
        }

        .button {
            position: absolute;
            width: 2.0rem;
            height: 0.4rem;
            line-height: 0.4rem;
            border-radius: 0.2rem;
            text-align: center;
            background-color: #FF4C13;
            font-size: 0.16rem;
            color: #FFFFFF;
            bottom: 0.15rem;
            left: 50%;
            transform: translateX(-50%);
        }
    }
}

</style>

