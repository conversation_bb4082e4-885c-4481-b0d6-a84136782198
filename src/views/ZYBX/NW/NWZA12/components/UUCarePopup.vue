<template>
    <Popup v-model="obj.visible" class="container_2211041610" position="center">
        <div class="container">
            <p class="header">以下情况都算有医保：</p>
            <div class="content">
                <p>1.新型农村合作医疗（新农合）；</p>
                <p>2.城乡居民基本医疗保险；</p>
                <p>3.城镇职工基本医疗保险；</p>
                <p>4.城镇居民基本医疗保险；</p>
                <p>5.公费医疗保险等。</p>
            </div>
        </div>
        <div class="button" @click="closeAction"></div>
    </Popup>
</template>

<script>
import {Popup} from "vant";

export default {
    name: "UUCarePopup",
    components: {Popup},
    props: {
        obj: Object,
    },
    methods: {
        closeAction() {
            this.obj.visible = false;
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2211041610 {
    width: 3.0rem;
    background-color: unset;

    .container {
        padding: 0.05rem 0.20rem;
        border-radius: 0.10rem;
        background-color: #FFFFFF;

        .header {
            font-size: 0.17rem;
            font-weight: bold;
            line-height: 0.5rem;
            color: #F08E39;
            border-bottom: #F2F2F2 1px solid;
        }

        .content {
            margin: 0.1rem 0;
            font-size: 0.16rem;
            line-height: 0.25rem;
            color: #333333;
        }
    }

    .button {
        margin: 0.1rem auto;
        width: 0.32rem;
        height: 0.32rem;
        background: url("~@/assets/imgs/common/sprites.png") no-repeat -1.92rem 0;
        background-size: 3.2rem 6.4rem;
    }
}
</style>
