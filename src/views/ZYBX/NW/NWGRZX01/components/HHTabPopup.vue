<template>
    <van-popup v-model="obj.visible" class="container_2302221730" position="bottom" round>
        <van-tabs v-model="obj.page">
            <van-tab v-for="page in pageList" :key="page" :name="page" :title="page.replace(/\d/, '')"></van-tab>
        </van-tabs>
        <div id="id_2302221730" class="content">
            <Policy_List v-if="list.length > 0" :list="list" @clickPolicy="clickPolicy"></Policy_List>

            <Policy_Health v-else-if="obj.page == '健康告知'" @clickPolicy="clickPolicy"></Policy_Health>
            <Policy_Notice v-else-if="obj.page == '投保须知'"></Policy_Notice>
            <Policy_Special v-else-if="obj.page == '特别约定'"></Policy_Special>
            <Policy_Duty v-else-if="obj.page == '责任免除'"></Policy_Duty>
            <!--<Policy_Privacy v-else-if="obj.page == '个人信息保护政策'"></Policy_Privacy>-->
            <!--<Policy_Appoint v-else-if="obj.page == '客户告知书'"></Policy_Appoint>-->
            <HHFileViewer v-else :fileName="obj.page"></HHFileViewer>
        </div>
        <div class="footer" @click="okAction">
            <div class="button">我已逐页阅读并同意</div>
        </div>
    </van-popup>
</template>

<script>
import Policy_Appoint from "./Policy/Policy_Appoint";
import Policy_Duty from "./Policy/Policy_Duty";
import Policy_Health from "./Policy/Policy_Health";
import Policy_Notice from "./Policy/Policy_Notice";
import Policy_Privacy from "./Policy/Policy_Privacy";
import Policy_List from "./Policy/Policy_List";
import Policy_Special from "./Policy/Policy_Special";
import {documentList} from "../src";
import HHFileViewer from "./HHFileViewer";
const imageDir = {
    '隐私协议': {path: 'IYBZA/IYBZAZX01/YSXY', count: 9, name: 'image_', suffix: 'jpg', prefix: ''},
    '隐私协议1': {path: 'TaiKang/TK06/dir11', count: 9, name: 'img_', suffix: 'png', prefix: '0'},
}
export default {
    name: "HHTabPopup",
    props: {obj: Object,},
    components: {
        Policy_List,
        Policy_Privacy,
        Policy_Notice,
        Policy_Health,
        Policy_Duty,
        Policy_Appoint,
				Policy_Special,
        HHFileViewer
    },
    computed: {
        policyList() {
            return documentList[this.obj.idx];
        },
        pageList() {
            return this.policyList.map(item => item.page);
        },
        imageList() {
            const cdnLink = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/picture';
            const page = this.obj.page;
            const imageList = [];
            const splits = page.split('#');
            for (const v of splits) {
                const obj = imageDir[v];
                if (!obj) continue;

                const {path, count, name, prefix, suffix} = obj;
                for (let i = 0; i < count; i++) {
                    const idx = i > 9 ? i : `${prefix}${i}`;
                    let temp = `${cdnLink}/${path}/${name}${idx}.${suffix}?version=${this.version}`;
                    imageList.push(temp);
                }
            }

            return imageList;
        },
        list() {
            const obj = this.policyList.find(item => item.page == this.obj.page) || {};
            if (Array.isArray(obj.list)) {
                return obj.list.filter(v => v.type.includes(this.obj.type)).map(v => v.page);
            }
            return [];
        },
    },
    watch: {
        obj: {
            handler() {
                this.scrollToTop();
            },
            deep: true,
        },
    },
    methods: {
        okAction() {
            this.obj.visible = false;
            this.$emit('okAction', true);
        },

        clickPolicy(policy) {
            this.$emit('clickPolicy', policy);
        },

        scrollToTop() {
            this.$nextTick(() => {
                const node = document.getElementById('id_2302221730');
                node && (node.scrollTop = 0);
            });
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2302221730 {
    height: 80%;
    width: 3.75rem;
    left: calc((100% - 3.75rem) * 0.5);

    display: flex;
    flex-direction: column;

    .content {
        flex: 1;
        overflow: auto;

        .item {
            padding: 0.2rem 0.1rem;
            border-bottom: #EEEEEE 1px solid;
            line-height: 0.2rem;

            a {
                color: #000;
            }
        }
    }

    .footer {
        background-color: #FFFFFF;
        border-top: #F2F2F2 1px solid;
				padding-bottom: .26rem;

        .button {
            margin: 0.08rem 0.2rem;
            height: 0.44rem;
            line-height: 0.44rem;
            border-radius: 0.22rem;
            font-size: 0.16rem;
            font-weight: 700;
            text-align: center;
            color: #FFFFFF;
            background: #ff8039;
        }
    }
}

</style>
