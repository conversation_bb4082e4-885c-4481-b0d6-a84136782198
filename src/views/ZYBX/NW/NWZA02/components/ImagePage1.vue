<template>
    <div class="imagePage">
        <img v-for="(src,idx) in imageList" v-lazy="src" :key="idx">
    </div>
</template>

<script>

export default {
    name: "ImagePage",
    props: {obj: Object},
    computed: {
        imageList() {
            const {dir, count} = this.obj;
            if (!dir || !count) return [];

            const imageList = [];
            for (let i = 0; i < count; i++) {
                // const path = require(`@/assets/imgs/${dir}/img_${i}.jpg`);
                let path = `${this.cdnLink}/${dir}/img_${i}.jpg?version=${this.version}`;

                imageList.push(path);
            }
            return imageList;
        },
    },
}
</script>

<style scoped type="text/less" lang="less">
.imagePage {
    img {
        display: block;
        padding: 0.2rem 3%;
        width: 94%;
    }
}
</style>
