<template>
    <van-popup v-model="visible" position="center" :closeOnClickModal="false" class="gradePopup">
        <div @touchmove.prevent>
            <div class="content">
                <div class="timeButton" @click="click">
                    一键升级投保<span class="count">{{ countDown }}s</span>
                </div>
                <div class="policy">
                    <span class="normal">我已阅读并同意
                        <span class="read" @click.stop="lookPolicy('免责条款说明')">《免责条款说明》</span>
                        <span class="read" @click.stop="lookPolicy('投保须知')">《投保须知》</span>
                        <span class="read" @click.stop="lookPolicy('保险条款')">《保险条款》</span>
                        <span class="read" @click.stop="lookPolicy('客户告知书')">《客户告知书》</span>
                        <span class="read" @click.stop="lookPolicy('个人信息保护政策')">《个人信息保护政策》</span>
                        <span class="read" @click.stop="lookPolicy('重要提示')">《重要提示》</span>
                        <span class="read" @click.stop="lookPolicy('费率表')">《费率表》</span>
                    </span>
                </div>
            </div>
            <div class="closeButton" @click="close"></div>
        </div>
    </van-popup>
</template>

<script>

export default {
    name: "UpgradePopup",
    props: {},
    data() {
        return {
            visible: false,
            intervalTimer: null,
            countDown: 5,
        }
    },
    mounted() {

    },
    beforeDestroy() {
        this.intervalTimer && clearInterval(this.intervalTimer);
    },
    methods: {
        onShow() {
            this.visible = true;
            this.intervalTimer && clearInterval(this.intervalTimer);
            this.countDown = 5;
            this.intervalTimer = setInterval(() => {
                this.countDown--;
                if (this.countDown <= 0) {
                    this.intervalTimer && clearInterval(this.intervalTimer);
                    this.close();
                }
            }, 1000);
        },
        lookPolicy(policy) {
            this.$emit('lookPolicy', policy);
        },
        click() {
            this.intervalTimer && clearInterval(this.intervalTimer);
            this.visible = false;
            this.$emit('click');
        },
        close() {
            this.intervalTimer && clearInterval(this.intervalTimer);
            this.visible = false;
            this.$emit('close');
        }
    },
}
</script>

<style scoped type="text/less" lang="less">

.gradePopup {
    width: 3.75rem;
    background-color: unset;
    z-index: 12;
    .content {
        position: relative;
        overflow: hidden;
        width: 3.75rem;
        height: 4.95rem;
        background: url("../../../../../assets/imgs/ZhongAn/ZA15/Upgrade_back.png") no-repeat;
        background-size: 100% auto;

        .timeButton {
            position: absolute;
            bottom: 1rem;
            left: 15%;
            width: 70%;
            height: 0.45rem;
            line-height: 0.45rem;
            border-radius: 0.25rem;
            background: linear-gradient(180deg, #FFC502 17%, #FF7501 90%);
            box-shadow: 0 0.03rem 0.1rem 0 rgba(255, 117, 1, 0.48), inset 0 -0.035rem 0 0 #FF5201;
            font-size: 0.2rem;
            color: #FFFFFF;
            font-weight: 700;
            text-align: center;

            .count {
                margin-left: 0.05rem;
                font-size: 0.18rem;
            }
        }

        .policy {
            position: absolute;
            bottom: 0.45rem;
            left: 12%;
            width: 80%;

            .normal {
                font-size: 0.12rem;
                color: #FFFFFF;
                line-height: 0.16rem;
                text-align: justify;
            }

            .read {
                color: #FFC502;
            }
        }

        .timer {
            margin-top: 1.35rem;
            font-size: 0.22rem;
        }
    }

    .closeButton {
        margin: 0 auto;
        width: 0.32rem;
        height: 0.32rem;
        background: url("../../../../../assets/imgs/common/sprites.png") no-repeat -1.92rem 0;
        background-size: 3.2rem 6.4rem;
    }
}

</style>
