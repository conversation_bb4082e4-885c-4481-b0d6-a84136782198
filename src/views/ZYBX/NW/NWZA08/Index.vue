<template>
    <div class="container_2308240900">

    </div>
</template>

<script>
import { isCardNo, isPersonName, isPhoneNum, url_safe_b64_decode } from "@/assets/js/common";
import { fetchRoundRobinWithAgeCommonFail, NWPreUnderWriting } from "@/api/insurance-api";
import { domainPathMap } from "@/views/ZYBX/src";

export default {
    name: "Index",
    data() {
        return {
            obj: {
                channel: '1',
                relation: 1,
                phoneNo: '',
                mTel: '',
            },
        }
    },

    mounted() {
        const query = this.$route.query || {};
        Object.assign(this.obj, query);

        try {
            if (query.bizParams) {
                const params = JSON.parse(url_safe_b64_decode(query.bizParams));
                Object.assign(this.obj, params);
            }
        } catch (error) {

        }

        this.preUnderwrite();
    },

    methods: {
        preUnderwrite() {
            const { relation, phoneNo, mTel } = this.obj;
            const certNo = this.obj[`idCard${relation}`];
            const certName = this.obj[`name${relation}`];

            let isValid = true;
            if (!isCardNo(certNo)) {
                isValid = false;
            } else if (!isPersonName(certName)) {
                isValid = false;
            } else if (!isPhoneNum(phoneNo) && !mTel.includes('_')) {
                isValid = false;
            }

            if (!isValid) {
                return this.jumpToNextLink();
            }

            const params = {
                "insuredName": certName,
                "insuredIdCard": certNo,
                "insuredPhone": isPhoneNum(phoneNo) ? phoneNo : mTel,
                "preUwProductCode": "BW85_2",
                "infoKey": 'nw_zak_disease_high_cube_base',
            }

            this.$toast.loading({
                message: '正在为您匹配产品',
                forbidClick: true,
                duration: 0,
            });

            NWPreUnderWriting(params).then(r => {
                const { code, data } = r.data || {};
                if (code == 2000) {
                    const { preUwStatus } = data || {};
                    if (preUwStatus != 1) {
                        return this.jumpToNextLink();
                    }
                }
                this.fetchFailJump();
            }).catch(err => {
                this.jumpToNextLink();
            });
        },

        fetchFailJump() {
            const { channel, phoneNo, mTel, relation } = this.obj;

            const params = {
                channelId: channel,
                idCard: this.obj[`idCard${relation}`],
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                robinKey: 'nw_za_disease_pre_fail',
                currentPage: 'NWZA08',
            }

            let path = "";
            fetchRoundRobinWithAgeCommonFail(params).then(r => {
                path = r.data.path || "";
            }).finally(() => {
                if (!path || path == 'NotFound') {
                    return this.jumpToNextLink();
                }
                this.jumpToFailLink(path);
            });
        },

        jumpToFailLink(path) {
            this.$toast.clear(true);

            if (!domainPathMap[path]) {
                path = 'NWTK02Index1';
            }

            let href = domainPathMap[path];
            href = window.location.href.replace(/[^?]+/, href);
            window.location.href = href;
        },

        jumpToNextLink() {
            this.$toast.clear(true);

            const href = window.location.href.replace('/PreUW/Index', '/Index');
            window.location.href = href;
        },
    },
}
</script>

<style lang="less" scoped>
.container_2308240900 {
    background-color: aliceblue;
}
</style>
