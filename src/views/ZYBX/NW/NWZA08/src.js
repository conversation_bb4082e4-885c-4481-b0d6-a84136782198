export const PremiumRate = [
    { min: 0, max: 70, month: { data1: 0.6, data2: 0.6 }, year: { data1: 895.0, data2: 1838.3 } },
]

export const PremiumRate1 = [
    { min: 0, max: 4, month: { data1: 100, data2: 95 }, year: { data1: 895.0, data2: 1838.3 } },
    { min: 5, max: 10, month: { data1: 80, data2: 76 }, year: { data1: 212.3, data2: 446.7 } },
    { min: 11, max: 15, month: { data1: 80, data2: 76 }, year: { data1: 350.3, data2: 794.6 } },
    { min: 16, max: 20, month: { data1: 80, data2: 76 }, year: { data1: 442.1, data2: 1010.6 } },
    { min: 21, max: 25, month: { data1: 80, data2: 85 }, year: { data1: 442.1, data2: 1010.6 } },
    { min: 26, max: 30, month: { data1: 80, data2: 88 }, year: { data1: 350.3, data2: 794.6 } },
    { min: 31, max: 35, month: { data1: 80, data2: 112 }, year: { data1: 442.1, data2: 1010.6 } },
    { min: 36, max: 40, month: { data1: 80, data2: 120 }, year: { data1: 567.0, data2: 1452.4 } },
    { min: 41, max: 45, month: { data1: 120, data2: 137 }, year: { data1: 755.8, data2: 2432.5 } },
    { min: 46, max: 50, month: { data1: 180, data2: 163 }, year: { data1: 1175.5, data2: 3433.1 } },
    { min: 51, max: 55, month: { data1: 200, data2: 178 }, year: { data1: 1482.3, data2: 4624.6 } },
    { min: 56, max: 60, month: { data1: 300, data2: 251 }, year: { data1: 1987.9, data2: 6163.5 } },
    { min: 61, max: 65, month: { data1: 272, data2: 242 }, year: { data1: 1987.9, data2: 6163.5 } },
    { min: 66, max: 70, month: { data1: 394, data2: 362 }, year: { data1: 1987.9, data2: 6163.5 } },
]

export const documentList = [
    { page: '健康告知', belongs: '123456789' },
    { page: '投保须知', belongs: '123456789' },
    { page: '除外责任', belongs: '123456789' },
    { page: '重要提示', belongs: '123456789' },
    { page: '投保声明', belongs: '123456789' },
    { page: '客户告知书', belongs: '123456789' },
    { page: '经纪委托协议', belongs: '123456789' },
    {
        page: '保险条款', belongs: '123456789', list: [
            '特定疾病保险（互联网专属）条款',
            '附加重大疾病保险（互联网专属B款）条款',
            '附加质子重离子医疗保险（互联网专属2022版）条款',
            '附加个人恶性肿瘤特定药品医疗保险（互联网专属）条款',
            '职业类型表',
            '特药清单',
            '健康管理服务使用手册',
        ],
    },
    { page: '委托代扣授权书', belongs: '123456789' },
    { page: '个人信息保护政策', belongs: '123456789' },
];

export const pdfFileObj = {
    '客户告知书': '/ZAZY/common/khgzs.pdf',
    '经纪委托协议': '/ZAZY/common/jjwtxy.pdf',
    '委托代扣授权书': '/ZAZY/common/dksqs.pdf',
    '个人信息保护政策': '/ZAZY/common/xxbhzc.pdf',
    '健康告知': '/ZAZY/YBGR02/jkgz.pdf',
    '特别约定$v1': '/ZAZY/YBGR02/tbyd_v1.pdf',
    '特别约定$v2': '/ZAZY/YBGR02/tbyd_v2.pdf',
    '除外责任': '/ZAZY/YBGR02/zrcw.pdf',
    '重要提示': '/ZAZY/YBGR02/zyts.pdf',
    '投保声明': '/ZAZY/YBGR02/tbsm.pdf',
    '投保须知': '/ZAZY/YBGR02/tbxz.pdf',
    '理赔流程': '/ZAZY/YBGR02/lplc.pdf',
    '职业类型表': '/ZAZY/YBGR02/zylbb.pdf',
    '特药清单': '/ZAZY/YBGR02/tyqd.pdf',
    '健康管理服务使用手册': '/ZAZY/YBGR02/fwsc.pdf',
    '特定疾病保险（互联网专属）条款': '/ZAZY/YBGR02/dir1.pdf',
    '附加重大疾病保险（互联网专属B款）条款': '/ZAZY/YBGR02/dir2.pdf',
    '附加质子重离子医疗保险（互联网专属2022版）条款': '/ZAZY/YBGR02/dir3.pdf',
    '附加个人恶性肿瘤特定药品医疗保险（互联网专属）条款': '/ZAZY/YBGR02/dir4.pdf',
}

export const policyList = [
    { name: '特定疾病保险（互联网专属）条款', title: '主条款', extra: '' },
    { name: '附加重大疾病保险（互联网专属B款）条款', title: '附加条款', extra: '' },
    { name: '附加质子重离子医疗保险（互联网专属2022版）条款', title: '', extra: '' },
    { name: '附加个人恶性肿瘤特定药品医疗保险（互联网专属）条款', title: '', extra: '' },
]

// 投保关系
export const relations = [
    { key: "本人", value: 1, param: 1 },
    { key: "配偶", value: 2, param: 2 },
    { key: "儿女", value: 3, param: 3 },
    { key: "父母", value: 4, param: 4 },
]

// 有无社保
export const insurances = [
    { key: "有医保(含新农合)", value: 1 },
    { key: "无医保", value: 0 },
]

// 缴费方式
export const repays = [
    { key: "按月缴费(12期)", value: 1 },
    { key: "全额缴费", value: 0 },
]

// 保障内容
export const planFeature = [
    '保单门槛低',
    '覆盖人群广',
    '保单可验真',
]

export const planSummary = [
    { key: '重大疾病责任', value: '最高1万' },
    { key: '重疾绿通', value: '免费赠送' },
    { key: '疾病住院护工', value: '免费赠送' },
    { key: '就医陪诊服务', value: '免费赠送' },
    { key: '免赔额', value: '0元' },
    { key: '赔付比例', value: '100%给付责任保额' },
    { key: '等待期', value: '90天' },
]

export const planSummary1 = [
    { key: '恶性肿瘤院外特定药品费用医疗责任', value: '100万元' },
    { key: '恶性肿瘤特定器械耗材费用医疗责任<br>（适用于女性被保险人）', value: '100万元' },
    { key: '重大疾病责任', value: '20万元' },
    { key: '特定疾病责任', value: '30万元' },
    { key: '未成年人罕见病特定药品费用医疗责任<br>（被保人年龄小于18周岁）', value: '100万元' },
    { key: '未成年人特定器械耗材费用医疗责任<br>（被保人年龄小于18周岁）', value: '100万元' },
    { key: '互联网医院药品费用医疗责任', value: '1万元' },
    { key: '医疗垫付服务', value: '免费赠送' },
    { key: '重疾绿通', value: '免费赠送' },
    { key: '肿瘤特药', value: '免费赠送' },
    { key: '术后家庭护理', value: '免费赠送' },
    { key: '在线问诊服务', value: '免费赠送' },
    { key: '图文咨询服务', value: '免费赠送' },
    { key: '药费直赔服务', value: '免费赠送' },
    { key: '少儿罕见病特药服务<br>(被保险人年龄小于18周岁)', value: '免费赠送' },
    { key: '胰岛素注射泵直付服务<br>(被保险人年龄小于18周岁)', value: '免费赠送' },
    { key: '免赔额', value: '0元' },
    { key: '赔付比例', value: '60%-100%(依据不同的保险责任而定)' },
    { key: '等待期', value: '重大疾病责任和特定疾病责任90天等待期<br>其他责任30天等待期' },
]

export const planPoints = [
    { key: '保障期限', value: '1年' },
    { key: '被保人年龄', value: '出生满30天-70周岁' },
    { key: '医院范围', value: '二级及以上公立医院' },
    { key: '等待期', value: '90天' },
    { key: '犹豫期', value: '2天' },
]

export const planPoints1 = [
    { key: '保障期限', value: '1年' },
    { key: '被保人年龄', value: '出生满30天-70周岁' },
    { key: '医院范围', value: '二级及二级以上的公立医院(互联网医院药品费用责任限众安互联网医院)' },
    { key: '等待期', value: '重大疾病责任和特定疾病责任90天等待期<br>其他责任30天等待期' },
    { key: '犹豫期', value: '2天' },
]

// 保障详情弹窗，文档信息，基础版
export const planDetails = [
    {
        key: '重大疾病责任',
        value: '最高1万',
        text: '被保险人于90天等待期后，经中华人民共和国境内二级及以上的公立医院的专科医生初次确诊罹患本合同约定的一种或多种重大疾病，保险人按本合同载明的保险金额给付重大疾病保险金（年龄<=30周岁时：10,000、31-45（含）周岁时：5000、46-60（含）周岁时：1000、61-70（含）周岁时：500），给付后本合同终止。'
    }, {
        key: '增值服务',
        value: '免费赠送',
        text: '重疾绿通、疾病住院护工、就医陪诊'
    },
]
// 保障详情弹窗，文档信息，升级版
export const planDetails1 = [
    {
        key: '重大疾病责任',
        value: '最高20万',
        text: '被保险人于90天等待期后，经中华人民共和国境内二级及以上的公立医院的专科医生初次确诊罹患本合同约定的一种或多种重大疾病，保险人按本合同载明的保险金额给付重大疾病保险金（0-30周岁20万，31-60周岁10万，61-70周岁5万），给付后本合同终止。'
    }, {
        key: '恶性肿瘤院外特定药品费用医疗责任',
        value: '100万',
        text: '（1）在等待期30天后初次确诊罹患恶性肿瘤——重度；<br>（2）治疗实际发生的合理且必需的且同时满足合同定义的院外特定药品费用；<br>（3）保险人按100%的赔付比例给付本项保险金，年累计给付以100万元为限；<br>（4）被保险人须在保险人指定的药店进行特定药品的购买；<br>（5）被保险人在购买处方中所列特定药品前，需按保险人指定流程提交相应材料并通过处方审核，具体流程见条款中“授权申请和药品处方审核”及“药品购买”的相关规定；<br>（6）仅赔付责任内约定的药品清单中列明的药品，若药品为当地基本医疗保险目录范围内药品，被保险人已经基本医疗保险结算的，按照100%进行赔付，未经基本医疗保险结算的，按照60%进行赔付。若药品为当地基本医疗保险目录范围外的药品，按照100%进行赔付。约定的药品清单以保险人最新公布信息为准，保险人保留对药品清单进行变更的权利，将根据医疗水平的发展对药品清单进行更新。'
    }, {
        key: '恶性肿瘤特定器械耗材费用医疗责任（适用于女性被保险人）',
        value: '100万',
        text: '（1）被保险人在等待期30天后初次确诊罹患恶性肿瘤——重度；<br>（2）在二级及二级以上的公立医院普通部发生的、需个人支付的、必需且合理的、用于治疗原发于乳腺的恶性肿瘤——重度的且用于乳房一期再造的乳腺假体器械费用；<br>（3）保险人按100%的赔付比例给付本项保险金，年累计给付以100万元为限；<br>（4）被保险人须在二级及二级以上的公立医院普通部或保险人指定药店购买特定器械耗材；<br>（5）被保险人在购买特定器械耗材前，需按保险人指定流程提交相应材料并通过审核，具体流程见条款中“授权申请和材料审核”的相关规定；<br>（6）仅赔付责任内约定的器械耗材清单中列明的器械耗材；被保险人已经基本医疗保险结算的，按照100%进行赔付，未经基本医疗保险结算的，按照60%进行赔付；保险人保留对器械耗材清单进行变更的权利，将根据医疗水平的发展对器械耗材清单进行更新。<br>（被保人需>=18周岁）'
    }, {
        key: '未成年人罕见病特定药品费用医疗责任（被保人年龄小于18周岁）',
        value: '100万',
        text: '（（1）被保险人在等待期30天后初次确诊罹患本合同约定的13种罕见病；<br>（2）治疗实际发生的合理且必需的且同时满足合同定义的特定药品费用；<br>（3）保险人按100%的赔付比例给付本项保险金，年累计给付以100万元为限；<br>（4）被保险人须在二级及二级以上的公立医院普通部或保险人指定的药店进行特定药品的购买；<br>（5）被保险人在购买处方中所列特定药品前，需按保险人指定流程提交相应材料并通过处方审核，具体流程见条款中“授权申请和药品处方审核”及“药品购买”的相关规定；<br>（6）仅赔付责任内约定的药品清单中列明的药品，若药品为当地基本医疗保险目录范围内药品，被保险人已经基本医疗保险结算的，按照100%进行赔付，未经基本医疗保险结算的，按照60%进行赔付。若药品为当地基本医疗保险目录范围外的药品，按照100%进行赔付；约定的药品清单以保险人最新公布信息为准，保险人保留对药品清单进行变更的权利，将根据医疗水平的发展对药品清单进行更新。'
    }, {
        key: '未成年人特定器械耗材费用医疗责任（被保人年龄小于18周岁）',
        value: '100万',
        text: '（1）被保险人在等待期30天后初次确诊罹患特定疾病；<br>（2）在二级及二级以上的公立医院普通部发生的、需个人支付的、必需且合理的、用于治疗I型糖尿病的胰岛素泵器械费用；<br>（3）保险人按100%的赔付比例给付本项保险金，年累计给付以100万元为限；<br>（4）被保险人须在二级及二级以上的公立医院普通部或保险人指定药店购买未成年人特定器械耗材；<br>（5）被保险人在购买特定器械耗材前，需按保险人指定流程提交相应材料并通过审核，具体流程见条款中“授权申请和材料审核”的相关规定；<br>（6）仅赔付责任内约定的器械耗材清单中列明的器械耗材；被保险人已经基本医疗保险结算的，按照100%进行赔付，未经基本医疗保险结算的，按照60%进行赔付；保险人保留对器械耗材清单进行变更的权利，将根据医疗水平的发展对器械耗材清单进行更新。'
    }, {
        key: '互联网医院药品费用医疗责任',
        value: '1万',
        text: '（1）被保险人在等待期30天后因遭受意外伤害事故或罹患疾病；<br>（2）在众安互联网医院发生的购药费用，每次购药费用按60%赔付比例进行赔付，单次赔付上限为500元，全年累计给付以10,000元为限。<br>（3）被保险人每个月限开药2次。<br>（4）6周岁（含）以下被保险人无法开具处方药品。<br>（5）保险人仅承担《疾病清单》中限定疾病所产生的药品费用。<br>（6）单次互联网医院药品费用指被保险人同一天在约定的互联网医院经同一个医生诊疗并开具处方，被保险人根据该处方在约定互联网药店购药所产生的药品费用。'
    }, {
        key: '特定疾病责任',
        value: '最高30万',
        text: '被保险人于90天等待期后，经中华人民共和国境内二级及以上的公立医院的专科医生初次确诊罹患本合同约定的一种或多种特定疾病，保险人按本合同载明的保险金额给付特定疾病保险金（0-30周岁30万，31-60周岁10万，61-70周岁5万），给付后本合同终止。'
    }, {
        key: '增值服务',
        value: '免费赠送',
        text: '医疗垫付服务、重疾绿色通道服务、肿瘤特药、术后家庭护理、在线问诊服务、图文咨询服务、药费直赔服务、少儿罕见病特药服务（被保险人年龄小于18周岁）及胰岛素注射泵直付服务（被保险人年龄小于18周岁）。'
    },
];

// 理赔说明
export const claimProcess = [
    {
        key: "第1步：报案",
        value: '在安卓市场或APP Store，搜索下载众安保险APP发起报案申请。'
    }, {
        key: "第2步：提交材料",
        value: "通过接收的理赔在线链接，根据页面指引提交理赔申请材料。"
    }, {
        key: "第3步：审核材料",
        value: "保险公司将及时进行审核、调查、反馈结果，并根据情况通知寄送纸质材料。"
    }, {
        key: "第4步：获取理赔金",
        value: "对属于保险责任的，保险公司会将理赔金直接转账至被保险人/受益人名下的指定账户。"
    },
]

export const askAnswerList = [
    {
        q: '可以赔付哪些费用？',
        a: '重大疾病责任中被保险人于等待期后，经中华人民共和国境内二级及以上的公立医院的专科医生初次确诊罹患本合同约定的一种或多种重大疾病，保险人按本合同载明的保险金额给付重大疾病保险金，给付后本合同终止。'
    }, {
        q: '本产品的等待期有多久？',
        a: '等待期为自合同生效日起计算的一段时间，等待期为30天。小安在1月15日因“肺炎”住院治疗，1月25日痊愈出院。因为小安1月15日的住院发生在医疗保险的等待期内，所以保险公司不承担赔付保险金的责任。本产品重大疾病责任的等待期为90天；在上一张保单期满后指定期限内重新投保无等待期。如承保保单为分期交付保费，若下一年度保单生效时未交清上一年度保单的保险费，则下一年度保单的等待期重新计算。'
    }, {
        q: '批改变更是否有年龄限制与时间限制？',
        a: '1)批改变更有年龄与时间限制。<br>2)被保险人首次投保众安康基础月缴A版时年龄为出生满30天至60周岁，符合条件仅可申请升级至众安康升级月缴版C，可申请时间段为首次投保日至下一期应缴日（不含）前，升级成功后的当次应缴日零时起升级为众安康升级月缴版C。<b>逾期不再受理申请，如未申请变更将按众安康基础月缴版继续承保。已申请变更后，如变更不成功（逾期扣费失败等原因），将导致保单断缴失效。</b><br>3)被保险人首次投保众安康基础月缴A版时年龄61周岁至70周岁，符合条件仅可申请升级至众安康升级月缴版D，可申请时间段为首次投保日至下一期应缴日（不含）前，升级成功后的当次应缴日零时起升级为众安康升级月缴版D。<b>逾期不再受理申请，如未申请升级将按众安康基础月缴版继续承保。已申请升级后，如升级不成功（逾期扣费失败等原因），将导致保单断缴失效。</b>'
    }, {
        q: '批改变更后什么时候开始生效和缴费？',
        a: '在众安康基础月缴版保单申请批改变更后，从下月缴费日起按照变更后产品的保费进行缴费，缴费成功后可享受变更后产品的责任保障。'
    }, {
        q: '保险期间是多久？理赔或停售后还可以重新投保吗？',
        a: '本产品保险期间为一年。保险期间届满，投保人需要重新向保险公司申请投保本产品，交纳保险费，并获得新的保险合同。在上一张保单期满后指定期限内重新投保不计算等待期。<b>我们审核通过方可为投保人办理重新投保手续。</b>若保险期间届满，本保险产品已停止销售，保险公司不再接受投保申请，但会向您提供投保其他保险产品的合理建议。'
    },
]
