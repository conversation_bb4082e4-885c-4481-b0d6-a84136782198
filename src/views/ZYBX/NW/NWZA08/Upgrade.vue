<template>
    <div class="container_2302211530">
        <div class="header">
            <img src="@/assets/imgs/NW/NWZA07/img03.png">
            <div class="header-points">
                <img :src="bannerUrl">
            </div>
        </div>
        <div class="premium-x">
            <p>优化保障后每月保费由<span>{{ premiumObj.first }}</span>元变更为<span>{{ premiumObj.next }}</span>元，自次月开始扣费并生效</p>
            <p>变更后产品计划的保障责任、赔付比例、免赔额详见<span class="view-txt" @click.stop="viewAction('投保须知', true)">《投保须知》</span>
            </p>
        </div>
        <div id="id_action_button" class="submit-button" @click="onSubmit">
            变更保障计划
            <img alt="" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <div class="policy-x">
            <van-icon class="policy-icon" :name="orderInfo.checked ? 'checked' : 'circle'"
                @click="orderInfo.checked = !orderInfo.checked" />
            我已阅读并同意
            <span class="view-txt" @click.stop="viewAction('健康告知', true)">《健康告知》</span>
            <span class="view-txt" @click.stop="viewAction('投保须知', true)">《投保须知》</span>
            <span class="view-txt" @click.stop="viewAction('除外责任', true)">《除外责任》</span>
            <span class="view-txt" @click.stop="viewAction('重要提示', true)">《重要提示》</span>
            <span class="view-txt" @click.stop="viewAction('投保声明', true)">《投保声明》</span>
            <span class="view-txt" @click.stop="viewAction('客户告知书', true)">《客户告知书》</span>
            <span class="view-txt" @click.stop="viewAction('经纪委托协议', true)">《经纪委托协议》</span>
            <span class="view-txt" @click.stop="viewAction('保险条款', true)">《保险条款》</span>
            <span class="view-txt" @click.stop="viewAction('委托代扣授权书', true)">《委托代扣授权书》</span>
            <span class="view-txt" @click.stop="viewAction('个人信息保护政策', true)">《个人信息保护政策》</span>
            <span class="view-txt" @click.stop="viewAction('费率表', false)">《费率表》</span>。
        </div>
        <div class="upgrade-tittle">
            优化后享更多权益
            <div class="upgrade-box">
                <div class="upgrade-row">
                    <div class="upgrade-label">产品名称：</div>
                    <div class="upgrade-value">众安康升级月缴版</div>
                </div>
                <div class="upgrade-row">
                    <div class="upgrade-label">备案号：</div>
                    <div class="upgrade-value">个人重大疾病保险条款(互联网2022版A款)</div>
                </div>
                <div class="upgrade-row">
                    <div class="upgrade-label">注册号：</div>
                    <div class="upgrade-value">C00017932612021111900413</div>
                </div>
            </div>
        </div>
        <HHPlan :obj="policyObj" @click="viewAction('保障详情')" :orderInfo="orderInfo"></HHPlan>
        <van-tabs v-model="currentTab" scrollspy sticky>
            <van-tab key="产品特色" name="产品特色" title="产品特色">
                <div class="section">
                    <div class="section-title">产品特色</div>
                    <img alt="产品特色" :src="contentUrl">
                </div>
            </van-tab>
            <van-tab key="理赔说明" name="理赔说明" title="理赔说明">
                <div class="section">
                    <div class="section-title">理赔说明</div>
                    <HHClaimProcess></HHClaimProcess>
                </div>
            </van-tab>
            <van-tab key="常见问题" name="常见问题" title="常见问题">
                <div class="section">
                    <div class="section-title">常见问题</div>
                    <HHAskAnswer :obj="policyObj"></HHAskAnswer>
                </div>
            </van-tab>
        </van-tabs>
        <div class="copyright">
            <img src="@/assets/imgs/NW/NWZA08/footer.png" alt="底图">
            <p>该保险产品由众安在线财产保险股份有限公司承保并负责理赔，产品页面仅供参考，具体责任描述以保险合同为准。</p>
            <p>众安保险最近季度偿付能力符合监管要求，详情请参见众安保险官网(www.zhongan.com) 偿付能力信息披露。</p>
            <p>本产品由爱邦保险经纪有限公司销售，爱邦保险经纪有限公司版权所有</p>
            <p><a href="https://beian.miit.gov.cn">粤ICP备2021097323号-1</a></p>
            <p>上海暖哇科技有限公司提供技术支持</p>
        </div>
        <div v-if="bottomButtonVisible" class="button-x" @click="onSubmit">
            变更保障计划<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
        <HHTabViewer :obj="policyObj" @ok="onAcceptPolicy"></HHTabViewer>
        <!--录屏-->
        <NwRecord @SetIseeBiz="SetIseeBiz"></NwRecord>
    </div>
</template>

<script>
import { bxStorage, } from "@/utils/store_util";
import HHTabViewer from "./components/HHTabViewer";
import NwRecord from "@/views/components/NwRecord";
import HHPlan from "./components/HHPlan";
import HHPolicyViewer from "./components/HHPolicyViewer";
import HHClaimProcess from "./components/HHClaimProcess";
import HHAskAnswer from "./components/HHAskAnswer";
import { orderInfo, eventTracking, loadOrderInfo, getPolicyVersion } from "./function";
import { TraceLogInfoKeys, url_safe_b64_decode } from "@/assets/js/common";

export default {
    name: "Upgrade", // 升级页
    components: { HHPolicyViewer, HHTabViewer, NwRecord, HHClaimProcess, HHAskAnswer, HHPlan },
    data() {
        return {
            orderInfo,
            currentTab: '',
            bottomButtonVisible: false,
            policyObj: { v: false, page: '', v1: false, page1: '', belongs: 5 },
        }
    },
    computed: {
        premiumObj() {
            const { nextPremium, firstPremium } = this.orderInfo;
            return { first: firstPremium, next: nextPremium };
        },
        bannerUrl() {
            if (this.policyObj.belongs == 5) {
                return require("@/assets/imgs/NW/NWZA08/img04_1.png");
            }
            if (this.policyObj.belongs == 6) {
                return require("@/assets/imgs/NW/NWZA08/img04_2.png");
            }
            if (this.policyObj.belongs == 7) {
                return require("@/assets/imgs/NW/NWZA08/img04_3.png");
            }
            return require("@/assets/imgs/NW/NWZA08/img04_4.png");
        },
        contentUrl() {
            if (this.policyObj.belongs == 5) {
                return require("@/assets/imgs/NW/NWZA08/img05_1.png");
            }
            if (this.policyObj.belongs == 6) {
                return require("@/assets/imgs/NW/NWZA08/img05_1.png");
            }
            if (this.policyObj.belongs == 7) {
                return require("@/assets/imgs/NW/NWZA08/img05_2.png");
            }
            return require("@/assets/imgs/NW/NWZA08/img05_3.png");
        },
    },
    created() {
        this.init();
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.$nextTick(() => { // 监听滚动事件
            this.actionButtonObserver();
        });

        this._entryReport();

        if (this.orderInfo.school == 1) {
            this.pushToResult();
        }
    },

    methods: {
        SetIseeBiz(value) {
            this.orderInfo.traceBackUuid = value;
        },

        actionButtonObserver() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries, observer) => {
                    this.bottomButtonVisible = !entries[0].isIntersecting;
                }, { threshold: 0.01 });

                const buttonNode = document.getElementById('id_action_button');
                buttonNode && observer.observe(buttonNode);
            }
        },

        // 初始化
        init() {
            const query = this.$route.query || {};
            query.source = query.source || 'direct'; // source要以链接携带的参数为准
            query.action = query.action || 'direct'; // action要以链接携带的参数为准
            query.school = query.school || '0'; // school要以链接携带的参数为准

            const inStore = loadOrderInfo();
            Object.assign(this.orderInfo, inStore, query);

            this.orderInfo.identifier = 'NWZA08Upgrade';
            this.orderInfo.planKey = TraceLogInfoKeys.nw_zak_disease_high_cube_base;
            this.orderInfo.checked = this.orderInfo.channel >= 1000;
            this.policyObj.belongs = getPolicyVersion(this.orderInfo, false);

            this.decodeUserInfo();

            bxStorage.setRawItem('NWZA08_Upgrade', 1);
        },

        decodeUserInfo() {
            const param = url_safe_b64_decode(this.orderInfo.bizParams);
            if (!param) return;

            const obj = JSON.parse(param);
            Object.assign(this.orderInfo, obj);
        },

        onSubmit() {
            if (this.orderInfo.checked) {
                return this.pushToResult();
            }

            this.viewAction('投保须知', true);
        },

        pushToResult() {
            this._actionTracking('点击立即升级按钮');

            setTimeout(() => {
                const href = window.location.href.replace('/Upgrade', '/Result');
                window.location.href = href;
            }, 250);
        },

        onAcceptPolicy() {
            this.orderInfo.checked = true;
            this.pushToResult();
        },

        viewAction(name, isPolicy,) {
            this.policyObj[isPolicy ? 'v1' : 'v'] = true;
            this.policyObj[isPolicy ? 'page1' : 'page'] = name;
        },

        _entryReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            this._actionTracking('升级页', domContentLoadedEventEnd - fetchStart);
        },

        _actionTracking(name, time = 0) {
            eventTracking(name, time);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2302211530 {
    width: 3.75rem;
    min-height: 100%;
    font-size: 0.15rem;
    background-color: #ffffff;

    img {
        display: block;
        max-width: 100%;
    }

    .header {
        .header-points {
            position: relative;
            margin-top: -0.35rem;
            border-radius: 0.1rem;
            background-color: #ffffff;
        }
    }

    .view-txt {
        color: #F34015;
        font-weight: 500;
    }

    .premium-x {
        font-size: 0.12rem;
        color: #333333;
        text-align: center;
        line-height: 0.2rem;
    }

    .policy-x {
        padding: 0.1rem;
        background-color: #ffffff;
        font-size: 0.13rem;
        color: #333333;
        line-height: 1.6;
        text-align: justify;

        .policy-icon {
            color: #F34015;
            font-size: 0.16rem;
            vertical-align: -0.01rem;
        }
    }

    .button-x,
    .submit-button {
        position: relative;
        margin: 0.1rem 0.35rem 0.1rem;
        padding: 0.15rem 0;
        font-size: 0.2rem;
        color: #ffffff;
        font-weight: 700;
        text-align: center;
        border-radius: 999px;
        box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
        background: linear-gradient(270deg,
                rgb(255, 16, 46),
                rgb(253, 123, 69));
        animation: button_animate 1.35s linear infinite;

        .hand {
            position: absolute;
            top: 0.25rem;
            left: 75%;
            width: 18%;
            animation: hand_animate 1s linear infinite;
        }
    }

    .button-x {
        position: fixed;
        inset: auto 0 0.25rem 0;
        margin: 0 auto;
        width: 3rem;
    }

    .upgrade-tittle {
        margin-top: 0.18rem;
        margin-bottom: 0.1rem;
        font-size: 0.2rem;
        text-align: center;
        font-weight: 700;
        color: #464646;

        .upgrade-box {
            font-size: .14rem;
            font-weight: 400;
            color: #333333;
            background-color: #f2f2f2;
            margin: 0.12rem 0.08rem;
            padding: 0.12rem 0.08rem;
            border-radius: 0.12rem;

            .upgrade-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: .12rem;
            }

            .upgrade-value {
                color: #999;
                text-align: right;
                width: 2.65rem;
            }
        }
    }

    .section {
        background-color: #ffffff;
        position: relative;

        .section-title {
            display: flex;
            justify-content: center;
            align-items: center;

            color: #333333;
            font-size: 0.18rem;
            font-weight: 500;
            line-height: 0.45rem;

            &::before,
            &::after {
                content: " ";
                width: 0.55rem;
                height: 0.13rem;
                background: no-repeat center/100%;
            }

            &::before {
                margin-right: 0.1rem;
                background-image: url("~@/assets/imgs/common/icon_needle_left.png");
            }

            &::after {
                margin-left: 0.1rem;
                background-image: url("~@/assets/imgs/common/icon_needle_right.png");
            }
        }

        .active-txt {
            opacity: 0;
            position: absolute;
            left: 1.5rem;
            top: 10.5rem;
            height: 0.3rem;
            width: 1.2rem;
        }
    }

    .copyright {
        padding: 0.2rem 0.15rem 0.85rem;
        color: #666666;
        font-size: 0.12rem;
        line-height: 1.5;
        text-align: center;

        a {
            color: #737680;
            text-decoration: underline;
        }
    }

    @keyframes button_animate {
        0% {
            transform: scale(1);
        }

        40% {
            transform: scale(1);
        }

        70% {
            transform: scale(0.95);
        }

        100% {
            transform: scale(1);
        }
    }

    @keyframes hand_animate {
        0% {
            transform: translate(-0.1rem, -0.1rem);
        }

        45% {
            transform: translate(0.1rem, 0);
        }

        70% {
            transform: translate(0.1rem, 0);
        }

        100% {
            transform: translate(-0.1rem, -0.1rem);
        }
    }
}
</style>
