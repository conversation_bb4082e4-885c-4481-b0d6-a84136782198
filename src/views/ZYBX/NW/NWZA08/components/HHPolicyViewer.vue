<template>
    <van-popup v-model="obj.v" class="container_2302140945" position="bottom" round>
        <div class="header">
            {{ obj.page }}
            <van-icon class="icon" name="cross" @click="closeAction" />
        </div>
        <div id="id_2302141022" class="content">
            <HHRateSchedule v-if="obj.page.indexOf('费率表') >= 0" :obj="obj"></HHRateSchedule>
            <HHPlanDetail v-else-if="obj.page.indexOf('保障详情') >= 0" :obj="obj"></HHPlanDetail>
            <HHFileViewer v-else :fileName="obj.page + '$v' + obj.belongs"></HHFileViewer>
        </div>
        <div class="footer">
            <div class="button" @click="okAction">
                知道了
            </div>
        </div>
    </van-popup>
</template>

<script>
import HHRateSchedule from "./HHRateSchedule";
import HHFileViewer from "./HHFileViewer";
import HHPlanDetail from "./HHPlanDetail";

export default {
    name: "HHPolicyViewer",
    components: {
        HHFileViewer,
        HHRateSchedule,
        HHPlanDetail,
    },
    props: { obj: Object, },
    watch: {
        'obj.v': {
            handler() {
                this.scrollToTop();
            },
        },
    },
    methods: {
        okAction() {
            this.obj.v = false;
            this.$emit('ok', this.obj.page);
        },
        closeAction() {
            this.okAction();
        },
        scrollToTop() {
            this.$nextTick(() => {
                const node = document.getElementById('id_2302141022');
                node && (node.scrollTop = 0);
            });
        }
    },
}
</script>


<style lang="less" scoped type="text/less">
.container_2302140945 {
    height: 80%;
    width: 3.75rem;
    inset: auto 0 0 0;
    margin: 0 auto;

    display: flex;
    flex-direction: column;

    .header {
        position: relative;
        padding: 0.15rem 0.1rem 0.15rem 0.1rem;
        color: #333333;
        font-size: 0.17rem;
        font-weight: 500;
        text-align: center;

        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .icon {
            top: 0;
            right: 0;
            position: absolute;
            padding: 0.12rem 0.15rem;
            font-size: 0.24rem;
        }
    }

    .content {
        overflow: auto;
        flex: 1;
    }

    .footer {
        background-color: #ffffff;
        border-top: #f2f2f2 1px solid;

        .button {
            margin: 0.05rem 0.25rem 0.1rem;
            padding: 0.16rem 0;
            border-radius: 0.25rem;
            font-size: 0.16rem;
            font-weight: 500;
            text-align: center;
            color: #ffffff;
            background: linear-gradient(to right,
                    rgba(255, 20, 30, 0.65),
                    rgba(255, 0, 0, 0.8));
        }
    }
}
</style>
