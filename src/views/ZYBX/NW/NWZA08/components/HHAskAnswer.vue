<template>
    <div class="container_2209010900">
        <van-collapse v-model="collapseName" accordion>
            <van-collapse-item v-for="item in questionList" :key="item.q" :name="item.q">
                <template #title>
                    <div class="collapse-item-header">
                        <img class="collapse-item-header-icon" src="@/assets/imgs/common/icon_question.png"/>
                        <span>{{ item.q }}</span>
                    </div>
                </template>
                <div v-html="item.a"></div>
            </van-collapse-item>
        </van-collapse>
        <div v-if="isLong" :style="{'--deg':showAll ? '180deg' : '0deg'}" class="showAll" @click="showAll= !showAll">
            <span>{{ showText }}</span>
        </div>
    </div>
</template>

<script>
import {askAnswerList,} from "../src";

export default {
    name: "HHAskAnswer",
    data() {
        const isLong = askAnswerList.length > 4;
        return {
            isLong,
            showAll: false,
            collapseName: '',
        }
    },
    computed: {
        questionList() {
            if (this.showAll) {
                return askAnswerList;
            }
            return askAnswerList.slice(0, 4);
        },

        showText() {
            return this.showAll ? '收起' : '查看更多';
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2209010900 {
    color: #333333;
    font-size: 0.14rem;
    text-align: justify;
    background-color: #FFFFFF;

    .collapse-item-header {
        display: flex;
        align-items: center;

        .collapse-item-header-icon {
            margin-right: 0.05rem;
            width: 0.20rem;
        }
    }

    .showAll {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FF4509;
        line-height: 0.35rem;
        font-size: 0.13rem;

        &::after {
            content: " ";
            margin-left: 0.05rem;
            height: 0.08rem;
            width: 0.08rem;
            background: url("~@/assets/imgs/common/sprites.png") no-repeat 0 -0.32rem;
            background-size: 1.6rem 3.2rem;
            transition: 0.25s;
            transform: rotate(var(--deg));
        }
    }
}

</style>
