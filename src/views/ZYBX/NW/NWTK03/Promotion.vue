<template>
    <div class="container_2209051000">

    </div>
</template>

<script>

import { bxStorage, } from "@/utils/store_util";

export default {
    name: "NWTK03Promotion",
    created() {
        this.init();
    },
    methods: {
        init() {
            const PATH_DEFAULT = 'NWTK03Promotion2'; // 默认产品路径

            let path = bxStorage.getRawItem('NWTK03RandomPath') || '';

            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/config/random_params.js';
            document.getElementsByTagName('head')[0].appendChild(script);
            script.onload = () => {
                try {
                    const list = NWTK03_Promotion_random || [[PATH_DEFAULT, 100]];
                    const productList = list.map(item => item[0]);
                    if (productList.includes(path)) {
                        return this.goPath(path);
                    }

                    const sum = list.reduce((a, b) => {
                        return a + b[1];
                    }, 0);

                    list.map((item, index) => {
                        if (index == 0) {
                            list[index][1] = item[1] / sum * 100;
                        } else {
                            list[index][1] = item[1] / sum * 100 + list[index - 1][1];
                        }
                    });

                    const randomVal = Math.random() * 100;
                    // console.log(randomVal, list);

                    for (let k = 0; k < list.length; k++) {
                        if (randomVal < list[k][1]) {
                            path = list[k][0];
                            return this.goPath(path);
                        }
                    }
                    this.goPath(PATH_DEFAULT);
                } catch (err) {
                    this.goPath(PATH_DEFAULT);
                }
            }
            script.onerror = () => {
                this.goPath(PATH_DEFAULT);
            }
        },
        goPath(path) {
            const inQry = this.$route.query || {};
            bxStorage.setRawItem('NWTK03RandomPath', path);
            this.$router.replace({
                name: path,
                query: inQry,
            });
        },
    }
}
</script>

<style lang="less" scoped type="text/less">
.container_2209051000 {
    width: 100%;
    height: 100%;
    background-color: #ffffff;
}
</style>
