<template>
	<div class="container_2302221700">
		<PDFViewer :file="fileUrl"></PDFViewer>
	</div>
</template>

<script>

import PDFViewer from "@/views/ZYBX/components/PDFViewer";
import { pdfFileObj } from "../src";

export default {
	name: "HHFileViewer",
	components: { PDFViewer },
	props: { fileName: String },
	computed: {
		fileUrl() {
			const key = this.fileName;
			return pdfFileObj[key] || pdfFileObj[key.split('$')[0]] || '';
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302221700 {
		height: 100%;
	}
</style>
