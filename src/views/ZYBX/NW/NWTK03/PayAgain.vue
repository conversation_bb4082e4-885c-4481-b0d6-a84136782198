<template>
	<div class="container_2304041313">
		<div class="looking">
			<div class="looking-wave"></div>
			<div class="looking-label">正在查询中，请稍等...</div>
		</div>
	</div>
</template>

<script>
import { Dialog, Toast } from 'vant';
import { submitPromoteOrderInfo } from "./function";
import { fetchNWAliStatus } from "@/api/insurance-api";

export default {
	name: "PayAgain",
	data() {
		return {
			obj: { infoNo: '', },
		}
	},
	created() {
		this.init();
	},
	methods: {
		//初始化
		init() {
			const query = this.$route.query || {};
			Object.assign(this.obj, query);

			if (!this.obj.infoNo) {
				return this.pushToHome();
			}

			this.fetchPayStatus();
		},

		async fetchPayStatus() {
			for (let idx = 0; idx < 3; idx++) {
				try {
					const start = Date.now();
					const res = await fetchNWAliStatus(this.obj.infoNo);
					const { code, data } = res.data || {};
					if (code == 2000 && data.code == 0) {
						return this.pushToResult();
					}

					const diff = Date.now() - start;
					if (diff < 2000) {
						await new Promise(resolve => {
							setTimeout(resolve, 2000 - diff);
						});
					}
				} catch (e) {

				}
			}

			Dialog.confirm({
				title: '支付结果',
				message: '请根据您的支付结果，点击以下操作',
				confirmButtonText: '重新支付',
				cancelButtonText: '支付完成',
			}).catch(() => {

			}).finally(() => {
				this.onSubmit();
			});
		},

		onSubmit() {
			this.$toast.loading({
				message: '订单处理中...',
				forbidClick: true,
				duration: 0,
			});

			const params = { infoNo: this.obj.infoNo };
			submitPromoteOrderInfo(params, {}).then(url => {
				window.location.href = url;
			}).catch(err => {
				this.fetchPayStatus();
			}).finally(() => {
				this.$toast.clear(true);
			});
		},

		pushToHome(msg) {
			const location = {
				name: 'NWTK03Index1',
			};

			this.openToast(msg || '该订单无效，正在跳转到首页');

			setTimeout(() => {
				this.$router.replace(location);
			}, 2000);
		},

		pushToResult() {
			const href = window.location.href.replace('/PayAgain', '/Upgrade');
			window.location.href = href;
		},

		openToast(message = '', duration = 2000) {
			Toast({
				message: message,
				duration: duration, // 弹窗时间毫秒
				position: 'middle',
				forbidClick: true,
			});
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2304041313 {
		width: 3.75rem;
		min-height: 100%;
		font-size: 0.15rem;
		background-color: #f2f2f2;

		.looking {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.looking-label {
				margin-top: 0.2rem;
				font-size: 0.18rem;
				color: #333333;
			}

			.looking-wave {
				transform: rotate(0deg);
				margin-top: 0.5rem;
				position: relative;
				width: 1.5rem;
				height: 1.5rem;
				background-color: rgb(23, 106, 201);
				border-radius: 50%;
				overflow: hidden;

				&::after {
					content: "";
					position: absolute;
					top: 0;
					left: 50%;
					width: 150%;
					height: 150%;
					border-radius: 40%;
					background-color: rgb(240, 228, 228);
					animation: fill 5s linear infinite;
				}

				@keyframes fill {
					0% {
						transform: translate(-50%, -65%) rotate(0deg);
					}
					100% {
						transform: translate(-50%, -65%) rotate(360deg);
					}
				}

				&::before {
					content: "";
					position: absolute;
					top: 0;
					left: 50%;
					width: 150%;
					height: 150%;
					border-radius: 42%;
					background-color: rgba(240, 228, 228, 0.2);
					animation: roll 7s linear infinite;
				}

				@keyframes roll {
					0% {
						transform: translate(-50%, -60%) rotate(0deg);
					}
					100% {
						transform: translate(-50%, -60%) rotate(360deg);
					}
				}
			}
		}
	}
</style>
