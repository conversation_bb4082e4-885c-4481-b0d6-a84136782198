export const PremiumRate = [
    { min: 0, max: 50, month: { data1: 1.4, data2: 4.9 }, year: { data1: 0.38, data2: 0.38 } },
    { min: 51, max: 60, month: { data1: 0.8, data2: 2.9 }, year: { data1: 19.72, data2: 19.72 } },
    { min: 61, max: 70, month: { data1: 1.3, data2: 4.4 }, year: { data1: 46.08, data2: 46.08 } },
]

export const PremiumRate1 = [
    { min: 0, max: 4, month: { data1: 76, data2: 159 }, year: { data1: 123.0, data2: 100.0 } },
    { min: 5, max: 10, month: { data1: 40, data2: 76 }, year: { data1: 80.0, data2: 75.0 } },
    { min: 11, max: 15, month: { data1: 27, data2: 48 }, year: { data1: 96.0, data2: 91.0 } },
    { min: 16, max: 20, month: { data1: 16, data2: 25 }, year: { data1: 136.0, data2: 131.0 } },
    { min: 21, max: 25, month: { data1: 32, data2: 51 }, year: { data1: 210.0, data2: 253.0 } },
    { min: 26, max: 30, month: { data1: 39, data2: 67 }, year: { data1: 349.0, data2: 423.0 } },
    { min: 31, max: 35, month: { data1: 54, data2: 101 }, year: { data1: 619.0, data2: 685.0 } },
    { min: 36, max: 40, month: { data1: 65, data2: 125 }, year: { data1: 775.0, data2: 832.0 } },
    { min: 41, max: 45, month: { data1: 78, data2: 179 }, year: { data1: 1191.0, data2: 1211.0 } },
    { min: 46, max: 50, month: { data1: 90, data2: 254 }, year: { data1: 1119.0, data2: 1091.0 } },
    { min: 51, max: 55, month: { data1: 105, data2: 382 }, year: { data1: 1637.0, data2: 1552.0 } },
    { min: 56, max: 60, month: { data1: 133, data2: 487 }, year: { data1: 2370.0, data2: 2232.0 } },
    { min: 61, max: 65, month: { data1: 190, data2: 696 }, year: { data1: 5130.0, data2: 4899.0 } },
    { min: 66, max: 70, month: { data1: 254, data2: 923 }, year: { data1: 5130.0, data2: 4899.0 } },
]

export const documentList = [
    { page: '投保须知', belongs: 'v1v2' },
    {
        page: '保险条款', belongs: 'v1', list: [
            '个人医疗保险V款（互联网专属）条款',
        ],
    },
    {
        page: '保险条款', belongs: 'v2', list: [
            '个人医疗保险V款（互联网专属）条款',
            '附加个人重大疾病住院津贴保险C款（互联网专属）条款',
            '附加癌症院外特定药品费用医疗保险H款（互联网专属）条款',
            '附加个人重大疾病保险（互联网专属）条款',
            '附加重大疾病扩展特需医疗保险（互联网专属）条款',
        ],
    },
    { page: '健康告知', belongs: 'v1v2' },
    { page: '责任免除', belongs: 'v1v2' },
    { page: '特别约定', belongs: 'v1v2' },
    { page: '职业类别表', belongs: 'v1v2' },
];

export const pdfFileObj = {
    '健康告知': '/YiXin/YXTK01/jkgz.pdf',
    '职业类别表': '/YiXin/YXTK01/zylbb.pdf',

    '投保须知$v1': '/YiXin/YXTK01/tbxz_v1.pdf',
    '投保须知$v2': '/YiXin/YXTK01/tbxz_v2.pdf',
    '责任免除$v1': '/YiXin/YXTK01/zrmc_v1.pdf',
    '责任免除$v2': '/YiXin/YXTK01/zrmc_v2.pdf',
    '特别约定$v1': '/YiXin/YXTK01/tbyd_v1.pdf',
    '特别约定$v2': '/YiXin/YXTK01/tbyd_v2.pdf',
    '个人医疗保险V款（互联网专属）条款': '/YiXin/YXTK01/dir1.pdf',
    '附加个人重大疾病住院津贴保险C款（互联网专属）条款': '/YiXin/YXTK01/dir2.pdf',
    '附加癌症院外特定药品费用医疗保险H款（互联网专属）条款': '/YiXin/YXTK01/dir3.pdf',
    '附加个人重大疾病保险（互联网专属）条款': '/YiXin/YXTK01/dir4.pdf',
    '附加重大疾病扩展特需医疗保险（互联网专属）条款': '/YiXin/YXTK01/dir5.pdf',
}

export const imageFileObj = {
    '健康告知': { path: "/YiXin/YXTK01/jkgz", name: "jkgz", count: 1 },
    '职业类别表': { path: "/YiXin/YXTK01/zylbb", name: "zylbb", count: 2 },

    '投保须知$v1': { path: "/YiXin/YXTK01/tbxz_v1", name: "tbxz_v1", count: 5 },
    '投保须知$v2': { path: "/YiXin/YXTK01/tbxz_v2", name: "tbxz_v2", count: 5 },
    '责任免除$v1': { path: "/YiXin/YXTK01/zrmc_v1", name: "zrmc_v1", count: 1 },
    '责任免除$v2': { path: "/YiXin/YXTK01/zrmc_v2", name: "zrmc_v2", count: 2 },
    '特别约定$v1': { path: "/YiXin/YXTK01/tbyd_v1", name: "tbyd_v1", count: 2 },
    '特别约定$v2': { path: "/YiXin/YXTK01/tbyd_v2", name: "tbyd_v2", count: 2 },
    '个人医疗保险V款（互联网专属）条款': { path: "/YiXin/YXTK01/dir1", name: "dir1", count: 39 },
    '附加个人重大疾病住院津贴保险C款（互联网专属）条款': { path: "/YiXin/YXTK01/dir2", name: "dir2", count: 4 },
    '附加癌症院外特定药品费用医疗保险H款（互联网专属）条款': { path: "/YiXin/YXTK01/dir3", name: "dir3", count: 13 },
    '附加个人重大疾病保险（互联网专属）条款': { path: "/YiXin/YXTK01/dir4", name: "dir4", count: 6 },
    '附加重大疾病扩展特需医疗保险（互联网专属）条款': { path: "/YiXin/YXTK01/dir5", name: "dir5", count: 10 },
}

// 投保关系
export const relations = [
    { key: "本人", value: 1, param: 1 },
    { key: "配偶", value: 2, param: 2 },
    { key: "儿女", value: 3, param: 3 },
    { key: "父母", value: 4, param: 4 },
]

// 有无社保
export const insurances = [
    { key: "有医保(含新农合)", value: 1 },
    { key: "无医保", value: 0 },
]

// 缴费方式
export const repays = [
    { key: "按月缴费(12期)", value: 1 },
    { key: "全额缴费", value: 0 },
]

// 保障内容
export const planFeature = [
    '保单门槛低',
    '覆盖人群广',
    '保单可验真',
]

export const planSummary = [
    { key: '一般医疗保险金', value: '300万' },
    { key: '重大疾病医疗保险金', value: '600万' },
    { key: '等待期', value: '30天' },
    { key: '犹豫期', value: '15天' },
]

export const planSummary1 = planSummary

export const planPoints = [
    { key: '保障期限', value: '1年' },
    { key: '被保人年龄', value: '出生满30天至70周岁' },
    { key: '免赔额', value: '0-50周岁：2万元<br>51-70周岁：3万元' },
    { key: '宽限期', value: '30天' },
]

export const planPoints1 = planPoints

export const planDetails = [
    {
        key: '一般医疗保险金',
        value: '300万',
        text: ' 1、被保险人因遭受意外或在等待期（30天）后罹患本合同所定义的重大疾病以外的疾病，在二级或二级以上公立医院普通部发生的，需被保险人支付的、必需且合理的医疗费用。<br>2、一般医疗费用包含：住院医疗费用、特殊门诊医疗费用、门诊手术医疗费用、住院前后门（急）诊医疗费用。<br>3、赔偿比例如下：<br>有社保身份购买，以社保身份就诊并结算，赔偿比例为30%；<br>有社保身份购买，但未以社保身份就诊并结算的，赔偿比例为18%；<br>无社保身份购买，赔偿比例为30%。'
    }, {
        key: '重大疾病医疗保险金',
        value: '600万',
        text: ' 1、被保险人因遭受意外或在等待期（30天）后初次确诊非因意外伤害导致罹患本合同所定义的重大疾病（无论一种或多种），在二级或二级以上公立医院普通部发生的，需被保险人支付的、必需且合理的医疗费用。<br>2、重大疾病医疗费用包含：重大疾病住院医疗费用、重大疾病特殊门诊医疗费用、重大疾病门诊手术医疗费用、重大疾病住院前后门（急）诊医疗费用。<br>3、赔偿比例如下：<br>有社保身份购买，以社保身份就诊并结算，赔偿比例为30%；<br>有社保身份购买，但未以社保身份就诊并结算的，赔偿比例为18%；<br>无社保身份购买，赔偿比例为30%。 '
    },
]

export const planDetails1 = [
    {
        key: '社保内原发恶性肿瘤医疗保险金',
        value: '300万元',
        text: '1、社保内原发恶性肿瘤医疗保险金免赔额为0元。<br>2、若被保险人以有社会医疗保险身份投保，且以社会医疗保险身份就诊并结算的，保险人按100%的比例进行赔付。若被保险人以有社会医疗保险身份投保，但未以社会医疗保险身份就诊并结算的，保险人按照60%的比例进行赔付。若被保险人以无社会医疗保险身份投保，保险人按照100%的比例进行赔付。'
    }, {
        key: '社保外原发恶性肿瘤医疗保险金',
        value: '300万元',
        text: '1、社保外原发恶性肿瘤医疗保险金免赔额为0元。<br>2、社保外原发恶性肿瘤医疗保险金的赔付比例为100%。'
    }, {
        key: '恶性肿瘤院外特种药品费用保险金',
        value: '600万元',
        text: '1、恶性肿瘤院外特种药品费用保险金的免赔额为0元。<br>2、（一）社保目录内药品费用赔偿比例：<br>（1）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，赔偿比例为100%；<br>（2）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，赔偿比例为60%；<br>（3）被保险人以未参加社会基本医疗保险或公费医疗身份投保，赔偿比例为100%。<br>（二）社保目录外药品费用赔偿比例为100%。'
    }, {
        key: '质子重离子医疗保险金',
        value: '600万元',
        text: '1、质子重离子医疗保险金的免赔额为0元。<br>2、质子重离子医疗保险金的赔偿比例为100%。升级方案选择承保的社保内原发恶性肿瘤医疗保险金、社保外原发恶性肿瘤医疗保险金、恶性肿瘤院外特种药品费用保险金、质子重离子医疗保险金责任，年度累计保险金额为600万元。'
    }, {
        key: '重大疾病异地转诊公共交通费用及救护车费用保险金',
        value: '1万元',
        text: '1、重大疾病异地转诊公共交通费用及救护车费用保险金的赔付比例为100%。<br>2、重大疾病异地转诊公共交通费用及救护车费用保险金的次限额为1000元，保险金额为1万元。'
    }, {
        key: '互联网医院特定药品费用医疗保险金',
        value: '4 万元',
        text: '1、互联网医院特定疾病药品费用赔付比例 50%，慢病特定药品费用赔付比例 30%。<br>2、互联网医院特定疾病药品费用单次赔偿限额 800 元，慢病特定药品费用单次赔偿限额 2000 元，每月限 1 次。'
    }, {
        key: '指定疾病及手术扩展特需费用保险金',
        value: '600万元',
        text: '1、指定疾病及手术扩展特需费用保险金赔付比例100%。<br>2、指定疾病及手术扩展特需费用保险金床位费限额为1500元/日。'
    }, {
        key: '癌症住院津贴保险金 最高 300 元/天',
        value: '600万元',
        text: '1、癌症免赔住院日数为 3 天，0-45 周岁按照 300 元/天给付，46-70 周岁按照 100 元/天给付。<br> 2、被保险人同一次住院的癌症住院津贴最高给付日数以九十日为限，保险期间内累计癌症住院津贴最高给付日数以一百八十日为限。'
    },
]

// 理赔说明
export const claimProcess = [
    {
        key: "第1步：理赔报案",
        value: '拨打40007-95522客服热线进行理赔报案，随后有专业理赔人员联系。'
    }, {
        key: "第2步：准备保险事故相关材料",
        value: "根据工作人员引导准备并提交理赔资料。"
    }, {
        key: "第3步：完成理赔",
        value: "根据审核结果将理赔款项支付到被保人指定账户。理赔时效：一般会在资料提交后5个工作日结案，需调查核实的复杂案件30日。"
    },
]

export const askAnswerList = [
    {
        q: '是否保证续保？',
        a: ' 本产品为不保证续保合同。本产品保险期间为一年。保险期间届满或保险期间届满前三十日内，投保人需要重新向保险人申请投保本产品，并经保险人同意，交纳保险费，获得新的保险合同。 '
    }, {
        q: '本产品的就诊医院有哪些？',
        a: '本产品就诊医院为中华人民共和国境内（不包括港、澳、台地区）合法经营的、国家卫生部医院等级分类中的二级或二级以上公立医院普通部及保险人扩展承保的医院普通部。',
    }, {
        q: '本产品的等待期有多久？',
        a: '本产品网上投保申请日后次日零时生效，等待期为30天（含犹豫期），续保或意外伤害引起的保险事故无等待期。',
    }, {
        q: '未及时缴费有什么影响？',
        a: ' 若投保人未按约定交纳其余各期保险费，保险人允许投保人在约定交费日起三十日（含第三十日）内补交保险费，如果被保险人在此三十日内发生保险事故，保险人将扣减投保人欠交的保险费后按主险合同约定承担赔偿保险金的责任。如果投保人在约定交费日起三十日（含第三十日）内未补交保险费，本合同自上述期限届满之日二十四时起效力中止，自本合同效力中止日起发生的保险事故或医疗费用，保险公司不承担保险责任。 ',
    },
]
