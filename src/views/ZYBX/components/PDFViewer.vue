<template>
    <div class="container_2307031600">
        <iframe :src="fileUrl" class="iframe"></iframe>
    </div>
</template>

<script>

export default {
    name: "PDFViewer",
    props: { file: String },
    computed: {
        fileUrl() {
            if (this.file.indexOf('.pdf') < 0) {
                return this.file;
            }

            const viewer = 'https://cdns.bountech.com/marketfront/financecdn/pdfjs/web/viewer.html?file=';
            const fileUrl = this.file.indexOf('http') >= 0 ? this.file : 'https://cdns.bountech.com/marketfront/file/insurance/cdn/picture' + this.file;

            return viewer + encodeURIComponent(fileUrl);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2307031600 {
    height: 100%;

    .iframe {
        width: 100%;
        height: 100%;
    }
}
</style>
