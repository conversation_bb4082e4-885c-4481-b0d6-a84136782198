 <!-- 
  输入：
  必填：
    value(v-model): 选中数据，
    options：数据选项列表，包括value属性，name属性，group属性（可选，不同group互斥）
  非必填：
    multiple： 是否多选，默认单选，
    buttonColor：颜色，可选，默认orange，
    hasGroup： 是否存在互斥选项，可选，请确保options有group属性却multiple未true，
  输出：@input，@change
 -->
<template>
    <div class="button-selection-box">
      <div v-for="item in options" :key="item.value" class="button-box">
        <template v-if="isSelected(item)">
          <button :class="buttonClassMap[buttonColor]" @click="handleChange(item)">{{item.name}}</button>
          <span class="triangle" :class="triangleClassMap[buttonColor]"><van-icon color="#fff" size="0.12rem" name="success" /></span>
        </template>
        <button v-else :class="handleButtonClass[item.group]" @click="handleChange(item)">{{item.name}}</button>
      </div>
    </div>
</template>

<script>
  export default {
    name: "ButtonSelection",
    props: {
      value: {
        type: [String, Number, Array],
        require: true
      },
      multiple: {
        type: Boolean,
        default: false
      },
      hasGroup: {
        type: Boolean,
        default: false
      },
      options: {
        type: Array,
        require: true
      },
      buttonColor: {
        type: String,
        default: 'orange'
      }
    },
    data() {
        return {
          selectedData: this.value,
          buttonClassMap: {
            none: 'none-btn',
            orange: 'orange-btn',  // 百保君保险顾问使用，调用请加选项
            blue: 'blue-btn',
            green: 'green-btn'
          },
          triangleClassMap: {
            orange: 'orange-triangle', // 百保君保险顾问使用，请勿调整
            blue: 'blue-triangle',
            green: 'green-triangle'
          },
        }
    },
    computed: {
      selectedGroup () {
        if (this.selectedData && this.selectedData.length > 0) {
          const selectedItem = this.options.find(item => item.value === this.selectedData[0])
          return selectedItem.group || ''
        }
        return ''
      }
    },
    watch: {
    },
    mounted() {
    },
    methods: {
      handleButtonClass(group = '') {
        if (this.hasGroup && this.selectedGroup && this.selectedGroup !== group) {
          return 'disable-btn'
        }
        return  this.buttonClassMap.none
      },
      isSelected(item) {
        return this.multiple ? this.selectedData.includes(item.value) : item.value === this.selectedData
      },
      handleChange(item) {
        if (this.multiple) {
          if (this.selectedData.includes(item.value)) {
            this.selectedData = this.selectedData.filter(e => e != item.value)
          } else {
            if (!this.hasGroup || !this.selectedGroup || this.selectedGroup === item.group) {
              this.selectedData.push(item.value)
            }
          }
          this.$emit('input', this.selectedData)
          this.$emit('change', this.selectedData )
        } else {
          this.selectedData = this.selectedData === item.value ? null : item.value
          this.$emit('input', this.selectedData )
          this.$emit('change', this.selectedData )
        }
      }
    },
  }
</script>

<style scoped lang="less" type="text/less">
  .button-selection-box {
    width: 100%;
    z-index: 1000;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .button-box {
      width: 0.753rem;
      height: 0.34rem;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: normal;
      position: relative;
      border-radius: 0.03rem;
      overflow: hidden;
      button {
        width: 0.73rem;
        height: 0.27rem;
        background-color: #eee; // #f8f0eb;
        color: #000;
        border-radius: 0.03rem;
        font-size: 0.13rem;
        border: 0;
      }
      .none-btn {
        border: 0;
        color: #000;
      }
      .orange-btn {
        border: 0.01rem solid #ff7a21;
        color: #ff7a21;
      }
      .blue-btn {
        color: #318efe;
        border-color: #318efe;
      }
      .green-btn {
        color: #47dc8d;
        border-color:#47dc8d;
      }
      .disable-btn {
        background-color: #fff;
        border-color: 0.01rem solid #ccc;
        color: #ccc;
      }
    }
    
    .triangle {
      position: absolute;
      top: 0.039rem;
      right: 0.017rem;
      width: 0.1rem;
      height: 0.1rem;
      -webkit-clip-path: polygon(100% 0, 0 0, 100% 100%);
      clip-path: polygon(100% 0, 0 0, 100% 100%);
      background-color: #ff7a21;
      .van-icon {
        position: absolute;
        top: -0.04rem;
        right: -0.04rem;
        transform: scale(0.36);
      }
    }
    .orange-triangle {
      background-color: #ff7a21;
    }
    .blue-triangle {
      background-color: #318efe;
    }
    .green-triangle {
      background-color: #47dc8d;
    }
  }


</style>
