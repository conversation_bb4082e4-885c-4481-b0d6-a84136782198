<template>
    <!--保险条款-->
    <div class="container_2302222110">
        <div class="content">
            <div v-for="item in list" :key="item" class="item" @click="onItemClick(item)">
                {{ item }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "Policy_List",
    props: {list: Array},
    computed: {
        policyList() {
            return this.list || [];
        }
    },
    methods: {
        onItemClick(item) {
            this.$emit('view', item);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2302222110 {
    font-size: 0.14rem;

    .content {

        .item {
            padding: 0.2rem 0.1rem;
            border-bottom: #EEEEEE 1px solid;
            line-height: 0.2rem;
        }
    }
}

</style>

