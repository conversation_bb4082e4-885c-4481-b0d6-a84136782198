<template>
	<van-popup v-model="obj.visible" class="container_2302221730" position="bottom" round>
		<van-tabs v-model="obj.page">
			<van-tab v-for="v in policyList" :key="v.page" :name="v.page" :title="v.page"></van-tab>
		</van-tabs>
		<div id="id_2302221730" class="content">
			<Policy_List v-if="list.length > 0" :list="list" @view="onViewDetail"></Policy_List>
			<HHFileViewer v-else :fileName="fileName"></HHFileViewer>
		</div>
		<div class="footer" @click="okAction">
			<div class="button">我已逐页阅读上述内容并同意</div>
		</div>
	</van-popup>
</template>

<script>
import HHFileViewer from "./HHFileViewer";
import Policy_List from "./Policy/Policy_List";
import { documentList } from "../src";

export default {
	name: "HHTabViewer",
	props: { obj: Object },
	components: {
		HHFileViewer,
		Policy_List,
	},
	computed: {
		policyList() {
			return documentList;
		},
		list() {
			const obj = this.policyList.find(item => item.page == this.obj.page);
			if (obj && Array.isArray(obj.list)) {
				return obj.list.map(item => item.page);
			}
			return [];
		},
        fileName() {
			if (this.obj.page.indexOf('客户告知书') >= 0 && this.isKaiSen) {
				return '凯森客户告知书';
			}
			return this.obj.page;
		},
	},
	watch: {
		'obj.visible': {
			handler() {
				if (this.obj.visible) {
					this.scrollToTop();
				}
			}
		},
	},
	methods: {
		okAction() {
			this.obj.visible = false;
			this.$emit('ok', true);
		},

		onViewDetail(v) {
			this.$emit('view', v);
		},

		scrollToTop() {
			this.$nextTick(() => {
				const node = document.getElementById('id_2302221730');
				node && (node.scrollTop = 0);
			});
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302221730 {
		margin: 0 auto;
		height: 80%;
		width: 3.75rem;
		left: 0;
		right: 0;

		display: flex;
		flex-direction: column;

		.content {
			flex: 1;
			overflow: auto;
		}

		.footer {
			background-color: #ffffff;
			border-top: #f2f2f2 1px solid;

			.button {
				margin: 0.08rem 0.2rem;
				height: 0.44rem;
				line-height: 0.44rem;
				border-radius: 0.22rem;
				font-size: 0.16rem;
				font-weight: 500;
				text-align: center;
				color: #ffffff;
				background: linear-gradient(
					to right,
					rgba(255, 20, 28, 0.65),
					rgba(255, 0, 0, 0.8)
				);
			}
		}
	}
</style>

