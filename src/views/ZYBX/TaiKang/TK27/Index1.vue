<template>
	<div class="container_2208310930">
		<div id="id_header_banner" class="header">
			<img src="@/assets/imgs/TaiKang/TK22/img01.png" alt="">
		</div>
		<div class="main-x">
			<HHInputBox :obj="orderInfo" @input="onTextInput" @submit="onSubmitClick" @view="({name, isPolicy}) => onViewPolicy(name, isPolicy)"></HHInputBox>
		</div>
		<van-tabs v-model="currentTab" scrollspy sticky>
			<van-tab key="产品特色" name="产品特色" title="产品特色">
				<div class="section">
					<div class="section-title">产品特色</div>
					<img src="@/assets/imgs/TaiKang/TK03/img03.png" alt="产品特色">
				</div>
			</van-tab>
			<van-tab key="理赔说明" name="理赔说明" title="理赔说明">
				<div class="section">
					<div class="section-title">理赔说明</div>
					<img src="@/assets/imgs/TaiKang/TK03/img05.png" alt="理赔说明">
					<img src="@/assets/imgs/TaiKang/TK03/img06.png" alt="理赔说明">
				</div>
			</van-tab>
			<van-tab key="疑问解答" name="疑问解答" title="疑问解答">
				<div class="section">
					<div class="section-title">疑问解答</div>
					<HHAskAnswer @click="onViewPolicy('常见问题')"></HHAskAnswer>
				</div>
			</van-tab>
		</van-tabs>
		<img src="@/assets/imgs/TaiKang/bottom_tk_1.png" class="copyright">
		<div v-if="bottomButtonVisible" class="bottomButton" @click="onSubmitClick">
			立即投保<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
		</div>
		<van-popup v-model="isMask" class="mask" :close-on-click-overlay="false">
			<img src="@/assets/imgs/common/icon_browser.png" />
		</van-popup>
		<HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
		<HHFailPopup :obj="failObj" @click="pushFailLink"></HHFailPopup>
		<HHSilentViewer :obj="policyObj" :obj1="orderInfo" @click="onSilentUpgrade"></HHSilentViewer>
		<HHTabViewer :obj="policyObj" @ok="onAcceptPolicy"></HHTabViewer>
		<!--录屏-->
		<TKRecord ref="ref_record" :recordObj="orderInfo"></TKRecord>
	</div>
</template>

<script>
import moment from "moment";
import HHFailPopup from "./components/HHFailPopup";
import HHTabViewer from "./components/HHTabViewer";
import HHInputBox from "./components/HHInputBox";
import HHSilentViewer from "./components/HHSilentViewer";
import HHAskAnswer from "./components/HHAskAnswer";
import HHPolicyViewer from "./components/HHPolicyViewer";
import TKRecord from "@/views/components/TKRecord";
import { isHuaWeiBrowser, isPhoneNum, TraceLogInfoKeys, isInWx, isAndroid, url_safe_b64_encode, Url, isMaskedAndT1Phone, object2QueryParams, url_safe_b64_decode } from "@/assets/js/common";
import { checkOrderParams, createOrderInfo, eventTracking, inputEndEditing, loadOrderInfo, saveOrderInfo, showToast, submitOrderInfo, } from "./function";
import { bindClickAndOrderForTX, conversionMonitorDataSource, fetchInfoByOrderNo, fetchRoundRobinWithAgeCommonFail, fetchStarPhoneV4, } from "@/api/insurance-api";
import { domainPathMap } from "@/views/ZYBX/src";

export default {
	name: "Index1", // 推广版
	components: {
		HHPolicyViewer,
		HHAskAnswer,
		HHInputBox,
		HHTabViewer,
		TKRecord,
		HHSilentViewer,
		HHFailPopup,
	},
	data() {
		const orderInfo = createOrderInfo('direct');
		return {
			orderInfo,
			currentTab: '',       // 切换的tab
			autoSubmit: true,    // 自动拉起支付
			bottomButtonVisible: false,
			policyObj: { v: false, page: '', v1: false, page1: '', v2: false, isAccept: false, belongs: 'v1' },
			failObj: { v: false, path: '' },
			isMask: false,
		}
	},
	computed: {
		orderPage() {
			const { source, action, school, identifier } = this.orderInfo;
			const productKey = school == 1 ? TraceLogInfoKeys.iyb_tk_372_cn_medical_high_default_cube_base : TraceLogInfoKeys.iyb_tk_372_cn_medical_high_cube_base;
			return `infoKey:${productKey}&page:${identifier}&act:${action}&src:${source}`;
		},
	},
	created() {
		this.init();
	},
	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		if (isInWx()) {
			if (isAndroid()) {
				const url = url_safe_b64_encode(window.location.href);
				setTimeout(() => {
					window.location.href = `${Url}/Insurance/info/checkIsJump?url=${url}`;
				}, 500);
			}
			return this.isMask = true;
		}

		const enterUpgrade = loadOrderInfo('TK27_Upgrade') || 0;
		if (enterUpgrade) {
			this.fetchInfoByOrderNo(true);
		}

		this.$nextTick(() => { // 监听滚动事件
			this.actionButtonObserver();
			this.addObserverForBanner();
		});
	},
	methods: {
		actionButtonObserver() {
			if (window.IntersectionObserver) {
				const observer = new IntersectionObserver((entries, observer) => {
					this.bottomButtonVisible = !entries[0].isIntersecting;
				}, { threshold: 0.10 });

				const buttonNode = document.getElementById('id_action_button');
				buttonNode && observer.observe(buttonNode);
			}
		},
		addObserverForBanner() {
			if (window.IntersectionObserver) {
				const observer = new IntersectionObserver((entries, observer) => {
					const entry = entries[0];
					if (!entry.isIntersecting) {
						observer.unobserve(entry.target);
						this.scrollTimeReport();
					}
				}, { threshold: 0.10 });

				const buttonNode = document.getElementById('id_header_banner');
				buttonNode && observer.observe(buttonNode);
			}
		},

		onAcceptPolicy() {
			this.orderInfo.checked = true;
			if (this.orderInfo.v2) {
				return;
			}
			if (this.policyObj.isAccept) {
				return this.policyObj.v2 = true;
			}
			this.onSubmitClick();
		},

		onSilentUpgrade(value) {
			this._actionTracking(`首页：${value ? '默认升级' : '正常升级'}`);
			this.orderInfo.school = value ? 1 : 0;

			this.onSubmitOrder();
		},
		// 初始化
		init() {
			const query = this.$route.query || {};
			query.source = query.source || 'direct'; // source要以链接携带的参数为准
			query.action = query.action || 'direct'; // action要以链接携带的参数为准
			query.sourcePage = query.sourcePage || ''; // sourcePage要以链接携带的参数为准
			query.channelCode = query.cld || query.channelCode || '';
			query.qz_gdt = query.qz_gdt || query.gdt_vid || query.click__id || query.clickid || '';
			query.qz_platform = query.qz_platform || 'TENG_XUN';
			query.advertiserName = query.advertiserName || '';

			const inStore = loadOrderInfo() || {};
			Object.assign(this.orderInfo, inStore, query);
            try {
                if (query.bizParams) {
                    const params = JSON.parse(url_safe_b64_decode(query.bizParams));
                    Object.assign(this.orderInfo, params);
                }
            } catch (error) {

            }

			this.orderInfo.identifier = 'TK27Index1';
			this.orderInfo.productKey = TraceLogInfoKeys.iyb_tk_372_cn_medical_high_default_cube_base;
			this.orderInfo.school = '1';
			this.orderInfo.policyNo = '';
			this.orderInfo.checked = this.orderInfo.channel >= 1000;

			const { phoneNo, starPhone, mTel } = this.orderInfo;
			if (!isPhoneNum(phoneNo) && isMaskedAndT1Phone(starPhone, mTel)) {
				this.orderInfo.phoneNo = starPhone;
			} else {
				this.orderInfo.starPhone = '';
			}

			this.fetchPhoneNumber();

			if (this.orderInfo.source == 'direct') {
				this.conversionMonitorDataSource("VIEW_CONTENT");
			}
		},
		// 手机号解密
		fetchPhoneNumber() {
			const { m, phoneNo, starPhone, mTel } = this.orderInfo;
			if (!m || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
				return this._entryReport();
			}

			const params = { encryptContent: m };
			fetchStarPhoneV4(params).then(res => {
				const { encryptPhone, showPhone } = res.data;
				if (isMaskedAndT1Phone(showPhone, encryptPhone)) {
					this.orderInfo.mTel = encryptPhone;
					this.orderInfo.starPhone = showPhone;
					this.orderInfo.phoneNo = showPhone;
					saveOrderInfo(this.orderInfo);
				}
			}).finally(() => {
				return this._entryReport();
			});
		},
		// 输入框输入，自动拉起下单
		onTextInput() {
			saveOrderInfo(this.orderInfo);

			if (this.autoSubmit) {
				this.onSubmitClick('AUTO');
			}
		},
		// 点击提交按钮
		onSubmitClick(from) {
			const { code, msg, } = checkOrderParams(this.orderInfo, this.orderPage);
			if (from != 'AUTO' || code == 0 || msg == '用户协议未同意') {
				this._actionTracking('点击立即投保按钮');
			}
			if (code != 0) {
				if (msg == '用户协议未同意') {
					this.showToastAndEndEdit();
					return this.onViewPolicy('投保须知', true, true);
				}
				return (from != 'AUTO') && this.showToastAndEndEdit(msg);
			}

			this.showToastAndEndEdit();
			this.policyObj.v2 = true;
		},

		onSubmitOrder() {
			this.showToastAndEndEdit();

			this.$toast.loading({
				message: '订单提交中\n请稍候',
				forbidClick: true,
				duration: 0,
			});

			if (isInWx()) {
				this.orderInfo.paymentCode = 0;
			} else {
				this.orderInfo.paymentCode = 1;
			}

			this.createSubmitOrder();

			this.conversionMonitorDataSource("RESERVATION");
			this.bindClickAndOrderForTX();
		},

		createSubmitOrder() {
			this._actionTracking('点击立即领取按钮');

			const { code, msg, params } = checkOrderParams(this.orderInfo, this.orderPage);
			if (this.orderInfo.school != 1) {
				params.planKey = TraceLogInfoKeys.iyb_tk_372_cn_medical_high_cube_base;
			}

			if (isHuaWeiBrowser()) {
				params.extendParams.midPage = 'default';
				params.extendParams.failUrl = `${params.extendParams.failUrl}&midPage=default`;
			}

			if (this.orderInfo.paymentCode >= 1 && this.orderInfo.paymentCode <= 7) {
				params.extendParams.bankCode = 'ALIPAYC';
				params.extendParams.failUrl = `${params.extendParams.failUrl}&bankCode=ALIPAYC`;
			}

			params.extendParams = JSON.stringify(params.extendParams || {});
			submitOrderInfo(params, this.orderInfo,).then(url => {
				this.$refs.ref_record && this.$refs.ref_record.uploadVideo();
				setTimeout(() => {
                    window.location.href = url;
                }, 300);
			}).catch(err => {
				const message = err.msg || '';
				if (!message.includes('不能重复购买') && !message.includes('已存在保单') && !message.includes('投保份数')) {
					return this.fetchFailLink();
				}
				this.fetchInfoByOrderNo();
			}).finally(() => {
				saveOrderInfo(this.orderInfo);
				this.$toast.clear(true);
			});
		},

		conversionMonitorDataSource(actionType = "VIEW_CONTENT") {
			const { qz_gdt, qz_platform, advertiserName } = this.orderInfo;
			if (!qz_gdt) return;

			const params = {
				platform: qz_platform,
				clickId: qz_gdt,
				url: window.location.host,
				actionType,
				advertiserName,
			}

			conversionMonitorDataSource(params).then(res => {

			}).catch(err => {
				console.log(err);
			});
		},

		bindClickAndOrderForTX() {
			const { qz_gdt, qz_platform, phoneNo, mTel, advertiserName } = this.orderInfo;
			if (!qz_gdt) return;

			const params = {
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				platform: qz_platform,
				clickId: qz_gdt,
				url: window.location.host,
				actionType: 'COMPLETE_ORDER',
				advertiserName,
			}

			bindClickAndOrderForTX(params).then(res => {

			}).catch(err => {

			});
		},

		// 根据orderNo查询订单信息
		fetchInfoByOrderNo(enterUpgrade) {
			const { relation, callbackUrl } = this.orderInfo;
			const idCardNo = this.orderInfo[`idCard${relation}`];
			const params = { infoKey: TraceLogInfoKeys.iyb_tk_372_cn_medical_high_cube_base, insuredIdCard: idCardNo };

			fetchInfoByOrderNo(params).then(r => {
				const { code, data } = r.data;
				if (code != 2000 || !data) {
					return !enterUpgrade && this.fetchFailLink();
				}

				const { infoNo } = data || {};
				if (infoNo && callbackUrl && callbackUrl.indexOf('http') >= 0) {
					const params = `infoNo=${infoNo}`;
					let href = callbackUrl.replace(/&?infoNo=[^?&]*/ig, '');
					href = href.indexOf('?') > 0 ? `${href}&${params}` : `${href}?${params}`;

					return window.location.href = href;
				}

				return !enterUpgrade && this.fetchFailLink();
			}).catch((err) => {
				return !enterUpgrade && this.fetchFailLink();
			});
		},

		fetchFailLink() {
			const { channel, phoneNo, mTel, relation } = this.orderInfo;

			const params = {
				channelId: channel,
                idCard: this.orderInfo[`idCard${relation}`],
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				robinKey: 'tk_mf_common_fail',
                currentPage: 'TK27',
			}

			fetchRoundRobinWithAgeCommonFail(params).then(result => {
				this.failObj.path = result.data.path;
			}).finally(() => {
                if (this.failObj.path == 'NotFound') {
                    return this.showToastAndEndEdit('投保失败，您可以选择为家人投保');
                }

				this.failObj.v = true;
				this._actionTracking(`显示核保失败弹窗(${this.failObj.path})`);
			});
		},

		pushFailLink() {
			let path = this.failObj.path;
			if (!domainPathMap[path]) {
				path = 'ZAZY02Index2';
			}
			this._actionTracking(`点击核保失败图片(${path})`);

			const { channel, relation, mTel, phoneNo, channelCode, starPhone, identifier, name1, idCard1 } = this.orderInfo;

			const params = {
				channel, cld: channelCode, mTel, relation, source: identifier, action: 'forward',
				name1, idCard1, [`name${relation}`]: this.orderInfo[`name${relation}`], [`idCard${relation}`]: this.orderInfo[`idCard${relation}`]
			}

			if (isPhoneNum(phoneNo)) {
				params.phoneNo = phoneNo;
				params.starPhone = '';
			} else if (isMaskedAndT1Phone(starPhone, mTel)) {
				params.phoneNo = '';
				params.starPhone = starPhone;
			}

			const query = object2QueryParams(params);

			setTimeout(() => {
				const href = domainPathMap[path];
				window.location.href = `${href}?${query}`;
			}, 250);
		},

		onViewPolicy(name, isPolicy, isAccept) {
			this.policyObj.isAccept = isAccept;
			this.policyObj.belongs = 'v1';
			this.policyObj[isPolicy ? 'v1' : 'v'] = true;
			this.policyObj[isPolicy ? 'page1' : 'page'] = name;
		},
		scrollTimeReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd } = timing || {};
			const detentionTime = moment() - domContentLoadedEventEnd;
			this._actionTracking('首页滚动', detentionTime);
		},
		_entryReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);
		},
		_actionTracking(name, time = 0) {
			eventTracking(this.orderInfo, name, time);
		},
		showToastAndEndEdit(message) {
			message && showToast(message);
			inputEndEditing();
			document.body.scrollTop = document.documentElement.scrollTop = 300;
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2208310930 {
		width: 3.75rem;
		min-height: 100%;
		font-size: 0.15rem;
		background-color: #f8f8f8;

		img {
			display: block;
			max-width: 100%;
		}

		.mask {
			height: 100%;
			width: 100%;
			background-color: unset;

			img {
				display: block;
				width: 3.5rem;
				margin: 0 auto;
			}
		}

		.section {
			background-color: #ffffff;

			.section-title {
				display: flex;
				justify-content: center;
				align-items: center;

				color: #333333;
				font-size: 0.18rem;
				font-weight: 500;
				line-height: 0.45rem;

				&::before,
				&::after {
					content: " ";
					width: 0.55rem;
					height: 0.13rem;
					background: no-repeat center/100%;
				}

				&::before {
					margin-right: 0.1rem;
					background-image: url("~@/assets/imgs/common/icon_needle_left.png");
				}

				&::after {
					margin-left: 0.1rem;
					background-image: url("~@/assets/imgs/common/icon_needle_right.png");
				}
			}
		}
		.main-x {
			padding: 0 0.12rem 0.15rem;
			background-color: #be271a;
		}

		.bottomButton {
			position: fixed;
			inset: auto 0 0.25rem 0;
			margin: 0 auto;
			padding: 0.15rem 0;
			width: 3rem;
			font-size: 0.2rem;
			color: #ffffff;
			font-weight: 700;
			text-align: center;
			border-radius: 999px;
			box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
			background: linear-gradient(
				270deg,
				rgb(255, 16, 46),
				rgb(253, 123, 69)
			);
			animation: button_animate 1.35s linear infinite;

			.hand {
				position: absolute;
				top: 0.25rem;
				left: 75%;
				width: 18%;
				animation: hand_animate 1s linear infinite;
			}
		}

		.copyright {
			padding: 0.3rem 0.5rem 0.8rem;
			width: 2.75rem;
		}

		@keyframes button_animate {
			0% {
				transform: scale(1);
			}
			40% {
				transform: scale(1);
			}
			70% {
				transform: scale(0.95);
			}
			100% {
				transform: scale(1);
			}
		}

		@keyframes hand_animate {
			0% {
				transform: translate(-0.1rem, -0.1rem);
			}
			45% {
				transform: translate(0.1rem, 0);
			}
			70% {
				transform: translate(0.1rem, 0);
			}
			100% {
				transform: translate(-0.1rem, -0.1rem);
			}
		}
	}
</style>





