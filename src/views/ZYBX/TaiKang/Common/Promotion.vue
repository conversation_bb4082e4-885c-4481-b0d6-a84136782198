<template>
	<div class="container_2312261330">

	</div>
</template>

<script>
import { fetchPromoteUrlForTK } from "@/api/insurance-api";
import { object2QueryParams } from '@/assets/js/common';

const links = [
	'https://js-bprod.tiancaibaoxian.com/marketfront/insurancecdn/ZYBX/TK12/Home/Index1',
	'https://channel.zhelibao.com/mktprod/rp/ZYBX/IYBTK14/Home/Index1',
	'https://m.tkcn.cc/marketfront/insurancecdn/ZYBX/TK15/Home/Index1',
]

export default {

	mounted() {
		const query = this.$route.query || {};
		if (!query.m) {
			return this.jumpToHomePage();
		}
		query.m = query.m.replace(/\s|\+/g, '-').replace(/\//g, '_').replace(/=/g, '');

		const { m, channel, } = query;
		const channelId = channel || '1';
		fetchPromoteUrlForTK(m, channelId).then(res => {
			const { code, data } = res.data || {};
			if (code != 2000) {
				return this.jumpToHomePage();
			}
			window.location.href = data;
		}).catch(err => {
			return this.jumpToHomePage();
		});
	},

	methods: {
		jumpToHomePage() {
			const domain = window.location.host;
			let link = links.find(v => v.indexOf(domain) >= 0) || links[0];
			const params = object2QueryParams(this.$route.query);
			if (params) {
				link = link + '?' + params;
			}

			window.location.href = link;
		},
	},

}
</script>

<style scoped lang="less">
	.container_2312261330 {
		min-height: 100vh;
		background-color: #ffffff;
	}
</style>