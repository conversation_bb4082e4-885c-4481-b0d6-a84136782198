<template>
    <div class="container_2211020950">
        <div class="header">
            <!-- <span class="header-title">保障内容</span>
            <span class="header-more" @click="onDetailClick">查看详情</span> -->
            <img src="@/assets/imgs/TaiKang/TK16/protect-header.png"/>
        </div>
        <div class="summary-x">
            <div v-for="(item) in planSummary" :key="item.key" class="summary-item">
                <span>{{ item.key }}</span>
                <span class="summary-item-value" v-html="item.value"></span>
            </div>
        </div>
        <!-- <div v-if="isLong" :style="{'--deg':showAll ? '180deg' : '0deg'}" class="showAll" @click="showAll= !showAll">
            <span>{{ showText }}</span>
        </div> -->
    </div>
</template>

<script>
import { planSummary } from "../src";

export default {
    name: "HHPlan",
    data() {
        const isLong = planSummary.length > 4;
        return {
            isLong,
            showAll: false,
        }
    },
    computed: {
        planSummary() {
            if (this.showAll) {
                return planSummary;
            }
            return planSummary
        },

        showText() {
            return this.showAll ? '收起' : '查看更多';
        },
    },
    methods: {
        onDetailClick() {
            this.$emit('click',);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2211020950 {
    color: #333333;
    font-size: 0.14rem;
    background-color: #FFFFFF;
    border-radius: 0.08rem;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        // padding: 0.10rem 0.15rem;
        font-weight: 500;
        img{
            display: block;
            width: 100%;
        }

        .header-title {
            font-size: 0.18rem;
        }

        .header-more {
            color: #FF4509;
        }
    }

    .feature-x {
        display: flex;
        align-items: center;
        justify-content: space-around;

        .feature-item {
            padding: 0.12rem 0;
            color: #555555;
            font-size: 0.14rem;
            font-weight: 500;
        }
    }

    .summary-x {
        margin-top: -0.08rem;
        padding-bottom: 0.04rem;

        .summary-item {
            display: flex;
            align-items: center;
            justify-content: space-between;

            padding: 0.12rem 0.16rem;
            border-bottom: #F2F2F2 1px dashed;
            color: #595959;
            &:last-child {
                border-bottom: none;
            }

            .summary-item-value {
                color: #333333;
                flex-shrink: 0;
                line-height: 1.5em;
            }
            
        }
    }

    .showAll {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #FF4509;
        line-height: 0.35rem;
        font-size: 0.13rem;

        &::after {
            content: " ";
            margin-left: 0.05rem;
            height: 0.08rem;
            width: 0.08rem;
            background: url("~@/assets/imgs/common/sprites.png") no-repeat 0 -0.32rem;
            background-size: 1.6rem 3.2rem;
            transition: 0.25s;
            transform: rotate(var(--deg));
        }
    }
}
</style>
