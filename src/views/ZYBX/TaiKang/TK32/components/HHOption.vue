<template>
    <div :class="{selected:obj.active}" :style="{width:obj.width}" class="container_2208311030" @click="clickAction">
        {{ obj.text }}
    </div>
</template>

<script>
export default {
    name: "OptionItem",
    props: {
        obj: Object,
    },
    methods: {
        clickAction() {
            this.$emit('click');
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2208311030 {
    padding: 0.12rem 0;
    min-width: 0.60rem;
    font-size: 0.14rem;
    line-height: 1.1;
    font-weight: 600;
    text-align: center;
    color: #333333;
    border-radius: 0.06rem;
    border: #DDDDDD 1px solid;
    background-color: #F8F8F8;

    &.selected {
        color: #FF8C41;
        border: #FF8C41 1px solid;
        background: #FEF7F0 url('~@/assets/imgs/common/icon_checked1.png') bottom right / 0.20rem  no-repeat;
    }
}

</style>
