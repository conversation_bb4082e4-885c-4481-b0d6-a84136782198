<template>
	<div class="container_2302211530">
		<img alt="头图" src="@/assets/imgs/TaiKang/TK16/img04.png">
		<div class="content">
			<div class="remainder">还剩<span class="remainder-count">{{ remainCount }}</span>个升级名额</div>
			<div class="process">
				<div :style="`width: ${(remainCount/300.0)*100}%`" class="process-value"></div>
			</div>
			<div id="id_action_button" class="submit-button" @click="onSubmit">
				一键升级 <span class="text">获取更高报销比例</span>
				<img alt="" class="hand" src="@/assets/imgs/common/icon_hand1.png">
			</div>
			<div class="premium">
				次月起只需{{ premiumObj.amount }}元{{ premiumObj.suffix }}/月，无需重复投保<br />
				升级后的保障将于第二期保费扣费/缴费成功后生效
			</div>
			<div class="policy-box">
				我已阅读并同意
				<span class="read" @click.stop="onViewPolicy('健康告知',true)">《健康告知》</span>
				<span class="read" @click.stop="onViewPolicy('投保须知',true)">《投保须知》</span>
				<span class="read" @click.stop="onViewPolicy('责任免除',true)">《责任免除》</span>
				<span class="read" @click.stop="onViewPolicy('特别约定',true)">《特别约定》</span>
				<span class="read" @click.stop="onViewPolicy('产品说明',true)">《产品说明》</span>
				<span class="read" @click.stop="onViewPolicy('保险条款',true)">《保险条款》</span>
				<span class="read" @click.stop="onViewPolicy('百万医疗高危职业表',true)">《百万医疗高危职业表》</span>
				<span class="read" @click.stop="onViewPolicy('费率表',false)">《费率表》</span>
			</div>
		</div>
		<img alt="产品图" src="@/assets/imgs/TaiKang/TK16/img05.png">
		<div v-if="bottomButtonVisible" class="bottomButton" @click="onSubmit">
			一键升级 <span class="text">获取更高报销比例</span>
			<img alt="" class="hand" src="@/assets/imgs/common/icon_hand1.png">
		</div>
		<HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
		<HHTabViewer :obj="policyObj" @ok="onAcceptPolicy"></HHTabViewer>
		<TKRecord ref="ref_record" :recordObj="orderInfo"></TKRecord>
	</div>
</template>

<script>
import HHTabViewer from "./components/HHTabViewer";
import TKRecord from "@/views/components/TKRecord";
import HHPolicyViewer from "./components/HHPolicyViewer";
import { TraceLogInfoKeys, url_safe_b64_decode } from "@/assets/js/common";
import { createOrderInfo, eventTracking, loadOrderInfo, saveOrderInfo, getPolicyVersion } from "./function";

export default {
	name: "Upgrade",
	components: { HHPolicyViewer, HHTabViewer, TKRecord, },
	data() {
		const orderInfo = createOrderInfo('direct');
		return {
			orderInfo,
			policyObj: { v: false, page: '', v1: false, page1: '', belongs: 'v2' },
			remainCount: 99,
			countInterval: null,
			bottomButtonVisible: false,
		}
	},
	computed: {
		premiumObj() {
			const upgradePremium = this.orderInfo.upgradePremium || 0;
			if (upgradePremium > 0) {
				return { amount: upgradePremium, suffix: '' };
			}
			return { amount: 42.07, suffix: '起' };
		},
	},
	created() {
		this.init();
	},
	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.$nextTick(() => { // 监听滚动事件
			this.actionButtonObserver();
		});

		this._entryReport();

		this.remainCount = loadOrderInfo('TK24-remainingNums') || 99;

		this.countInterval = setInterval(() => {
			if (this.remainCount <= 28) {
				this.countInterval && clearInterval(this.countInterval);
				return;
			}

			const random = Math.random() * 100;
			if (random <= 30) {
				this.remainCount--;
				saveOrderInfo(this.remainCount, 'TK24-remainingNums');
			}
		}, 5000);

		if (this.orderInfo.school == 1) {
			this.pushToResult();
		}
	},
	beforeDestroy() {
		this.countInterval && clearInterval(this.countInterval);
	},
	methods: {
		actionButtonObserver() {
			if (window.IntersectionObserver) {
				const observer = new IntersectionObserver((entries, observer) => {
					this.bottomButtonVisible = !entries[0].isIntersecting;
				}, { threshold: 0.01 });

				const buttonNode = document.getElementById('id_action_button');
				buttonNode && observer.observe(buttonNode);
			}
		},

		// 初始化
		init() {
			const query = this.$route.query || {};
			query.source = query.source || 'direct'; // source要以链接携带的参数为准
			query.action = query.action || 'direct'; // action要以链接携带的参数为准
			query.school = query.school || '0'; // school要以链接携带的参数为准

			const inStore = loadOrderInfo();
			Object.assign(this.orderInfo, inStore, query);

			this.orderInfo.identifier = 'TK24Upgrade';
			this.orderInfo.productKey = TraceLogInfoKeys.tk_jj_cn_medical_health_high_cube_base;
			this.orderInfo.checked = true;

			this.decodeUserInfo();

            this.policyObj.belongs = getPolicyVersion(this.orderInfo[`idCard${this.orderInfo.relation}`]);

			saveOrderInfo(1, 'TK24_Upgrade');
		},

		decodeUserInfo() {
			const param = url_safe_b64_decode(this.orderInfo.param);
			if (!param) return;

			const obj = JSON.parse(param);
			Object.assign(this.orderInfo, obj);
		},

		onSubmit() {
			this.onViewPolicy('健康告知', true);
		},

		pushToResult() {
			this._actionTracking('点击立即升级按钮');

			setTimeout(() => {
				const href = window.location.href.replace('/Upgrade', '/Result');
				window.location.href = href;
			}, 250);
		},

		onAcceptPolicy() {
			this.orderInfo.checked = true;

			this.pushToResult(true);
		},

		onViewPolicy(name, isPolicy,) {
			this.policyObj[isPolicy ? 'v1' : 'v'] = true;
			this.policyObj[isPolicy ? 'page1' : 'page'] = name;
		},

		_entryReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('升级页', domContentLoadedEventEnd - fetchStart);
		},

		_actionTracking(name, time = 0) {
			eventTracking(this.orderInfo, name, time);
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302211530 {
		padding-bottom: 0.75rem;
		font-size: 0.16rem;
		width: 3.75rem;
		min-height: 100%;
		background-color: #ffffff;

		img {
			display: block;
			max-width: 100%;
		}

		.content {
			margin: 0 0.1rem;

			.premium {
				margin: 0.15rem auto 0;
				color: #666666;
				font-size: 0.12rem;
				text-align: center;
				line-height: 1.5;
			}

			.remainder {
				margin: 0.15rem auto 0.1rem;
				color: #333333;
				font-weight: 500;
				font-size: 0.16rem;
				text-align: center;

				.remainder-count {
					margin: 0 0.05rem;
					font-size: 0.24rem;
					font-weight: 700;
					color: #ff491d;
				}
			}

			.process {
				height: 0.12rem;
				background: #fdeecc;
				border-radius: 0.06rem;
				width: 2.8rem;
				margin: 0.1rem auto;

				.process-value {
					height: 0.12rem;
					border-radius: 0.06rem;
					background: linear-gradient(90deg, #fe660f, #ff9829);
				}
			}

			.submit-button {
				position: relative;
				margin: 0.15rem auto;
				padding: 0.15rem 0;
				width: 3rem;
				border-radius: 999px;
				box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
				background: linear-gradient(
					270deg,
					rgb(255, 16, 46),
					rgb(253, 123, 69)
				);
				animation: button_animate 1.35s linear infinite;

				font-size: 0.2rem;
				color: #ffffff;
				font-weight: 700;
				text-align: center;

				.text {
					font-size: 0.16rem;
					font-weight: 500;
				}

				.hand {
					position: absolute;
					top: 0.25rem;
					left: 75%;
					width: 18%;
					animation: hand_animate 1s linear infinite;
				}
			}

			.policy-box {
				padding: 0.08rem;
				background-color: #ffffff;
				font-size: 0.13rem;
				color: #333333;
				line-height: 0.2rem;
				text-align: justify;

				.read {
					color: #ff8c41;
					font-weight: 500;
				}
			}
		}

		.bottomButton {
			position: fixed;
			inset: auto 0 0.25rem 0;
			margin: 0 auto;
			padding: 0.15rem 0;
			width: 3rem;
			font-size: 0.2rem;
			color: #ffffff;
			font-weight: 700;
			text-align: center;
			border-radius: 999px;
			box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
			background: linear-gradient(
				270deg,
				rgb(255, 16, 46),
				rgb(253, 123, 69)
			);
			animation: button_animate 1.35s linear infinite;

			.hand {
				position: absolute;
				top: 0.25rem;
				left: 75%;
				width: 18%;
				animation: hand_animate 1s linear infinite;
			}

			.text {
				font-size: 0.16rem;
				font-weight: 500;
			}
		}

		@keyframes button_animate {
			0% {
				transform: scale(1);
			}
			40% {
				transform: scale(1);
			}
			70% {
				transform: scale(0.95);
			}
			100% {
				transform: scale(1);
			}
		}

		@keyframes hand_animate {
			0% {
				transform: translate(-0.1rem, -0.1rem);
			}
			45% {
				transform: translate(0.1rem, 0);
			}
			70% {
				transform: translate(0.1rem, 0);
			}
			100% {
				transform: translate(-0.1rem, -0.1rem);
			}
		}
	}
</style>


