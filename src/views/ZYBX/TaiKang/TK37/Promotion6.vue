<template>
    <div class="container_2306150950">
        <div class="content">
            <div class="top" :class="{ top_cancelled: isCancelled }" @click="onSubmit">{{ isCancelled ? '您的保障已失效' :
                '您的保障即将过期，请尽快交费 >' }}</div>
            <div class="header" :class="{ header_cancelled: isCancelled }">
                <p class="header-product">泰超能·百万医疗险</p>
                <p class="header-policy" :data-clipboard-text="orderInfo.policyNo">保单号：{{ orderInfo.policyNo }}</p>
                <p class="header-company">本产品由泰康在线财产保险股份有限公司承保</p>
            </div>
            <div v-for="obj in sectionObj" :key="obj.title" class="section">
                <div class="section-title">
                    <span v-if="obj.title" class="section-title-main">{{ obj.title }}</span>
                    <span v-if="obj.title1" class="section-title-color">{{ obj.title1 }}</span>
                    <span v-if="obj.title2" class="section-title-extra" @click="viewAction(obj.title, false)">{{
                        obj.title2 }}</span>
                </div>
                <div v-for="item in obj.content" :key="item.key" class="section-item">
                    <div class="section-item-key">
                        {{ item.key }}
                        <span class="section-item-extra">{{ item.extra }}</span>
                    </div>
                    <div class="section-item-value">{{ item.value }}</div>
                </div>
            </div>
            <van-collapse v-model="collapseName" accordion>
                <van-collapse-item name="理赔指引">
                    <template #title>
                        <div class="collapse-header">
                            <span>理赔指引</span>
                        </div>
                    </template>
                    <div class="collapse-content">
                        您可通过【泰康在线保险】微信公众号→“我的服务”→“我要理赔”或【泰康在线】APP→“我的”→“理赔”→“我要理赔”自助理赔并查看理赔进度，或致电泰康在线（95522-3）；院外特药购药指引关注微信公众号【泰康在线保险】→公众号主界面右下角点击“我的服务”→“我要理赔”→“院外特药”→“申请购药服务”，根据页面提示填写用药申请信息并上传材料。
                    </div>
                </van-collapse-item>
            </van-collapse>
            <van-collapse v-model="collapseName" accordion>
                <van-collapse-item name="缴费信息">
                    <template #title>
                        <div class="collapse-header">
                            <span>缴费信息</span>
                        </div>
                    </template>
                    <div class="collapse-content">
                        <table>
                            <thead>
                                <tr>
                                    <th>交费期数</th>
                                    <th>应付日期</th>
                                    <th>实付日期</th>
                                    <th>支付金额</th>
                                </tr>
                            </thead>
                            <tfoot>
                                <tr v-for="v in orderInfo.renewInfo" :key="v.period">
                                    <td>第{{ v.period }}期</td>
                                    <td>{{ v.expectPayDate }}</td>
                                    <td>{{ v.payDate }}</td>
                                    <td>¥{{ v.premium }}</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </van-collapse-item>
            </van-collapse>
            <div class="policy-box">
                请仔细阅读
                <span class="view-txt" @click.stop="viewAction('投保须知', true)">《投保须知》</span>
                <span class="view-txt" @click.stop="viewAction('责任免除', true)">《责任免除》</span>
                <span class="view-txt" @click.stop="viewAction('特别约定', true)">《特别约定》</span>
                <span class="view-txt" @click.stop="viewAction('产品说明', true)">《产品说明》</span>
                <span class="view-txt" @click.stop="viewAction('保险条款', true)">《保险条款》</span>
                <span class="view-txt" @click.stop="viewAction('泰康在线高危职业表', true)">《泰康在线高危职业表》</span>
                <span class="view-txt" @click.stop="viewAction('费率表', false)">《费率表》</span>
            </div>
        </div>
        <div class="bottom-wrapper" @click="onSubmit">
            <p class="button" :class="{ cancelled: isCancelled }">
                {{ buttonTitle }}
                <span v-if="!isCancelled" class="button-small">(第{{ orderInfo.nextPeriod }}期)</span>
            </p>
        </div>
        <HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
        <HHTabViewer :obj="policyObj" @ok="onSubmit"></HHTabViewer>
        <van-popup v-model="isMask" class="mask" :close-on-click-overlay="false">
            <img src="@/assets/imgs/common/icon_browser.png" />
        </van-popup>
    </div>
</template>

<script>
import moment from 'moment';
import ClipboardJS from "clipboard";
import HHTabViewer from "./components/HHTabViewer";
import HHPolicyViewer from "./components/HHPolicyViewer";
import { domainPathMap, } from "@/views/ZYBX/src";
import { calculatePremium, eventTracking, showToast, } from "./function";
import { fetchOrderInfoByPolicyNo, fetchRenewInfoByPolicyNo, renewTKCubePolicy } from "@/api/insurance-api";
import { TraceLogInfoKeys, isInWx, isAndroid, url_safe_b64_encode, Url, } from "@/assets/js/common";

export default {
    name: "Promotion3",
    components: { HHTabViewer, HHPolicyViewer },
    data() {
        const orderInfo = {
            source: 'promote',
            action: 'promote',
            productKey: TraceLogInfoKeys.iyb_tk_rr_cn_by_jd_cube_base,
            identifier: 'TK37Promotion6',
            insurance: 1,   // 有无社保
            repay: 1,       // 缴费方式
            channel: '1',
            mTel: '',       // 加密手机号
            infoNo: '',     // 订单号
            policyNo: '',
            premium: 0,
            holderName: '',
            holderIdCard: '',
            holderPhone: '',
            insuredName: '',
            insuredIdCard: '',
            effectDate: '',
            expireDate: '',
            autoRenew: '0',
            code: 1, // code=0 保单号不存在，无续期数据;code=1 保单正常，返回续期数据;code=2 用户退保，返回续期数据
            nextPeriod: 1,
            renewInfo: [],
        };

        return {
            collapseName: '',
            isMask: false,
            orderInfo: orderInfo,
            policyObj: { v: false, page: '', v1: false, page1: '', belongs: 'v2' },
        }
    },
    computed: {
        isCancelled() {
            return this.orderInfo.code == 2;
        },
        premium() {
            return (this.orderInfo.premium).toFixed(2);
        },
        buttonTitle() {
            return this.isCancelled ? '保障已失效' : this.orderInfo.autoRenew == 1 ? '开通自动续期' : '支付保费'
        },
        sectionObj() {
            return [
                {
                    title: '保障详情',
                    title2: '查看详情',
                    content: [
                        { key: '姓名', value: this.orderInfo.insuredName },
                        { key: '身份证号', value: this.orderInfo.insuredIdCard },
                        { key: '保障期限', value: this.orderInfo.effectDate + '至' + this.orderInfo.expireDate },
                    ],
                },
                {
                    title1: '升级保障',
                    content: [
                        { key: '一般医疗保险金', value: '300万(1万免赔)' },
                        { key: '重大疾病医疗保险金', value: '600万(0免赔)' },
                        { key: '质子重离子医疗保险金', value: '600万(0免赔)' },
                        { key: '指定疾病扩展特需医疗保险金', value: '600万(0免赔)' },
                        { key: '重大疾病异地转诊保险金', value: '20万(0免赔)' },
                        { key: '附加个人住院补偿医用医疗保险金', value: '3000(0免赔)' },
                        { key: '附加扩展门(急)诊医疗保险金', value: '1万(0免赔)' },
                    ],
                },
                {
                    title1: '基础保障',
                    content: [
                        { key: '一般医疗保险金', value: '300万(4万免赔)' },
                        { key: '重大疾病医疗保险金', value: '600万(4万免赔)' },
                        { key: '齿科服务：家庭健齿套餐', value: '免费赠送' },
                    ],
                },
            ];
        },
    },

    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.init();

        const clipboard = new ClipboardJS('.header-policy');
        clipboard.on('success', (e) => {
            // console.info('Action:', e.action);
            // console.info('Text:', e.text);
            // console.info('Trigger:', e.trigger);
            //注销对象
            e.clearSelection();
            this.$toast({ message: '保单号已复制', forbidClick: true, duration: 2000 });
        });

        clipboard.on('error', (e) => {
            // console.error('Action:', e.action);
            // console.error('Trigger:', e.trigger);
            this.$toast({ message: '长按保单号进行复制', forbidClick: true, duration: 2000 });
        });
    },
    methods: {
        //初始化
        init() {
            const inQry = this.$route.query || {};
            const { channel, policyNo, autoRenew } = inQry;
            this.orderInfo.channel = channel || '1';
            this.orderInfo.autoRenew = autoRenew || '0';

            if (isInWx() && (inQry.isMask == 1)) {
                if (isAndroid()) {
                    const url = url_safe_b64_encode(window.location.href);
                    setTimeout(() => {
                        window.location.href = `${Url}/Insurance/info/checkIsJump?url=${url}`;
                    }, 500);
                }
                return this.isMask = true;
            }

            this.fetchOrderInfo(policyNo);
        },

        fetchOrderInfo(policyNo) {
            if (!policyNo) {
                this._contentHomeView();
                return this.editOrderInfo();
            }

            this.$toast.loading({
                message: '正在请求数据\n请稍候',
                forbidClick: true,
                duration: 0,
            });

            fetchOrderInfoByPolicyNo(policyNo).then(res => {
                const data = res.data || {};
                this.orderInfoHandle(data);
                this.fetchRenewData(policyNo);
            }).catch(err => {
                this.editOrderInfo();
            }).finally(() => {
                this._contentHomeView();
            });
        },

        fetchRenewData(policyNo) {
            fetchRenewInfoByPolicyNo(policyNo).then(res => {
                this.$toast.clear(true);

                const { nextPeriod, renewInfo, code, msg } = res.data || {};
                if (nextPeriod > 12 || code == 0) {
                    return this.editOrderInfo(msg);
                }

                this.orderInfo.code = code;
                this.orderInfo.nextPeriod = nextPeriod;
                this.orderInfo.renewInfo = renewInfo;

                if (code == 2) return;

                const prevItem = renewInfo[renewInfo.length - 1];
                if (prevItem) {
                    const date = moment(prevItem.expectPayDate).add(1, 'months').format('YYYY-MM-DD');
                    const obj = { period: nextPeriod, expectPayDate: date, payDate: '未支付', premium: 0 };
                    if (prevItem.premium > 20) {
                        obj.premium = prevItem.premium;
                    } else {
                        obj.premium = this.orderInfo.premium;
                    }

                    renewInfo.push(obj);
                }
            }).catch(err => {
                this.editOrderInfo();
            });
        },

        orderInfoHandle(data) {
            Object.assign(this.orderInfo, data);

            const { operatorPhone, insuredIdCard, insurance, repay, effectDate } = this.orderInfo;
            this.orderInfo.mTel = operatorPhone;
            this.orderInfo.effectDate = moment(effectDate).format('YYYY.MM.DD');
            this.orderInfo.expireDate = moment(effectDate).add(1, 'years').subtract(1, 'days').format('YYYY.MM.DD');

            const birthday = insuredIdCard.substr(6, 8);
            const days = moment().diff(moment(effectDate), 'days');
            const birthday1 = moment(birthday).add(days, 'days').format('YYYYMMDD');
            const certNo = insuredIdCard.replace(birthday, birthday1);
            this.orderInfo.premium = calculatePremium(certNo, insurance, repay).high;
        },

        viewAction(name, isPolicy) {
            this.policyObj[isPolicy ? 'v1' : 'v'] = true;
            this.policyObj[isPolicy ? 'page1' : 'page'] = name;
        },

        editOrderInfo(msg) {
            showToast(msg || '该订单无效，正在跳转到首页');

            const { channel, mTel } = this.orderInfo;
            const params = { channel, mTel, action: 'promotion', source: 'promotion' }
            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap['TK37Index1'] + `?bizParams=${bizParams}`;

            setTimeout(() => {
                return window.location.href = href;
            }, 2000);
        },

        onSubmit() {
            if (this.isCancelled) {
                return this.$toast({ message: '保障已失效，无法再续费' });
            }
            const { policyNo, channel, infoNo, mTel } = this.orderInfo;

            this._actionTracking('点击促活立即续费按钮');

            this.$toast.loading({
                message: '订单处理中...',
                forbidClick: true,
                duration: 0,
            });

            const failUrl = window.location.href.split('?')[0] + `?policyNo=${policyNo}&channel=${channel}`;
            const successUrl = window.location.href.replace(/\/Promotion.*/, '/Result') + `?infoNo=${infoNo}&channel=${channel}&mTel=${mTel}&promote=promotion6`;

            const params = { policyNo, channelId: channel, failUrl, successUrl, platform: isInWx() ? 'WX' : 'WAP', };
            renewTKCubePolicy(params).then(r => {
                const { result, payUrl, message } = r.data;
                if (result != 1 || !payUrl) {
                    let msg = '';
                    if (message.indexOf('自动扣费正在进行中') >= 0) {
                        msg = '系统正在扣款中，暂无需主动支付保费';
                    } else if (message.indexOf('投保单支付中') >= 0) {
                        msg = '投保订单支付中，请勿重复支付';
                    }

                    if (msg) {
                        return this.$toast({
                            message: msg,
                            forbidClick: true,
                            duration: 6000,
                        });
                    }
                    return this.editOrderInfo();
                }

                this.$toast.clear(true);
                setTimeout(() => {
                    window.location.href = payUrl;
                }, 250);
            }).catch(err => {
                this.editOrderInfo();
            });
        },

        _contentHomeView() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            this._actionTracking('续费促活页', domContentLoadedEventEnd - fetchStart);
        },

        _actionTracking(name, time = 0) {
            eventTracking(this.orderInfo, name, time);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2306150950 {
    padding-bottom: 0.6rem;
    width: 3.75rem;
    font-size: 0.15rem;
    background-color: #f2f2f2;

    .cancelled {
        filter: grayscale(100%);
    }

    img {
        display: block;
        max-width: 100%;
    }

    .mask {
        width: 100%;
        height: 100%;
        background-color: unset;

        img {
            display: block;
            width: 3.5rem;
            margin: 0 auto;
        }
    }

    .content {
        .top {
            padding: 0.1rem;
            color: #ff5050;
            text-align: center;
            font-weight: 500;
            background-color: #fff3f2;
        }

        .top_cancelled {
            color: #999999;
        }

        .header {
            padding: 0.15rem 0 0.15rem 0.15rem;
            color: #ffffff;
            font-size: 0.13rem;
            font-weight: 500;
            background: url("~@/assets/imgs/common/icon_working.png") no-repeat right 0.1rem center/0.6rem,
                linear-gradient(-50deg, #ff8539, #ff954b);

            .header-product {
                font-size: 0.19rem;
            }

            .header-policy {
                margin: 0.1rem 0;
            }

            .header-company {
                color: #ffffff99;
            }
        }

        .header_cancelled {
            background: url("~@/assets/imgs/common/icon_expired.png") no-repeat right 0.1rem center/0.6rem,
                linear-gradient(-50deg, #ff8539, #ff954b);
        }

        .collapse-header {
            color: #000000;
            font-size: 0.17rem;
            font-weight: 700;
        }

        .collapse-content {
            font-size: 0.13rem;
            text-align: justify;
            color: #333333;

            table {
                width: 100%;
                border-collapse: collapse; //合并表格边框
                border-spacing: 0; //表格边框间距
                border: 1px solid #eeeeee;
            }

            table tr td,
            table tr th {
                height: 0.35rem;
                text-align: center;
                border: 1px solid #eeeeee;
            }

            table tr th {
                background-color: #f8f8f8;
            }
        }

        .section {
            margin: 0.1rem 0;
            padding: 0.15rem 0.15rem 0.1rem;
            background-color: #ffffff;

            .section-title {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding-bottom: 0.1rem;

                .section-title-main {
                    font-size: 0.17rem;
                    font-weight: 700;
                }

                .section-title-color {
                    padding: 0.08rem;
                    color: #ffffff;
                    font-size: 0.12rem;
                    border-radius: 0 0.15rem 0.15rem 0;
                    background: linear-gradient(90deg, #ffa402, #ff6701);
                }

                .section-title-extra {
                    color: #057aff;
                }
            }

            .section-item {
                padding: 0.05rem 0;
                display: flex;
                align-items: center;
                justify-content: space-between;

                color: #333333;
                font-size: 0.14rem;
                line-height: 1.5;
                text-align: justify;

                .section-item-extra {
                    font-size: 0.12rem;
                    color: #999999;
                }

                .section-item-value {
                    margin-left: 0.1rem;
                    flex-shrink: 0;
                }
            }
        }

        .policy-box {
            margin: 0.2rem 0.1rem;
            font-size: 0.13rem;
            color: #333333;
            line-height: 1.6;
            text-align: justify;

            .view-txt {
                color: #057aff;
                font-weight: 500;
            }
        }
    }

    .bottom-wrapper {
        position: fixed;
        inset: auto 0 0 0;
        margin: 0 auto;
        height: 0.6rem;
        width: 3.75rem;
        background-color: #ffffff;

        display: flex;
        align-items: center;
        justify-content: center;

        .button {
            color: #ffffff;
            padding: 0.15rem;
            width: 1.8rem;
            font-size: 0.16rem;
            font-weight: 700;
            text-align: center;
            border-radius: 0.23rem;
            background: linear-gradient(90deg, #fc0, #ff9500);

            .button-small {
                font-size: 0.12rem;
            }
        }
    }
}
</style>
