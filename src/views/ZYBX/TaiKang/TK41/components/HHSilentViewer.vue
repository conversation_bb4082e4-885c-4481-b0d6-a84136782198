<template>
    <van-popup v-model="obj.v2" class="container_2212271130" position="bottom" round>
        <div class="header">
            <img class="header-img" src="@/assets/imgs/TaiKang/TK41/img03.png">
            <div class="header-premium">
                <p>变更保障后每月保费由<span>{{ premiumObj.first }}</span>元变更为<span>{{ premiumObj.next }}</span>元，自次月开始扣费并生效</p>
                <p>变更后的保障责任、赔付比例、免赔额详见<span class="view-txt" @click.stop="onViewPolicy('产品说明及投保须知', true)">《产品说明及投保须知》</span>
                </p>
            </div>
        </div>
        <div id="id_2212271130" class="content">
            <Policy_Health></Policy_Health>
        </div>
        <div class="footer">
            <div class="footer-text">
                我同意次月变更保障
                <span class="view-txt" @click.stop="onViewPolicy('产品说明及投保须知', true)">《产品说明及投保须知》</span>
                <span class="view-txt" @click.stop="onViewPolicy('保险条款', true)">《保险条款》</span>
                <span class="view-txt" @click.stop="onViewPolicy('健康告知', true)">《健康告知》</span>
                <span class="view-txt" @click.stop="onViewPolicy('责任免除', true)">《责任免除》</span>
                <span class="view-txt" @click.stop="onViewPolicy('特别约定', true)">《特别约定》</span>
                <span class="view-txt" @click.stop="onViewPolicy('投保规则', true)">《投保规则》</span>
                <!-- <span class="view-txt" @click.stop="onViewPolicy('客户预授权投保服务合同书', true)">《客户预授权投保服务合同书》</span> -->
                <span class="view-txt" @click.stop="onViewPolicy('泰康在线授权文件', true)">《泰康在线授权文件》</span>
                <span class="view-txt" @click.stop="onViewPolicy('隐私政策', true)">《隐私政策》</span>
                <span class="view-txt" @click.stop="onViewPolicy('费率表', false)">《费率表》</span>。
            </div>
            <div class="button-container">
                <div class="button" @click="onclick(false)">
                    符合健告同意投保基础版
                </div>
                <div class="button color" @click="onclick(true)">
                    符合健告同意投保升级版
                </div>
            </div>
        </div>
    </van-popup>
</template>

<script>
import Policy_Health from "./Policy/Policy_Health";

export default {
    name: "HHSilentViewer",
    components: { Policy_Health, },
    props: { obj: Object, obj1: Object },
    watch: {
        'obj.v2': {
            handler() {
                this.scrollToTop();
            },
        },
    },
    computed: {
        premiumObj() {
            const { firstPremium, nextPremium } = this.obj1;
            return { first: firstPremium, next: nextPremium };
        },
    },
    methods: {
        onclick(ret) {
            this.obj.v2 = false;
            this.$emit('click', ret);
        },
        onViewPolicy(name, isPolicy) {
            this.obj.belongs = 'v2';
            this.obj[isPolicy ? 'v1' : 'v'] = true;
            this.obj[isPolicy ? 'page1' : 'page'] = name;
        },
        scrollToTop() {
            const node = document.getElementById('id_2212271130');
            node && (node.scrollTop = 0);
        }
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2212271130 {
    height: 100%;
    width: 3.75rem;
    inset: auto 0 0 0;
    margin: 0 auto;
    background-color: unset;

    display: flex;
    flex-direction: column;

    .view-txt {
        color: #ff8c41;
        font-weight: 500;
    }

    .header {

        .header-img {
            margin: 0 auto;
            display: block;
            width: 100%;
        }

        .header-premium {
            font-size: 0.12rem;
            color: #333333;
            text-align: center;
            line-height: 0.2rem;
            background-color: #ffffff;
        }
    }

    .content {
        flex: 1;
        overflow: auto;
        background-color: #ffffff;
    }

    .footer {
        background-color: #ffffff;
        border-top: #f2f2f2 1px solid;

        .footer-text {
            padding: 0.05rem 0.1rem 0;
            font-size: 0.13rem;
            line-height: 1.5;
            text-align: justify;
        }

        .button-container {
            display: flex;
            justify-content: space-around;

            .button {
                margin: 0.05rem 0 0.10rem;
                padding: 0.15rem 0.10rem;
                border-radius: 999px;
                font-size: 0.14rem;
                font-weight: 500;
                text-align: center;
                color: #666666;
                border: #eeeeee 1px solid;
                background-color: #ffffff;
            }

            .color {
                color: #ffffff;
                background: linear-gradient(to right, #ffcc00, #ff9500);
            }
        }
    }
}
</style>
