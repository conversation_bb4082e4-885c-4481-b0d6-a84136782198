<template>
	<div id="id_input_x" class="container_2209011100">
		<div id="被保人信息" class="wrapper">
			<div class="header">
				<span class="header-title">为谁投保（被保人）</span>
			</div>
			<div class="flex-rows" style="margin-top: 0.15rem">
				<HHOption v-for="item in relations" :key="item.key" :obj="{text:item.key,active:item.value == obj.relation,width:'0.70rem'}" @click="onRelationChange(item.value)"></HHOption>
			</div>
			<div class="input-x">
				<div class="input-item">
					<span class="input-item-label">被保人姓名</span>
					<input v-model="obj['name' + obj.relation]" class="input-item-input" maxlength="20" placeholder="请输入被保人姓名" type="text" @input="onTextInput('certName2',$event.target.value)">
				</div>
				<div class="input-item">
					<span class="input-item-label" @click="onIdCardTypeChange(obj.relation)">{{ icLabelInsured }}<van-icon name="arrow-down" class="input-item-arrow" /></span>
					<input v-model="obj['idCard' + obj.relation]" class="cert_no_x input-item-input" maxlength="18" placeholder="信息保密，仅用于投保" type="text" @input="onTextInput('certNo2',$event.target.value)">
				</div>
				<div v-if="isOwn" class="input-item">
					<span class="input-item-label">手机号码</span>
					<input v-model="obj.phoneNo" :readonly="isReadOnly" class="input-item-input" maxlength="11" onkeyup="value=value.replace(/\D/g,'')" placeholder="信息保密，仅用于投保" type="tel" @input="onTextInput('phone',$event.target.value)">
				</div>
				<div class="has-x">
					<div class="has-x-label">
						有无医保
						<van-icon name="question-o" @click="onViewExplain('医保')" />
					</div>
					<div class="flex-rows">
						<HHOption v-for="item in insurances" :key="item.key" :obj="{text:item.key,active:item.value == obj.insurance,width:'1.30rem'}" @click="OnInsuranceChange(item.value)"></HHOption>
					</div>
				</div>
                <div class="has-x">
					<div class="has-x-label">
						开通下一年自主重新投保
						<van-icon name="question-o" @click="onViewExplain('续保')" />
					</div>
                    <div class="flex-rows">
						<HHOption :obj="{text:'同意开通',active:obj.autoRenewalType == 1,width:'1.30rem'}" @click="onRenewalChange(1)"></HHOption>
                        <HHOption :obj="{text:'暂不开通',active:obj.autoRenewalType == 0,width:'1.30rem'}" @click="onRenewalChange(0)"></HHOption>
					</div>
				</div>
			</div>
		</div>
		<div v-if="!isOwn" class="wrapper">
			<div class="header">
				<span class="header-title">投保人信息</span>
			</div>
			<div id="投保人信息" class="input-x">
				<div class="input-item">
					<span class="input-item-label">投保人姓名</span>
					<input v-model="obj.name1" class="input-item-input" maxlength="20" placeholder="请输入您的姓名" type="text" @input="onTextInput('certName1',$event.target.value)">
				</div>
				<div class="input-item">
					<span class="input-item-label" @click="onIdCardTypeChange(1)">{{ icLabelHolder }}<van-icon name="arrow-down" /></span>
					<input v-model="obj.idCard1" class="cert_no_x input-item-input" maxlength="18" placeholder="信息保密，仅用于投保" type="text" @input="onTextInput('certNo1',$event.target.value)">
				</div>
				<div class="input-item">
					<span class="input-item-label">手机号码</span>
					<input v-model="obj.phoneNo" :readonly="isReadOnly" class="input-item-input" maxlength="11" onkeyup="value=value.replace(/\D/g,'')" placeholder="信息保密，仅用于投保" type="tel" @input="onTextInput('phone',$event.target.value)">
				</div>
			</div>
		</div>
		<div id="id_action_button" class="submit-x" @click="submitAction">
			立即投保<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
		</div>
		<div class="premium-x">
			<span class="number">{{ premiumObj.month }}</span>
			元{{ premiumObj.suffix }}/月
			<span class="small-txt">依据《费率表》</span>
		</div>
		<div class="policy-x">
			<div>
				<van-icon class="policy-icon" :name="obj.checked ?'checked':'circle'" @click="obj.checked = !obj.checked" />
				我已阅读并同意
				<span class="view-txt" @click.stop="viewAction('产品说明及投保须知',true)">《产品说明及投保须知》</span>
				<span class="view-txt" @click.stop="viewAction('保险条款',true)">《保险条款》</span>
				<span class="view-txt" @click.stop="viewAction('责任免除',true)">《责任免除》</span>
				<span class="view-txt" @click.stop="viewAction('健康告知',true)">《健康告知》</span>
				<span class="view-txt" @click.stop="viewAction('特别约定',true)">《特别约定》</span>
                <span class="view-txt" @click.stop="viewAction('投保规则',true)">《投保规则》</span>
				<!-- <span class="view-txt" @click.stop="viewAction('客户预授权投保服务合同书',true)">《客户预授权投保服务合同书》</span> -->
                <span class="view-txt" @click.stop="viewAction('泰康在线授权文件',true)">《泰康在线授权文件》</span>
				<span class="view-txt" @click.stop="viewAction('隐私政策',true)">《隐私政策》</span>
                <br>保费与年龄和有无社保相关,此保费以1-55周岁为例 详见
				<span class="view-txt" @click.stop="viewAction('费率表',false)">《费率表》</span>。
			</div>
			<p class="record-txt">您已进入投保流程，请仔细阅读免责说明书、产品说明及投保须知、重要提示等信息，为维护您的合法权益，您的操作轨迹将被记录。</p>
		</div>
		<HHExplainViewer :obj="viewObj"></HHExplainViewer>
        <HHICTypeViewer :obj="icTypeObj" @click="onIdCardTypeFinish"></HHICTypeViewer>
	</div>
</template>

<script>

import { insurances, relations } from "../src";
import HHOption from "./HHOption";
import HHExplainViewer from "./HHExplainViewer";
import HHICTypeViewer from "./HHICTypeViewer";
import { calculatePremium, } from "../function";
import { isCardNo, isMaskedAndT1Phone, isPhoneNum, secureIdCard } from "@/assets/js/common";

export default {
	name: "HHInputBox",
	components: { HHExplainViewer, HHOption, HHICTypeViewer },
	props: { obj: Object, },
	data() {
		return {
			relations,
			insurances,
			viewObj: { v: false, page: '' },
            icTypeObj: { v: false, relation: 1, type: '01' },
		}
	},
	computed: {
		isOwn() {
			return this.obj.relation == 1;
		},
		isReadOnly() {
			const { starPhone, mTel, phoneNo, smsCode } = this.obj;
			return isMaskedAndT1Phone(starPhone, mTel) || (isPhoneNum(phoneNo) && smsCode == 8888);
		},
        icLabelHolder() {
			return this.obj.idCardType1 == '01' ? '身份证号' : '永居证号';
		},
		icLabelInsured() {
			return this.obj[`idCardType${this.obj.relation}`] == '01' ? '身份证号' : '永居证号';
		},
		premiumObj() {
			const { firstPremium } = this.obj;
			const obj = { month: '0.70', day: '0', suffix: '' };
			if (firstPremium) {
				obj.month = firstPremium;
				obj.day = (+firstPremium / 30.0).toFixed(2);
				obj.suffix = '';
			}
			return obj;
		},
	},
	mounted() {
		this.premiumCalculate();

        const idCardDeal = () => {
            requestAnimationFrame(() => {
                const inputList = document.querySelectorAll('.cert_no_x');
                for (const input of inputList) {
                    if (input.value.includes('*') && isCardNo(input.value)) {
                        console.log(secureIdCard(input.value))
                        input.value = secureIdCard(input.value);
                    }
                }

                idCardDeal();
            });
        }

        idCardDeal();
	},
	methods: {
		onRelationChange(value) {
			if (this.obj.relation == value) return;
			this.obj.relation = value;
			this.premiumCalculate();
		},
		OnInsuranceChange(value) {
			if (this.obj.insurance == value) return;
			this.obj.insurance = value;
			this.premiumCalculate();
		},
        onIdCardTypeChange(relation) {
			const icType = this.obj[`idCardType${relation}`];
			this.icTypeObj.type = icType;
			this.icTypeObj.relation = relation;
			this.icTypeObj.v = true;
		},
        onIdCardTypeFinish({ relation, type }) {
			this.obj[`idCardType${relation}`] = type;
			this.premiumCalculate();
		},
        onRenewalChange(value) {
			this.obj.autoRenewalType = value;
		},
		premiumCalculate() {
			const { relation, insurance, repay } = this.obj;
			const idCardNo = this.obj[`idCard${relation}`];
			const { first, next } = calculatePremium(idCardNo, insurance, repay);
			this.obj.firstPremium = first;
			this.obj.nextPremium = next;
		},
		onViewExplain(page) {
			this.viewObj.v = true;
			this.viewObj.page = page;
		},
		onTextInput(key, value) {
			let isFinished = false;
			if (key.includes('certNo')) {
				isFinished = isCardNo(value);
				this.premiumCalculate();
			} else if (key.includes('phone')) {
				isFinished = isPhoneNum(value);
			}

            if (key == 'certName1' || key == 'certNo1') {
                const { name1, idCard1 } = this.obj;
                if (name1.includes('*')) {
                    this.obj.name1 = '';
                }
                if (idCard1.includes('*')) {
                    this.obj.idCard1 = '';
                }
            }

            if (key == 'certName2' || key == 'certNo2') {
                const { relation } = this.obj;
                const name2 = this.obj[`name${relation}`];
                const idCard2 = this.obj[`idCard${relation}`];
                if (name2.includes('*')) {
                    this.obj[`name${relation}`] = '';
                }
                if (idCard2.includes('*')) {
                    this.obj[`idCard${relation}`] = '';
                }
                this.premiumCalculate();
            }

			if (!isFinished) return;

			this.$emit('input');
		},

		submitAction() {
			this.$emit('submit');
		},

		viewAction(name, isPolicy) {
			this.$emit('view', { name, isPolicy });
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2209011100 {
		overflow: hidden;
		font-size: 0.15rem;
		border-radius: 0.1rem;
		background-color: #ffffff;

		.flex-rows {
			display: flex;
			align-items: center;
			justify-content: space-evenly;
		}

		.wrapper {
			.header {
				padding: 0.2rem 0 0 0.15rem;

				.header-title {
					position: relative;
					font-size: 0.16rem;
					font-weight: bold;
					color: #1a1a1a;

					&::before {
						position: absolute;
						content: " ";
						width: 100%;
						height: 0.16rem;
						opacity: 0.1;
						background: #f60;
						border-radius: 0.04rem;
						left: -0.02rem;
						bottom: -0.02rem;
					}
				}
			}

			.input-x {
				margin: 0.15rem 0.1rem 0;
				padding: 0 0.15rem;
				border-radius: 0.1rem;
				box-shadow: #dddddd 0 0 0.1rem;

				.input-item {
					display: flex;
					align-items: center;
					line-height: 0.5rem;
					color: #333333;

					.input-item-label {
						width: 1rem;
						font-weight: 500;
					}

					.input-item-input {
						flex: 1;
						border: none;
						outline: none;
						width: 2rem;
						font-size: 0.17rem;
						background-color: unset;
					}
				}

				.input-item:not(:last-child) {
					border-bottom: #f2f2f2 1px solid;
				}

				.has-x {
					padding-bottom: 0.15rem;

					.has-x-label {
						color: #333333;
						font-weight: 500;
						line-height: 0.5rem;
					}
				}

				.renewal-x {
					padding-bottom: 0.15rem;
					display: flex;
					align-items: center;
					font-weight: 500;
					color: #333333;
				}
			}
		}

		.submit-x {
			position: relative;
			margin: 0.15rem auto;
			padding: 0.15rem 0;
			width: 3rem;
			border-radius: 999px;
			box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
			background: linear-gradient(
				270deg,
				rgb(255, 16, 46),
				rgb(253, 123, 69)
			);
			animation: button_animate 1.35s linear infinite;

			font-size: 0.2rem;
			color: #ffffff;
			font-weight: 700;
			text-align: center;

			.hand {
				position: absolute;
				top: 0.25rem;
				left: 75%;
				width: 18%;
				animation: hand_animate 1s linear infinite;
			}
		}

		.premium-x {
			margin: 0.1rem 0;
			font-size: 0.15rem;
			text-align: center;
			color: #333333;

			.number {
				color: #ff4509;
				font-size: 0.18rem;
				font-weight: 500;
			}

			.small-txt {
				color: #999999;
				font-size: 0.12rem;
			}
		}

		.policy-x {
			padding: 0.08rem;
			background-color: #ffffff;
			font-size: 0.13rem;
			color: #333333;
			line-height: 1.6;
			text-align: justify;

			.policy-icon {
				color: #ff8c41;
				font-size: 0.16rem;
				vertical-align: -0.01rem;
			}

			.view-txt {
				color: #ff8c41;
				font-weight: 500;
			}

			.record-txt {
				margin-top: 0.1rem;
				color: #999999;
				font-size: 0.12rem;
			}
		}

		@keyframes button_animate {
			0% {
				transform: scale(1);
			}
			40% {
				transform: scale(1);
			}
			70% {
				transform: scale(0.95);
			}
			100% {
				transform: scale(1);
			}
		}

		@keyframes hand_animate {
			0% {
				transform: translate(-0.1rem, -0.1rem);
			}
			45% {
				transform: translate(0.1rem, 0);
			}
			70% {
				transform: translate(0.1rem, 0);
			}
			100% {
				transform: translate(-0.1rem, -0.1rem);
			}
		}
	}
</style>
