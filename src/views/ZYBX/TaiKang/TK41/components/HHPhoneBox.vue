<template>
    <div id="id_input_x" class="container_2209011100">
        <div class="header">
            <div class="header-title">填写手机号 立即投保</div>
        </div>
        <div class="input-x">
            <div class="input-item">
                <span class="input-item-label">手机号码</span>
                <input v-model="obj.phoneNo" class="input-item-input" maxlength="11"
                    onkeyup="value=value.replace(/\D/g,'')" placeholder="信息保密，仅用于投保" type="tel"
                    @input="onTextInput('phone', $event.target.value)">
            </div>
            <div class="input-item" v-if="isCodeInputShow">
                <span class="input-item-label">验证码</span>
                <input v-model="obj.smsCode" class="input-item-input" maxlength="4"
                    onkeyup="value=value.replace(/\D/g,'')" placeholder="请输入验证码" type="tel">
                <div class="input-item-btn" :class="{ 'input-item-btn-disabled': isCodeBtnDisabled }"
                    @click="onGetSmsCode">{{ isCodeBtnDisabled ? `${countDown}秒后重试` : '获取验证码' }}</div>
            </div>
        </div>
        <div id="id_action_button" class="submit-x" @click="submitAction">
            立即投保<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <div class="policy-x">
            <div>
                <van-icon class="policy-icon" :name="obj.checked ? 'checked' : 'circle'"
                    @click="obj.checked = !obj.checked" />
                我已阅读并同意
                <span class="view-txt" @click.stop="viewAction('产品说明及投保须知', true)">《产品说明及投保须知》</span>
                <span class="view-txt" @click.stop="viewAction('保险条款', true)">《保险条款》</span>
                <span class="view-txt" @click.stop="viewAction('责任免除', true)">《责任免除》</span>
                <span class="view-txt" @click.stop="viewAction('健康告知', true)">《健康告知》</span>
                <span class="view-txt" @click.stop="viewAction('特别约定', true)">《特别约定》</span>
                <span class="view-txt" @click.stop="viewAction('投保规则', true)">《投保规则》</span>
                <!-- <span class="view-txt" @click.stop="viewAction('客户预授权投保服务合同书',true)">《客户预授权投保服务合同书》</span> -->
                <span class="view-txt" @click.stop="viewAction('泰康在线授权文件', true)">《泰康在线授权文件》</span>
                <span class="view-txt" @click.stop="viewAction('隐私政策', true)">《隐私政策》</span>。
            </div>
            <p class="record-txt">您已进入投保流程，请仔细阅读免责说明书、产品说明及投保须知、重要提示等信息，为维护您的合法权益，您的操作轨迹将被记录。</p>
        </div>
    </div>
</template>

<script>

import { isPhoneNum } from "@/assets/js/common";
import { dispatchWithBitwise } from "../function";

export default {
    name: "HHPhoneBox",
    props: { obj: Object, },
    data() {
        return {
            isCodeInputShow: false,
            isCodeBtnDisabled: false,
            countDown: 0,
            timer: null,
        }
    },

    mounted() {
        this.isCodeInputShow = isPhoneNum(this.obj.phoneNo);
    },

    methods: {
        onTextInput(key, value) {
            if (key == 'phone' && isPhoneNum(value)) {
                this.isCodeInputShow = true;
            }
        },

        onGetSmsCode() {
            if (this.isCodeBtnDisabled) {
                return;
            }

            if (!isPhoneNum(this.obj.phoneNo)) {
                return dispatchWithBitwise(1, "请输入正确的手机号码");
            }

            this.isCodeBtnDisabled = true;

            this.timer && clearInterval(this.timer);
            this.countDown = 60;
            this.timer = setInterval(() => {
                this.countDown--;
                if (this.countDown <= 0) {
                    this.isCodeBtnDisabled = false;
                }
            }, 1000);
        },

        submitAction() {
            if (!isPhoneNum(this.obj.phoneNo)) {
                return dispatchWithBitwise(1, "请输入正确的手机号码");
            }

            if (this.obj.smsCode != 8888) {
                return dispatchWithBitwise(1, "请输入正确的验证码");
            }

            if (!this.obj.checked) {
                return dispatchWithBitwise(7, '用户协议未同意');
            }

            this.obj.step = "step2";

            this.$emit('click');
        },

        viewAction(name, isPolicy) {
            this.$emit('view', { name, isPolicy });
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2209011100 {
    overflow: hidden;
    font-size: 0.15rem;
    border-radius: 0.1rem;
    background-color: #ffffff;

    .header {
        padding: 0.2rem 0 0 0;

        .header-title {
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1a1a1a;
            font-size: 0.20rem;
            font-weight: bold;

            &::before,
            &::after {
                content: "";
                width: 0.30rem;
                height: 0.18rem;
                background: no-repeat center/100%;
            }

            &::before {
                margin-right: 0.1rem;
                background-image: url("~@/assets/imgs/common/arrow_down.png");
            }

            &::after {
                margin-left: 0.1rem;
                background-image: url("~@/assets/imgs/common/arrow_down.png");
            }
        }
    }

    .input-x {
        margin: 0.20rem 0.1rem 0;
        padding: 0 0.15rem;
        border-radius: 0.1rem;
        box-shadow: #dddddd 0 0 0.03rem;

        .input-item {
            display: flex;
            align-items: center;
            line-height: 0.5rem;
            color: #333333;

            .input-item-label {
                width: 0.85rem;
                font-weight: 500;
            }

            .input-item-input {
                flex: 1;
                border: none;
                outline: none;
                width: 1rem;
                font-size: 0.17rem;
                background-color: unset;
            }

            .input-item-btn {
                padding: 0.10rem 0.15rem;
                font-size: 0.13rem;
                line-height: 1;
                color: #ff4509;
                border-radius: 999px;
                border: 1px solid #ff4509;
            }

            .input-item-btn-disabled {
                color: #999999;
                border: 1px solid #999999;
            }
        }

        .input-item:not(:last-child) {
            border-bottom: #f2f2f2 1px solid;
        }
    }

    .submit-x {
        position: relative;
        margin: 0.15rem auto;
        padding: 0.15rem 0;
        width: 3rem;
        border-radius: 999px;
        box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
        background: linear-gradient(270deg,
                rgb(255, 16, 46),
                rgb(253, 123, 69));
        animation: button_animate 1.35s linear infinite;

        font-size: 0.2rem;
        color: #ffffff;
        font-weight: 700;
        text-align: center;

        .hand {
            position: absolute;
            top: 0.25rem;
            left: 75%;
            width: 18%;
            animation: hand_animate 1s linear infinite;
        }
    }

    .policy-x {
        padding: 0.08rem;
        background-color: #ffffff;
        font-size: 0.13rem;
        color: #333333;
        line-height: 1.6;
        text-align: justify;

        .policy-icon {
            color: #ff8c41;
            font-size: 0.16rem;
            vertical-align: -0.01rem;
        }

        .view-txt {
            color: #ff8c41;
            font-weight: 500;
        }

        .record-txt {
            margin-top: 0.1rem;
            color: #999999;
            font-size: 0.12rem;
        }
    }

    @keyframes button_animate {
        0% {
            transform: scale(1);
        }

        40% {
            transform: scale(1);
        }

        70% {
            transform: scale(0.95);
        }

        100% {
            transform: scale(1);
        }
    }

    @keyframes hand_animate {
        0% {
            transform: translate(-0.1rem, -0.1rem);
        }

        45% {
            transform: translate(0.1rem, 0);
        }

        70% {
            transform: translate(0.1rem, 0);
        }

        100% {
            transform: translate(-0.1rem, -0.1rem);
        }
    }
}
</style>
