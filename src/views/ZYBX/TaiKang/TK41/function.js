import moment from "moment";
import CryptoJS from "crypto-js";
import { Toast } from "vant";
import { actionTracking } from "@/assets/js/api";
import { bxStorage, } from "@/utils/store_util";
import { PremiumRate, PremiumRate1, relations } from "./src";
import { createPromotionOrder, fetchCreateTKZengXianOrder, upgradeTKJCubeProduct, createTKJPreUnderwritingOrder } from "@/api/insurance-api";
import { isCardNo, isMaskedAndT1Phone, isPersonName, isPhoneNum, TraceLogInfoKeys, url_safe_b64_encode, isInWx, GetAge } from "@/assets/js/common";

export const orderInfo = {
    checked: false,
    hintVisible: true,
    source: 'direct', // 页面来源
    action: 'direct', //  back: '返回',promotion: '促活跳转',follow: '结果跳转',direct: '直发',forward: '转发',result: '结果页',promote: '促活'
    planKey: TraceLogInfoKeys.az_tkj_bw_prevent_cancer_jd_cube_base,
    infoKey: TraceLogInfoKeys.az_tkj_bw_prevent_cancer_jd_cube_base,
    identifier: "",
    relation: 1, // 为谁投保 1:本人；2:配偶；3:儿女；4:父母；
    insurance: 1, // 有无社保
    repay: 1, // 缴费方式
    autoRenewalType: -1, // 自动续期类型
    channel: "1",
    channelCode: "",
    m: "", // 链接自带的加密手机号
    mTel: "", // 加密手机号
    infoNo: "", // 订单号
    policyNo: "", // 保单号
    firstPremium: 0,
    nextPremium: 0,
    callbackUrl: "",

    starPhone: "", // 带*手机号
    phoneNo: "", // 手机号
    name1: "", // 本人
    name2: "", // 配偶
    name3: "", // 儿女
    name4: "", // 父母
    idCard1: "",
    idCardType1: '01',
    idCard2: "",
    idCardType2: '01',
    idCard3: "",
    idCardType3: '01',
    idCard4: "",
    idCardType4: '01',
    bizParams: "",

    sourcePage: "",
    paymentCode: -1, // NSF评分
    school: "0", // 0非前置升级；1前置升级

    // 回溯相关
    fromId: '82515',
    productCode: '13C00746',
    productName: '泰康2024防癌医疗险',
    proposalNo: '',
    traceBackUuid: '',

    step: 'step1',
    smsCode: '',
    authFailObj: {}, // 实名认证失败
}

export const checkOrderParams = () => {
    const { infoKey, identifier, channel, channelCode, sourcePage, action, source, school } = orderInfo;
    const { infoNo, mTel, starPhone, name1, idCard1, idCardType1, phoneNo, relation, repay, insurance, } = orderInfo;
    const { checked, firstPremium, nextPremium, traceBackUuid, autoRenewalType, } = orderInfo;

    const page = `infoKey:${infoKey}&page:${identifier}&act:${action}&src:${source}`;
    const relation1 = relations.find(item => item.value == relation).param;

    const params = {
        infoNo,
        insurance,
        planKey: infoKey,
        page,
        channelCode,
        sourcePage,
        traceBackUuid,
        relation: relation1,
        channelId: channel,
        paymentPlan: repay,
        operatorPhone: '',
        holderName: '',
        holderIdCard: '',
        holderPhone: '',
        insuredName: '',
        insuredIdCard: '',
        insuredPhone: '',
    };

    const certName = `name${relation}`;
    const insuredName = orderInfo[certName];
    if (!isPersonName(insuredName)) {
        return { code: 1, msg: '请填写正确的被保人姓名', };
    }
    params.insuredName = insuredName;

    const certNo = `idCard${relation}`;
    const insuredIdCard = orderInfo[certNo];
    const icTypeInsured = orderInfo[`idCardType${relation}`];
    if (!isCardNo(insuredIdCard)) {
        return { code: 1, msg: '请填写正确的被保人身份证号' };
    }
    params.insuredIdCard = insuredIdCard;

    if (!isPersonName(name1)) {
        return { code: 1, msg: '请填写正确的投保人姓名' };
    }
    params.holderName = name1;

    if (!isCardNo(idCard1)) {
        return { code: 1, msg: '请填写正确的投保人身份证号' };
    }
    params.holderIdCard = idCard1;

    if (!isPhoneNum(phoneNo) && !isMaskedAndT1Phone(starPhone, mTel)) {
        return { code: 1, msg: '请填写正确的手机号码' };
    }

    const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;
    params.holderPhone = phone;
    params.insuredPhone = phone;
    params.operatorPhone = mTel || phoneNo;

    const age = GetAge(idCard1)
    const age1 = GetAge(insuredIdCard)
    if (age < 18) {
        return { code: 1, msg: '投保人年龄小于18周岁，可选择为儿女投保的方式进行投保' };
    }

    if (relation != 1) {
        if (idCard1 == insuredIdCard) {
            return { code: 1, msg: '您与被保人身份证号码不能相同' };
        }
    }

    if (relation == 2) {
        const agender = (+idCard1.slice(16, 17)) % 2;
        const agender1 = (+insuredIdCard.slice(16, 17)) % 2;
        if (age1 < 18) {
            return { code: 1, msg: '配偶年龄不能小于18周岁' }
        }
        if (agender == agender1) {
            return { code: 1, msg: '配偶双方性别不能相同' };
        }
    }

    if (relation == 3 && (age - age1 < 16)) {
        return { code: 1, msg: '您与儿女年龄至少要相差16周岁' };
    }

    if (relation == 4 && (age1 - age < 16)) {
        return { code: 1, msg: '您与父母年龄至少要相差16周岁' };
    }

    const index = window.location.href.indexOf("/ZYBX/");
    let successUrl = `${window.location.href.substring(0, index)}/ZYBX/TK41/Upgrade`;
    successUrl = `${successUrl}?tb_uuid=${traceBackUuid}`;

    const obj = {
        channel,
        school,
        mTel,
        starPhone,
        firstPremium,
        nextPremium,
        relation,
        name1,
        idCard1,
        phoneNo,
        [certNo]: insuredIdCard,
        [certName]: insuredName,
    };

    const param = url_safe_b64_encode(JSON.stringify(obj));
    successUrl = `${successUrl}&bizParams=${param}`;
    orderInfo.callbackUrl = successUrl;

    const extendParams = {
        school,
        autoRenewalType,
        successUrl,
        failUrl: window.location.href.split("?")[0],
        platformId: isInWx() ? 'WX' : 'WAP',
    };

    if (idCardType1 == '08') {
        extendParams.holderIdCardType = idCardType1;
    }

    if (icTypeInsured == '08') {
        extendParams.insuredIdCardType = icTypeInsured;
    }

    params.extendParams = extendParams;

    saveOrderInfo();

    if (!checked) {
        return { code: 2, msg: '用户协议未同意', params }; //
    }

    return { code: 0, params };
}

export const submitPreUnderwritingOrder = (params) => {
    return new Promise((resolve, reject) => {
        createTKJPreUnderwritingOrder(params).then(r => {
            const { result } = r.data || {};
            if (result == 1) {
                resolve();
            } else {
                reject({ reason: '预核保失败', msg: '预核保失败' });
            }
        }).catch(err => {
            reject({ reason: '接口出错', msg: JSON.stringify(err) });
        });
    });
}

export const submitOrderInfo = (params) => {
    const v = getFailIdentity(orderInfo);
    if (v) {
        return Promise.reject({ code: 1, msg: v });
    }

    return new Promise((resolve, reject) => {
        fetchCreateTKZengXianOrder(params).then((result) => {
            orderResultHandle(result, resolve, reject);
        }).catch((err) => {
            reject({ code: 1, msg: JSON.stringify(err) });
        });
    });
};

export const submitPromoteOrderInfo = (params) => {
    return new Promise((resolve, reject) => {
        createPromotionOrder(params).then((result) => {
            orderResultHandle(result, resolve, reject);
        }).catch((err) => {
            reject({ code: 1, msg: JSON.stringify(err) });
        });
    });
};

const orderResultHandle = (result, resolve, reject) => {
    const { code, msg, data } = result.data || {};
    if (code != 2000) {
        return reject({ code: 1, msg: msg });
    }

    const { orderNo, success, payforURL, extentMap, msg: message } = data || {};
    if (orderNo) {
        orderInfo.infoNo = orderNo;
    }

    if (extentMap && extentMap.proposalNo) {
        orderInfo.proposalNo = extentMap.proposalNo || '';
    }

    if (!success) {
        let v = message;
        if (v.indexOf('不通过') >= 0 || v.indexOf('未通过') >= 0) {
            v = '实名认证未通过，请核对修改姓名和身份证号';
            saveFailIdentity(orderInfo, v);
        }
        return reject({ code: 1, msg: v });
    }
    if (payforURL) {
        return resolve(payforURL);
    }
    return reject({ code: 1, msg: "未获取到支付链接" });
};

export const upgradeOrderInfo = (params) => {
    return new Promise((resolve, reject) => {
        upgradeTKJCubeProduct(params).then((res) => {
            const { code, msg, data } = res.data || {};
            if (code != 2000) {
                return reject({ code: 1, msg: msg });
            }
            return resolve({ code: 0 });
        }).catch(() => {
            return reject({ code: 1, msg: "接口出错" });
        });
    });
}

export const calculatePremium = (idCardNo, insurance, repay) => {
    if (!idCardNo || idCardNo.length != 18) {
        return { first: 0, next: 0 };
    }

    const birthday = idCardNo.substr(6, 8);
    const age = moment().add(0, "days").diff(moment(birthday), "year"); // 计算年龄
    const premiumObj = PremiumRate.find(
        (item) => item.min <= age && item.max >= age
    );
    const premiumObj1 = PremiumRate1.find(
        (item) => item.min <= age && item.max >= age
    );

    // 年龄超出投保范围
    if (!premiumObj) {
        return { first: 0, next: 0 };
    }

    const obj = repay == 0 ? premiumObj.year : premiumObj.month;
    const obj1 = repay == 0 ? premiumObj1.year : premiumObj1.month;

    const first = insurance == 0 ? obj.data2 : obj.data1;
    const next = insurance == 0 ? obj1.data2 : obj1.data1;

    return { first, next };
};

export const eventTracking = (name, time = 0) => {
    const { action, planKey, mTel, channel, phoneNo, identifier } = orderInfo;
    const map = {
        back: "返回",
        promotion: "促活跳转",
        follow: "结果跳转",
        direct: "直发",
        forward: "转发",
        result: "结果页",
        promote: "促活",
    };
    const prefix = map[action] ? `${map[action]}-` : "";
    const page = `${prefix}艾泽泰康健防癌险${identifier}`;
    const phone = mTel || (isPhoneNum(phoneNo) ? phoneNo : "");

    actionTracking({
        page: `${page}(${planKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: planKey,
        time: time,
    }).then((res) => {
        const { code, data } = res;
        if (code == 2000) {
            const { mobileId } = data || {};
            mobileId && (orderInfo.mTel = mobileId);
        }
    });
};

export const loadOrderInfo = (key = "TK41Info") => {
    return bxStorage.getObjItem(key) || null;
};

export const saveOrderInfo = (key = "TK41Info") => {
    bxStorage.setObjItem(key, orderInfo);
};

const showToast = (message = "", duration = 2000) => {
    Toast({
        message: message,
        duration: duration, // 弹窗时间毫秒
        position: "middle",
        forbidClick: true,
    });
};

const inputEndEditing = () => {
    const inputList = document.querySelectorAll("input");
    for (const input of inputList) {
        input.blur && input.blur();
    }
};

const adjustInputPosition = () => {
    const node = document.querySelector("#id_input_x");
    node && node.scrollIntoView && node.scrollIntoView(true);
};

export const dispatchWithBitwise = (v = 0, message = "", duration = 2000) => {
    // 1 toast(1) 2收键盘(1<<1) 4输入框位置调整(1<<2)
    if (v & 0b1) {
        showToast(message, duration);
    }

    if (v & 0b10) {
        inputEndEditing();
    }

    if (v & 0b100) {
        adjustInputPosition();
    }
};

const saveFailIdentity = (orderInfo, reason) => {
    const { relation, name1, idCard1, } = orderInfo;
    const certName = orderInfo[`name${relation}`];
    const certNo = orderInfo[`idCard${relation}`];
    const md5 = CryptoJS.MD5(`${name1}${idCard1}${certName}${certNo}`).toString();
    orderInfo.authFailObj[md5] = reason;
    console.log(md5);
}

const getFailIdentity = (orderInfo) => {
    const { relation, name1, idCard1, } = orderInfo;
    const certName = orderInfo[`name${relation}`];
    const certNo = orderInfo[`idCard${relation}`];
    const md5 = CryptoJS.MD5(`${name1}${idCard1}${certName}${certNo}`).toString();
    return orderInfo.authFailObj[md5]
}