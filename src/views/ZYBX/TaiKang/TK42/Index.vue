<template>
    <div class="container_2208310931">
    </div>
</template>

<script>
import { isHuaWeiBrowser, isPhoneNum, TraceLogInfoKeys, isMaskedAndT1Phone, url_safe_b64_decode } from "@/assets/js/common";
import { checkOrderParams, orderInfo, loadOrderInfo, submitPreUnderwritingOrder, } from "./function";
import { fetchRoundRobinWithAgeCommonFail } from "@/api/insurance-api";
import { domainPathMap } from "@/views/ZYBX/src";

export default {
    name: "Index1", // 推广版

    data() {
        return {
            orderInfo,
        }
    },
    computed: {
        orderPage() {
            const { source, action, school, identifier } = this.orderInfo;
            const productKey = school == 1 ? TraceLogInfoKeys.az_tkj_new_prevent_cancer_jd_default_cube_base : TraceLogInfoKeys.az_tkj_new_prevent_cancer_jd_cube_base;
            return `infoKey:${productKey}&page:${identifier}&act:${action}&src:${source}`;
        },
    },
    created() {
        this.init();
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }
    },
    methods: {
        // 初始化
        init() {
            const query = this.$route.query || {};
            query.source = query.source || 'direct'; // source要以链接携带的参数为准
            query.action = query.action || 'direct'; // action要以链接携带的参数为准
            query.sourcePage = query.sourcePage || ''; // sourcePage要以链接携带的参数为准
            query.channelCode = query.cld || query.channelCode || '';

            const inStore = loadOrderInfo() || {};
            Object.assign(this.orderInfo, inStore, query);
            try {
                if (query.bizParams) {
                    const params = JSON.parse(url_safe_b64_decode(query.bizParams));
                    Object.assign(this.orderInfo, params);
                }
            } catch (error) {

            }

            this.orderInfo.identifier = 'TK42Index';
            this.orderInfo.productKey = TraceLogInfoKeys.az_tkj_new_prevent_cancer_jd_default_cube_base;
            this.orderInfo.school = '1';
            this.orderInfo.autoRenewalType = 1;

            const { phoneNo, starPhone, mTel } = this.orderInfo;
            if (!isPhoneNum(phoneNo) && isMaskedAndT1Phone(starPhone, mTel)) {
                this.orderInfo.phoneNo = starPhone;
            } else {
                this.orderInfo.starPhone = '';
            }

            this.onSubmitOrder();
        },

        onSubmitOrder() {
            const { code, msg, } = checkOrderParams(this.orderInfo, this.orderPage);

            if (code == 1) {
                return this.jumpToNextLink();
            }

            this.$toast.loading({
                message: '正在为您匹配产品',
                forbidClick: true,
                duration: 0,
            });

            this.createSubmitOrder();
        },

        createSubmitOrder() {
            const { code, msg, params } = checkOrderParams(this.orderInfo, this.orderPage);
            if (this.orderInfo.school != 1) {
                params.planKey = TraceLogInfoKeys.az_tkj_new_prevent_cancer_jd_default_cube_base;
            }

            // if (isHuaWeiBrowser()) {
            //     params.extendParams.midPage = 'default';
            // }

            // params.extendParams.bankCode = 'BANK';
            params.extendParams = JSON.stringify(params.extendParams || {});
            submitPreUnderwritingOrder(params).then(url => {
                this.jumpToNextLink();
            }).catch(err => {
                this.fetchFailJump();
            });
        },

        fetchFailJump() {
            const { channel, phoneNo, mTel, relation } = this.orderInfo;

            const params = {
                channelId: channel,
                idCard: this.orderInfo[`idCard${relation}`],
                phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
                robinKey: 'tk_mf_pre_fail',
                currentPage: 'TK42',
            }

            let path = "";
            fetchRoundRobinWithAgeCommonFail(params).then(r => {
                path = r.data.path || "";
            }).finally(() => {
                if (!path || path == 'NotFound') {
                    return this.jumpToNextLink();
                }
                this.jumpToFailLink(path);
            });
        },

        jumpToFailLink(path) {
            this.$toast.clear(true);

            if (!domainPathMap[path]) {
                path = 'NWTK02Index1';
            }

            let href = domainPathMap[path];
            href = window.location.href.replace(/[^?]+/, href);
            window.location.href = href;
        },

        jumpToNextLink() {
            this.$toast.clear(true);

            const href = window.location.href.replace('/PreUW/Index', '/Index');
            window.location.href = href;
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2208310931 {
    width: 3.75rem;
    min-height: 100%;
    font-size: 0.15rem;
}
</style>
