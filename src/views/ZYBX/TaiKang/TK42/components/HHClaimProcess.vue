<template>
    <div class="container_2211011700">
        <div class="header">
            <span class="header-title">理赔说明</span>
            <span class="header-more" @click="onDetailClick">理赔须知</span>
        </div>
        <!-- <div class="time-x">
            <van-icon name="clock-o" />
            工作日：9:00-18:00
        </div> -->
        <div class="process-x">
            <div v-for="(item, index) in claimProcess" :key="index" class="process-item">
                <div class="process-item-key">{{ item.key }}</div>
                <div class="process-item-value">{{ item.value }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import { claimProcess } from "../src";

export default {
    name: "ClaimProcess",
    data() {
        return {
            claimProcess,
        }
    },
    methods: {
        onDetailClick() {
            this.$emit('click',);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2211011700 {
    color: #333333;
    font-size: 0.14rem;
    background-color: #FFFFFF;

    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        padding: 0.10rem 0.15rem;
        font-weight: 500;
        border-bottom: #F2F2F2 1px solid;

        .header-title {
            font-size: 0.18rem;
        }

        .header-more {
            color: #FF4509;
        }
    }

    .time-x {
        padding: 0.15rem 0 0.15rem 0.15rem;
        color: #FE5243;
        font-weight: 500;
    }

    .process-x {

        .process-item {
            padding: 0 0.15rem 0.15rem;

            .process-item-key {
                display: flex;
                align-items: center;
                line-height: 0.20rem;
                font-weight: 600;

                &::before {
                    content: " ";
                    margin-right: 0.05rem;
                    height: 0.08rem;
                    width: 0.08rem;
                    border-radius: 50%;
                    background-color: #FE5243;
                }
            }

            .process-item-value {
                padding: 0.05rem 0.15rem 0;
                line-height: 1.5;
                text-align: justify;
                color: #888888;
            }
        }
    }
}
</style>
