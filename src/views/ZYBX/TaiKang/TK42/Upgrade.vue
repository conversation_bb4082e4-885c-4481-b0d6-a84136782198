<template>
    <div class="container_2302211530">
        <div class="header">
            <img class="header-img" src="@/assets/imgs/TaiKang/TK41/img04.png">
        </div>
        <div class="premium-x">
            <p>优化保障后每月保费由<span>{{ premiumObj.first }}</span>元变更为<span>{{ premiumObj.next }}</span>元，自次月开始扣费并生效</p>
            <p>变更后的保障责任、赔付比例、免赔额详见<span class="view-txt"
                    @click.stop="onViewPolicy('产品说明及投保须知', true)">《产品说明及投保须知》</span></p>
        </div>
        <div id="id_action_button" class="submit-button" @click="onSubmit">
            变更保障计划
            <img alt="" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <div class="policy-x">
            <van-icon class="policy-icon" :name="orderInfo.checked ? 'checked' : 'circle'"
                @click="orderInfo.checked = !orderInfo.checked" />
            我已阅读并同意
            <span class="view-txt" @click.stop="onViewPolicy('产品说明及投保须知', true)">《产品说明及投保须知》</span>
            <span class="view-txt" @click.stop="onViewPolicy('保险条款', true)">《保险条款》</span>
            <span class="view-txt" @click.stop="onViewPolicy('健康告知', true)">《健康告知》</span>
            <span class="view-txt" @click.stop="onViewPolicy('责任免除', true)">《责任免除》</span>
            <span class="view-txt" @click.stop="onViewPolicy('特别约定', true)">《特别约定》</span>
            <span class="view-txt" @click.stop="onViewPolicy('投保规则', true)">《投保规则》</span>
            <span class="view-txt" @click.stop="onViewPolicy('泰康在线授权文件', true)">《泰康在线授权文件》</span>
            <span class="view-txt" @click.stop="onViewPolicy('隐私政策', true)">《隐私政策》</span>
            <span class="view-txt" @click.stop="onViewPolicy('费率表', false)">《费率表》</span>。
        </div>
        <img src="@/assets/imgs/IYB/IYBTK02/img05.png">
        <div class="copyright">
            <div class="wrapper">
                <img src="@/assets/imgs/logo/lechengshi_1.png">
                <img src="@/assets/imgs/logo/taikangzaixian.png">
            </div>
            <p>产品名称：泰康2024防癌医疗险基础版</p>
            <p>本产品由泰康在线财产保险股份有限公司授权<br>乐城十保险经纪有限公司销售服务</p>
            <p style="margin-top: 0.10rem;">互联网专属产品<br>本页面由乐城十保险经纪有限公司提供</p>
        </div>
        <div v-if="bottomButtonVisible" class="button-x" @click="onSubmit">
            变更保障计划<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
        <HHTabViewer :obj="policyObj" @ok="onAcceptPolicy"></HHTabViewer>
        <TKRecord1 ref="ref_record" :recordObj="orderInfo"></TKRecord1>
    </div>
</template>

<script>
import { bxStorage, } from "@/utils/store_util";
import HHTabViewer from "./components/HHTabViewer";
import TKRecord1 from "@/views/components/TKRecord1";
import HHPolicyViewer from "./components/HHPolicyViewer";
import { orderInfo, eventTracking, loadOrderInfo, getPolicyVersion } from "./function";
import { TraceLogInfoKeys, url_safe_b64_decode } from "@/assets/js/common";

export default {
    name: "Upgrade", // 升级页
    components: { HHPolicyViewer, HHTabViewer, TKRecord1, },
    data() {
        return {
            orderInfo,
            upgradeObj: { visible: false },
            policyObj: { v: false, page: '', v1: false, page1: '', belongs: 5 },
            bottomButtonVisible: false,
        }
    },
    computed: {
        premiumObj() {
            const { nextPremium, firstPremium } = this.orderInfo;
            return { first: firstPremium, next: nextPremium };
        },
    },
    created() {
        this.init();
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.$nextTick(() => { // 监听滚动事件
            this.actionButtonObserver();
        });

        this._entryReport();

        if (this.orderInfo.school == 1) {
            this.pushToResult();
        }
    },

    methods: {
        actionButtonObserver() {
            if (window.IntersectionObserver) {
                const observer = new IntersectionObserver((entries, observer) => {
                    this.bottomButtonVisible = !entries[0].isIntersecting;
                }, { threshold: 0.01 });

                const buttonNode = document.getElementById('id_action_button');
                buttonNode && observer.observe(buttonNode);
            }
        },

        // 初始化
        init() {
            const query = this.$route.query || {};
            query.source = query.source || 'direct'; // source要以链接携带的参数为准
            query.action = query.action || 'direct'; // action要以链接携带的参数为准
            query.school = query.school || '0'; // school要以链接携带的参数为准

            const inStore = loadOrderInfo();
            Object.assign(this.orderInfo, inStore, query);

            this.orderInfo.identifier = 'TK42Upgrade';
            this.orderInfo.planKey = TraceLogInfoKeys.az_tkj_new_prevent_cancer_jd_cube_base;
            this.orderInfo.checked = this.orderInfo.channel >= 1000;
            this.policyObj.belongs = getPolicyVersion(this.orderInfo, false);

            this.decodeUserInfo();
            bxStorage.setRawItem('TK42_Upgrade', 1);
        },

        decodeUserInfo() {
            const param = url_safe_b64_decode(this.orderInfo.bizParams);
            if (!param) return;

            const obj = JSON.parse(param);
            Object.assign(this.orderInfo, obj);
        },

        onSubmit() {
            if (this.orderInfo.checked) {
                return this.pushToResult();
            }

            this.onViewPolicy('产品说明及投保须知', true);
        },

        pushToResult() {
            this._actionTracking('点击立即升级按钮');

            setTimeout(() => {
                const href = window.location.href.replace('/Upgrade', '/Result');
                window.location.href = href;
            }, 250);
        },

        onAcceptPolicy() {
            this.orderInfo.checked = true;
            this.pushToResult();
        },

        onViewPolicy(name, isPolicy,) {
            this.policyObj[isPolicy ? 'v1' : 'v'] = true;
            this.policyObj[isPolicy ? 'page1' : 'page'] = name;
        },

        _entryReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            this._actionTracking('升级页', domContentLoadedEventEnd - fetchStart);
        },

        _actionTracking(name, time = 0) {
            eventTracking(name, time);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2302211530 {
    width: 3.75rem;
    min-height: 100%;
    font-size: 0.15rem;
    background-color: #ffffff;

    img {
        display: block;
        max-width: 100%;
    }

    .view-txt {
        color: #ff8c41;
        font-weight: 500;
    }

    .premium-x {
        margin-top: 0.15rem;
        font-size: 0.12rem;
        color: #333333;
        text-align: center;
        line-height: 0.2rem;
    }

    .policy-x {
        padding: 0.1rem;
        background-color: #ffffff;
        font-size: 0.13rem;
        color: #333333;
        line-height: 1.6;
        text-align: justify;

        .policy-icon {
            color: #ff7000;
            font-size: 0.16rem;
            vertical-align: -0.01rem;
        }
    }

    .button-x,
    .submit-button {
        position: relative;
        margin: 0.1rem 0.35rem 0.1rem;
        padding: 0.15rem 0;
        font-size: 0.2rem;
        color: #ffffff;
        font-weight: 700;
        text-align: center;
        border-radius: 999px;
        box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
        background: linear-gradient(270deg,
                rgb(255, 16, 46),
                rgb(253, 123, 69));
        animation: button_animate 1.35s linear infinite;

        .hand {
            position: absolute;
            top: 0.25rem;
            left: 75%;
            width: 18%;
            animation: hand_animate 1s linear infinite;
        }
    }

    .button-x {
        position: fixed;
        inset: auto 0 0.25rem 0;
        margin: 0 auto;
        width: 3rem;
    }

    .copyright {
        padding: 0 0 1rem;
        color: #969799;
        font-size: 0.12rem;
        line-height: 1.6;
        text-align: center;
        background-color: #f8f8f8;

        .wrapper {
            padding: 0.2rem 0 0.15rem;
            display: flex;
            align-items: center;
            justify-content: space-evenly;

            img {
                height: 0.40rem;
            }
        }
    }

    @keyframes button_animate {
        0% {
            transform: scale(1);
        }

        40% {
            transform: scale(1);
        }

        70% {
            transform: scale(0.95);
        }

        100% {
            transform: scale(1);
        }
    }

    @keyframes hand_animate {
        0% {
            transform: translate(-0.1rem, -0.1rem);
        }

        45% {
            transform: translate(0.1rem, 0);
        }

        70% {
            transform: translate(0.1rem, 0);
        }

        100% {
            transform: translate(-0.1rem, -0.1rem);
        }
    }
}
</style>
