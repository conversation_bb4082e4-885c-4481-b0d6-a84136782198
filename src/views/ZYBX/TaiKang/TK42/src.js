export const PremiumRate = [
    { min: 0, max: 55, month: { data1: 0.70, data2: 4.60 }, year: { data1: 0.38, data2: 0.38 } },
    { min: 56, max: 70, month: { data1: 1.80, data2: 12.60 }, year: { data1: 19.72, data2: 19.72 } },
    { min: 71, max: 80, month: { data1: 3.20, data2: 22.70 }, year: { data1: 46.08, data2: 46.08 } },
]

export const PremiumRate1 = [
    { min: 0, max: 4, month: { data1: 59.5, data2: 109.0 }, year: { data1: 123.0, data2: 100.0 } },
    { min: 5, max: 10, month: { data1: 31.8, data2: 52.7 }, year: { data1: 80.0, data2: 75.0 } },
    { min: 11, max: 15, month: { data1: 19.4, data2: 28.8 }, year: { data1: 96.0, data2: 91.0 } },
    { min: 16, max: 20, month: { data1: 22.2, data2: 34.2 }, year: { data1: 136.0, data2: 131.0 } },
    { min: 21, max: 25, month: { data1: 27.3, data2: 44.8 }, year: { data1: 210.0, data2: 253.0 } },
    { min: 26, max: 30, month: { data1: 33.0, data2: 56.7 }, year: { data1: 349.0, data2: 423.0 } },
    { min: 31, max: 35, month: { data1: 48.6, data2: 81.6 }, year: { data1: 619.0, data2: 685.0 } },
    { min: 36, max: 40, month: { data1: 67.8, data2: 116.6 }, year: { data1: 775.0, data2: 832.0 } },
    { min: 41, max: 45, month: { data1: 95.2, data2: 174.4 }, year: { data1: 1191.0, data2: 1211.0 } },
    { min: 46, max: 50, month: { data1: 130.8, data2: 240.8 }, year: { data1: 1119.0, data2: 1091.0 } },
    { min: 51, max: 55, month: { data1: 177.2, data2: 326.2 }, year: { data1: 1637.0, data2: 1552.0 } },
    { min: 56, max: 60, month: { data1: 241.9, data2: 439.3 }, year: { data1: 2370.0, data2: 2232.0 } },
    { min: 61, max: 65, month: { data1: 355.0, data2: 686.7 }, year: { data1: 5130.0, data2: 4899.0 } },
    { min: 66, max: 70, month: { data1: 466.5, data2: 882.6 }, year: { data1: 5130.0, data2: 4899.0 } },
    { min: 71, max: 75, month: { data1: 166.2, data2: 551 }, year: { data1: 5130.0, data2: 4899.0 } },
    { min: 76, max: 80, month: { data1: 200.9, data2: 671.7 }, year: { data1: 5130.0, data2: 4899.0 } },
]

export const documentList = [
    { page: '产品说明及投保须知', belongs: '123456789' },
    {
        page: '保险条款', belongs: '1234', list: [
            '泰康在线财产保险股份有限公司住院费用医疗保险U款（互联网专属）',
        ],
    },
    {
        page: '保险条款', belongs: '56', list: [
            '泰康在线财产保险股份有限公司住院费用医疗保险U款（互联网专属）',
            '泰康在线财产保险股份有限公司附加重大疾病异地转诊公共交通费用及救护车费用保险A款（互联网专属）',
            '泰康在线财产保险股份有限公司附加指定疾病及手术扩展特需费用医疗保险（互联网专属）',
            '泰康在线财产保险股份有限公司附加互联网医院特定药品费用医疗保险B款（互联网专属）',
            '泰康在线财产保险股份有限公司附加癌症住院津贴保险（互联网专属）',
        ],
    },
    {
        page: '保险条款', belongs: '789', list: [
            '泰康在线财产保险股份有限公司住院费用医疗保险U款（互联网专属）',
            '泰康在线财产保险股份有限公司附加重大疾病异地转诊公共交通费用及救护车费用保险A款（互联网专属）',
        ],
    },
    { page: '健康告知', belongs: '123456789' },
    { page: '责任免除', belongs: '123456789' },
    { page: '特别约定', belongs: '123456789' },
    { page: '投保规则', belongs: '123456789' },
    { page: '泰康在线授权文件', belongs: '123456789' },
    { page: '隐私政策', belongs: '123456789' },
];

export const pdfFileObj = {
    '产品说明及投保须知$v1': '/TaiKang/TK42/tbxz_v1.pdf',
    '产品说明及投保须知$v5': '/TaiKang/TK42/tbxz_v2.pdf',
    '产品说明及投保须知$v6': '/TaiKang/TK42/tbxz_v2.pdf',
    '产品说明及投保须知$v7': '/TaiKang/TK42/tbxz_v2.pdf',
    '健康告知$v1': '/TaiKang/TK41/jkgz_v1.pdf',
    '健康告知$v5': '/TaiKang/TK41/jkgz_v2.pdf',
    '健康告知$v6': '/TaiKang/TK41/jkgz_v2.pdf',
    '健康告知$v7': '/TaiKang/TK41/jkgz_v2.pdf',
    '责任免除$v1': '/TaiKang/TK41/zrmc_v1.pdf',
    '责任免除$v5': '/TaiKang/TK41/zrmc_v2.pdf',
    '责任免除$v6': '/TaiKang/TK41/zrmc_v2.pdf',
    '责任免除$v7': '/TaiKang/TK41/zrmc_v2.pdf',
    '特别约定$v1': '/TaiKang/TK41/tbyd_v1.pdf',
    '特别约定$v5': '/TaiKang/TK42/tbyd_v5.pdf',
    '特别约定$v6': '/TaiKang/TK42/tbyd_v6.pdf',
    '特别约定$v7': '/TaiKang/TK42/tbyd_v7.pdf',
    '投保规则$v1': '/TaiKang/TK41/tbgz_v1.pdf',
    '投保规则$v5': '/TaiKang/TK41/tbgz_v2.pdf',
    '投保规则$v6': '/TaiKang/TK41/tbgz_v2.pdf',
    '投保规则$v7': '/TaiKang/TK41/tbgz_v2.pdf',
    '隐私政策': '/TaiKang/TK40/lcs_ysxy.pdf',
    '泰康在线授权文件': '/IYB/IYBTK09/zxsq_1.pdf?v=1',
    '泰康在线财产保险股份有限公司住院费用医疗保险U款（互联网专属）': '/IYB/IYBTK02/dir1.pdf',
    '泰康在线财产保险股份有限公司附加重大疾病异地转诊公共交通费用及救护车费用保险A款（互联网专属）': '/IYB/IYBTK02/dir2.pdf',
    '泰康在线财产保险股份有限公司附加指定疾病及手术扩展特需费用医疗保险（互联网专属）': '/IYB/IYBTK02/dir4.pdf',
    '泰康在线财产保险股份有限公司附加互联网医院特定药品费用医疗保险B款（互联网专属）': '/IYB/IYBTK06/dir1.pdf',
    '泰康在线财产保险股份有限公司附加癌症住院津贴保险（互联网专属）': '/IYB/IYBTK06/dir2.pdf',
}

// 投保关系
export const relations = [
    { key: "本人", value: 1, param: 1 },
    { key: "配偶", value: 2, param: 2 },
    { key: "儿女", value: 3, param: 3 },
    { key: "父母", value: 4, param: 4 },
]

// 有无社保
export const insurances = [
    { key: "有医保(含新农合)", value: 1 },
    { key: "无医保", value: 0 },
]

// 缴费方式
export const repays = [
    { key: "按月缴费(12期)", value: 1 },
    { key: "全额缴费", value: 0 },
]

// 保障内容
export const planFeature = [
    '保单门槛低',
    '覆盖人群广',
    '保单可验真',
]

export const planSummary = [
    { key: '投保年龄', value: '30天-80周岁' },
    { key: '医院范围', value: '二级及以上公立医院普通部' },
    { key: '保障期限', value: '1年' },
    { key: '犹豫期', value: '15天' },
    { key: '社保内原发恶性肿瘤医疗保险金', value: '300万元' }
]

export const planSummary1 = planSummary

export const planPoints = [
    { key: '投保年龄', value: '30天-80周岁' },
    { key: '保障区域', value: '中国境内（不含港澳台）' },
    { key: '医院范围', value: '二级及以上公立医院普通部' },
    { key: '保障期限', value: '1年' },
    { key: '等待期', value: '高龄升级方案等待期为90天，其余30天' },
    { key: '犹豫期', value: '15天' },
]

export const planPoints1 = planPoints

export const planDetails = [
    {
        key: '社保内原发恶性肿瘤医疗保险金',
        value: '300万元',
        text: '1、社保内原发恶性肿瘤医疗保险金5万元免赔额。<br>2、若被保险人以有社会医疗保险身份投保，且以社会医疗保险身份就诊并结算的，保险人按25%的比例进行赔付。若被保险人以有社会医疗保险身份投保，但未以社会医疗保险身份就诊并结算的，保险人按照15%的比例进行赔付。若被保险人以无社会医疗保险身份投保，保险人按照25%的比例进行赔付。'
    },
]

export const planDetails1 = [
    {
        key: '社保内原发恶性肿瘤医疗保险金',
        value: '300万元',
        text: '1、社保内原发恶性肿瘤医疗保险金免赔额为0元。<br>2、若被保险人以有社会医疗保险身份投保，且以社会医疗保险身份就诊并结算的，保险人按100%的比例进行赔付。若被保险人以有社会医疗保险身份投保，但未以社会医疗保险身份就诊并结算的，保险人按照60%的比例进行赔付。若被保险人以无社会医疗保险身份投保，保险人按照100%的比例进行赔付。'
    }, {
        key: '社保外原发恶性肿瘤医疗保险金',
        value: '300万元',
        text: '1、社保外原发恶性肿瘤医疗保险金免赔额为0元。<br>2、社保外原发恶性肿瘤医疗保险金的赔付比例为100%。'
    }, {
        key: '恶性肿瘤院外特种药品费用保险金',
        value: '600万元',
        text: '1、恶性肿瘤院外特种药品费用保险金的免赔额为0元。<br>2、（一）社保目录内药品费用赔偿比例：<br>（1）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，赔偿比例为100%；<br>（2）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，赔偿比例为60%；<br>（3）被保险人以未参加社会基本医疗保险或公费医疗身份投保，赔偿比例为100%。<br>（二）社保目录外药品费用赔偿比例为100%。'
    }, {
        key: '质子重离子医疗保险金',
        value: '600万元',
        text: '1、质子重离子医疗保险金的免赔额为0元。<br>2、质子重离子医疗保险金的赔偿比例为100%。升级方案选择承保的社保内原发恶性肿瘤医疗保险金、社保外原发恶性肿瘤医疗保险金、恶性肿瘤院外特种药品费用保险金、质子重离子医疗保险金责任，年度累计保险金额为600万元。'
    }, {
        key: '重大疾病异地转诊公共交通费用及救护车费用保险金',
        value: '1万元',
        text: '1、重大疾病异地转诊公共交通费用及救护车费用保险金的赔付比例为100%。<br>2、重大疾病异地转诊公共交通费用及救护车费用保险金的次限额为1000元，保险金额为1万元。'
    }, {
        key: '互联网医院特定药品费用医疗保险金',
        value: '4 万元',
        text: '1、互联网医院特定疾病药品费用赔付比例 50%，慢病特定药品费用赔付比例 30%。<br>2、互联网医院特定疾病药品费用单次赔偿限额 800 元，慢病特定药品费用单次赔偿限额 2000 元，每月限 1 次。'
    }, {
        key: '指定疾病及手术扩展特需费用保险金',
        value: '600万元',
        text: '1、指定疾病及手术扩展特需费用保险金赔付比例100%。<br>2、指定疾病及手术扩展特需费用保险金床位费限额为1500元/日。'
    }, {
        key: '癌症住院津贴保险金 最高 300 元/天',
        value: '600万元',
        text: '1、癌症免赔住院日数为 3 天，0-45 周岁按照 300 元/天给付，46-70 周岁按照 100 元/天给付。<br> 2、被保险人同一次住院的癌症住院津贴最高给付日数以九十日为限，保险期间内累计癌症住院津贴最高给付日数以一百八十日为限。'
    },
]

// 理赔说明
export const claimProcess = [
    {
        key: "第1步：理赔报案",
        value: '拨打40007-95522客服热线进行理赔报案，随后有专业理赔人员联系。'
    }, {
        key: "第2步：准备保险事故相关材料",
        value: "根据工作人员引导准备并提交理赔资料。"
    }, {
        key: "第3步：完成理赔",
        value: "根据审核结果将理赔款项支付到被保人指定账户。理赔时效：一般会在资料提交后5个工作日结案，需调查核实的复杂案件30日。"
    },
]

export const askAnswerList = [
    {
        q: '投保后或升级后，责任何时生效？',
        a: '本产品保单起期为投保成功后次日零时；如客户首次投保基础版，后续选择升级到“升级版”，升级后责任在升级后的下个交费日交费成功后于当日零时生效。'
    }, {
        q: '这款产品有犹豫期吗？',
        a: '有，自本合同生效之日起15日（含第15日）为犹豫期。',
    }, {
        q: '升级后的保险期间会变化吗？',
        a: '升级后保单的保险止期同升级前基础版的保险止期，保险期间不会变化。',
    }, {
        q: '投保成功后在哪里查看保单？',
        a: '可以通过关注“泰康在线保险服务”微信公众号或下载“泰康在线”app查看电子保单。',
    },
]
