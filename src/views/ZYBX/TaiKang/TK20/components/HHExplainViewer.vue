<template>
    <van-popup v-model="obj.v" class="container_2211041610" position="center">
        <div class="container">
            <p class="header">{{ obj1.title }}</p>
            <div class="content">
                <p v-for="v in obj1.contents" :key="v">
                    <van-icon color="#F08E39" name="certificate"/>
                    {{ v }}
                </p>
            </div>
        </div>
        <div class="button" @click="closeAction"></div>
    </van-popup>
</template>

<script>

const ExplainObj = {
    '医保': {
        title: '以下情况都算有医保：',
        contents: [
            '新型农村合作医疗（新农合）；',
            '城乡居民基本医疗保险；',
            '城镇职工基本医疗保险；',
            '城镇居民基本医疗保险；',
            '公费医疗保险等。',
        ],
    },

    '续保': {
        title: '如开通可享受：',
        contents: [
            '安心！开通自动缴费可使保障不间断',
            '省心！下一年重新投保，不用担心断保',
            '放心！无额外费用，开通后可随时取消',
        ],
    },
}

export default {
    name: "HHExplainViewer",
    props: {obj: Object,},
    computed: {
        obj1() {
            return ExplainObj[this.obj.page] || {};
        },
    },
    methods: {
        closeAction() {
            this.obj.v = false;
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2211041610 {
    width: 3.20rem;
    background-color: unset;

    .container {
        padding: 0.05rem 0 0.05rem 0.15rem;
        border-radius: 0.10rem;
        background-color: #FFFFFF;

        .header {
            font-size: 0.17rem;
            font-weight: bold;
            line-height: 0.5rem;
            color: #F08E39;
            border-bottom: #F2F2F2 1px solid;
        }

        .content {
            margin: 0.10rem 0;
            font-size: 0.15rem;
            line-height: 0.25rem;
            color: #333333;
        }
    }

    .button {
        margin: 0.1rem auto;
        width: 0.32rem;
        height: 0.32rem;
        background: url("~@/assets/imgs/common/sprites.png") no-repeat -1.92rem 0;
        background-size: 3.2rem 6.4rem;
    }
}
</style>
