<template>
    <div class="container_2303061710">
        <div style="overflow: auto;flex: 1;">
            <div class="header">
                <img src="@/assets/imgs/TaiKang/TK36/promotion1.png" alt="">
                <p class="count-down">
                    <span>{{ minutes }}</span>:
                    <span>{{ seconds }}</span>后将失去优化资格，请尽快完成投保
                </p>
            </div>
            <div class="section">
                <div class="section-header">
                    <span>投保人</span>
                    <span @click="onEdit" class="section-header-edit">编辑</span>
                </div>
                <div v-for="(item, index) in holderInfo" :key="index" class="section-item">
                    <span>{{ item.key }}</span>
                    <span class="section-item-value">{{ item.value }}</span>
                </div>
            </div>
            <div class="section">
                <div class="section-header">
                    <span>被保人</span>
                    <span @click="onEdit" class="section-header-edit">编辑</span>
                </div>
                <div v-for="(item, index) in insuredInfo" :key="index" class="section-item">
                    <span>{{ item.key }}</span>
                    <span class="section-item-value">{{ item.value }}</span>
                </div>
            </div>
            <div class="premium-x">
                月缴更轻松，每{{ premiumObj.name }}<span class="number">{{ premiumObj.val }}</span>元
            </div>
            <div class="policy-x">
                <div>
                    我确认并接受
                    <span class="view-txt" @click.stop="viewAction('健康告知', true)">《健康告知》</span>
                    <span class="view-txt" @click.stop="viewAction('投保须知', true)">《投保须知》</span>
                    <span class="view-txt" @click.stop="viewAction('责任免除', true)">《责任免除》</span>
                    <span class="view-txt" @click.stop="viewAction('重要信息', true)">《重要信息》</span>
                    <span class="view-txt" @click.stop="viewAction('特别约定', true)">《特别约定》</span>
                    <span class="view-txt" @click.stop="viewAction('产品说明', true)">《产品说明》</span>
                    <span class="view-txt" @click.stop="viewAction('保险条款', true)">《保险条款》</span>
                    <span class="view-txt" @click.stop="viewAction('费率表', false)">《费率表》</span>。
                </div>
            </div>
        </div>
        <div class="bottom-button" @click="onSubmit">立即完善</div>
        <HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
        <HHTabViewer :obj="policyObj" @ok="onAcceptPolicy"></HHTabViewer>
        <!--投保提示弹窗-->
        <van-popup v-model="popupObj.v" class="container_2212271130" position="center" round @click-overlay="onClickPopupOverlay" >
            <div class="box">
                <div class="button" @click="onSubmit">
                    <!--为{{ orderInfo.insuredName }}投保<span v-if="popupSecond>0">({{ popupSecond }}s)</span>-->
                    为{{ orderInfo.insuredName }}投保
                </div>
                <div class="hint">
                    <span>256987</span>人已参与
                </div>
            </div>
            <div class="box-close" @click="onClickPopupClose">
                <svg-icon iconClass="error" class="box-close-icon" />
            </div>
        </van-popup>
    </div>
</template>

<script>
import { bxStorage, } from "@/utils/store_util";
import HHTabViewer from "./components/HHTabViewer";
import HHPolicyViewer from "./components/HHPolicyViewer";
import { domainPathMap, } from "@/views/ZYBX/src";
import { fetchPromoteOrderInfo } from "@/api/insurance-api";
import { url_safe_b64_encode } from "@/assets/js/common";
import { calculatePremium, showToast, eventTracking, submitPromoteOrderInfo, createOrderInfo } from "./function";
import { getDailyRateFromMonthlyRate } from '@/utils/rate';
import '@/assets/icons/error.svg'

export default {
    name: "Promotion",
    components: { HHPolicyViewer, HHTabViewer },
    data() {
        const orderInfo1 = {
            holderName: '',
            holderIdCard: '',
            holderPhone: '',
            insuredName: '',
            insuredIdCard: '',
            expireTime: 0,
        };

        const orderInfo = createOrderInfo();

        Object.assign(orderInfo, orderInfo1);

        return {
            minutes: 0,
            seconds: 0,
            orderInfo,
            policyObj: { v: false, page: '', v1: false, page1: '', belongs: 'v1' },
            popupObj: { v: false },
            // popupTimer: null,
            // popupSecond: 3,
        }
    },
    computed: {
        premiumObj() {
            if (!this.orderInfo.totalPremium) {
                return {
                    name: '月',
                    val: '0.6',
                };
            }
            return getDailyRateFromMonthlyRate(this.orderInfo.totalPremium);
        },

        holderInfo() {
            const { holderName, holderIdCard, holderPhone } = this.orderInfo;
            return [
                { key: '姓名', value: holderName },
                { key: '身份证', value: holderIdCard },
                { key: '手机号', value: holderPhone },
            ]
        },

        insuredInfo() {
            const { insuredName, insuredIdCard, insurance } = this.orderInfo;
            return [
                { key: '姓名', value: insuredName },
                { key: '身份证', value: insuredIdCard },
                { key: '有无社保', value: insurance == 1 ? '有' : '无' },
            ]
        },
    },

    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.init();

        this.popupObj.v = true;
        // this.openPopupCountdown();
    },
    beforeDestroy() {
        // this.closePopupCountdown();
    },
    methods: {
        // closePopupCountdown() {
        //     clearInterval(this.popupTimer);
        //     this.popupTimer = null;
        // },
        // openPopupCountdown() {
        //     clearInterval(this.popupTimer);
        //     this.popupSecond = 3;
        //     this.popupTimer = setInterval(() => {
        //         this.popupSecond--;
        //         if (this.popupSecond <= 0) {
        //             this.popupObj.v = false;
        //             this.closePopupCountdown();
        //         }
        //     }, 1000);
        // },
        onClickPopupOverlay() {
            this._actionTracking('点击弹窗遮罩');
            // 弹窗组件已经关闭了，这里只记录埋点，不用手动赋值关闭
            // this.popupObj.v = false;
            // this.closePopupCountdown();
        },
        onClickPopupClose() {
            this._actionTracking('点击弹窗关闭按钮');
            // 关闭弹窗和关闭倒计时
            this.popupObj.v = false;
            // this.closePopupCountdown();
        },

        //初始化
        init() {
            let inQry = this.$route.query || {}; // 必须：加密的infoNo

            const { channel, infoNo } = inQry;
            this.orderInfo.channel = channel || '1';
            this.orderInfo.identifier = 'TK36Promotion9';
            this.orderInfo.action = 'promote';
            this.orderInfo.source = 'promote';
            this.orderInfo.expireTime = bxStorage.getRawItem('TK36ExpireTime') || 0;

            this.fetchOrderInfo(infoNo);
        },

        fetchOrderInfo(infoNo) {
            if (!infoNo) {
                this._entryBehaviorReport();
                return this.editOrderInfo(true);
            }

            this.$toast.loading({
                message: '正在请求数据\n请稍候',
                forbidClick: true,
                duration: 0,
            });

            fetchPromoteOrderInfo(infoNo).then(res => {
                this.$toast.clear(true);
                const data = res.data || {};
                this.orderInfoHandle(data);
            }).catch(err => {
                this.$toast.clear(true);
                this.editOrderInfo(true);
            }).finally(() => {
                this._entryBehaviorReport();
            });
        },

        orderInfoHandle(data) {
            Object.assign(this.orderInfo, data);

            const { operatorPhone, insuredIdCard, insurance, repay, infoNo, } = this.orderInfo;
            if (!infoNo) {
                return this.editOrderInfo(true);
            }

            this.orderInfo.mTel = operatorPhone;
            this.orderInfo.totalPremium = calculatePremium(insuredIdCard, insurance, repay).low;

            this.startCountdown();
        },

        startCountdown() {
            if (Date.now() - this.orderInfo.expireTime > 0) {
                this.orderInfo.expireTime = Date.now() + 60 * 60 * 1000;
                bxStorage.setRawItem('TK36ExpireTime', this.orderInfo.expireTime);
            }

            requestAnimationFrame(() => {
                const diff = Math.floor((this.orderInfo.expireTime - Date.now()) / 1000);
                this.minutes = ('00' + Math.floor(diff / 60)).slice(-2);
                this.seconds = ('00' + (diff - this.minutes * 60)).slice(-2);

                this.startCountdown();
            })
        },

        onEdit() {
            this._actionTracking('点击编辑按钮');
            this.editOrderInfo();
        },

        editOrderInfo(delay, msg) {
            const { channel, mTel } = this.orderInfo;
            const params = { channel, mTel, action: 'promotion', source: 'promotion' }
            const bizParams = url_safe_b64_encode(JSON.stringify(params));
            const href = domainPathMap['TK36Index1'] + `?bizParams=${bizParams}`;

            if (!delay) {
                return setTimeout(() => {
                    return window.location.href = href;
                }, 250);
            }

            const message = (msg || '').indexOf('已') >= 0 ? '此保单已支付，您可以投保其它产品' : msg;
            showToast(message || '该订单无效，正在跳转到首页');

            setTimeout(() => {
                return window.location.href = href;
            }, 2000);
        },

        onSubmit() {
            this.popupObj.v = false;
            // this.closePopupCountdown();

            const { infoNo, channel, productKey } = this.orderInfo;

            this._actionTracking('点击促活立即支付按钮');

            this.$toast.loading({
                message: '订单提交中\n请稍候',
                forbidClick: true,
                duration: 0,
            });

            const params = { infoNo: infoNo, page: `infoKey:${productKey}&page:promotion9`, channelId: channel };
            submitPromoteOrderInfo(params, this.orderInfo).then(url => {
                this.$toast.clear(true);
                return window.location.href = url;
            }).catch(err => {
                const message = err.msg || '';
                this.editOrderInfo(true, message);
            });
        },

        onAcceptPolicy() {
            this.onSubmit();
        },

        viewAction(name, isPolicy) {
            this.policyObj[isPolicy ? 'v1' : 'v'] = true;
            this.policyObj[isPolicy ? 'page1' : 'page'] = name;
        },

        _entryBehaviorReport() {
            const { timing } = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const { domContentLoadedEventEnd, fetchStart } = timing || {};
            this._actionTracking('支付促活页', domContentLoadedEventEnd - fetchStart);
        },

        _actionTracking(name, time = 0) {
            eventTracking(this.orderInfo, name, time);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2303061710 {
    height: 100%;
    box-sizing: border-box;
    background-color: #f2f2f2;

    display: flex;
    flex-direction: column;

    img {
        display: block;
        max-width: 100%;
    }

    .header {
        position: relative;

        .count-down {
            position: absolute;
            top: 0.90rem;
            left: 0.20rem;
            display: flex;
            align-items: center;

            font-size: 0.14rem;
            font-weight: 700;
            color: #ffffff;

            span {
                margin: 0 0.03rem;
                padding: 0.04rem 0.05rem;
                text-align: center;
                border-radius: 0.04rem;
                background-color: #FF3601;
            }
        }
    }

    .section+.section {
        margin-top: 0.1rem;
    }

    .section {
        background-color: #ffffff;

        .section-header {
            padding: 0.15rem 0.1rem;
            font-size: 0.17rem;
            font-weight: 700;

            display: flex;
            align-items: center;
            justify-content: space-between;

            .section-header-edit {
                color: #0b78ff;
            }
        }

        .section-item {
            padding: 0 0.1rem 0.15rem;
            font-size: 0.14rem;
            font-weight: 500;
            color: #666666;

            display: flex;
            align-items: center;
            justify-content: space-between;

            .section-item-value {
                color: #191919;
                font-size: 0.16rem;
                font-weight: 500;
            }

            .section-item-point {
                color: #fd8a25;
                font-weight: bold;
            }
        }
    }

    .premium-x {
        margin: 0.15rem 0;
        color: #333333;
        font-size: 0.14rem;
        text-align: center;

        .number {
            font-size: 0.16rem;
            font-weight: 700;
            color: #FF3601;
        }
    }

    .policy-x {
        padding: 0.1rem;
        font-size: 0.13rem;
        color: #333333;
        line-height: 1.6;
        text-align: justify;
        background-color: #ffffff;

        .view-txt {
            color: #FF3601;
            font-weight: 500;
        }
    }

    .bottom-button {
        padding: 0.16rem 0;
        color: #ffffff;
        font-size: 0.17rem;
        font-weight: 700;
        text-align: center;
        background-color: #FF3601;
    }
}

.container_2212271130 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background-color: transparent;

    img {
        display: block;
        max-width: 100%;
    }

    .box {
        position: relative;
        width: 3.15rem;
        height: 4.83rem;
        background: url("~@/assets/imgs/TaiKang/TK36/promotion3.png") no-repeat center/100%;
    }

    .button {
        position: absolute;
        bottom: .7rem;
        left: .225rem;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 2.7rem;
        height: .52rem;
        background: url("~@/assets/imgs/TaiKang/TK36/promotion3_button.png") no-repeat center/100%;
        color: #FFF1F0;
        font-size: .2rem;
        font-weight: 500;
    }

    .hint {
        position: absolute;
        bottom: 0;
        left: .19rem;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 2.8rem;
        height: .5rem;
        border-top: 2px dashed #BE351A;
        font-weight: 400;
        font-size: .14rem;
        color: #999999;

        span {
            color: #002EFE;
        }
    }

    .box-close {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 0.6rem;
        height: 0.6rem;
        font-size: 0.3rem;

        .box-close-icon {
            color: #999;
        }
    }
}
</style>
