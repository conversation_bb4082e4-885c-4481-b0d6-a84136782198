<template>
    <van-popup v-model="obj.v" :close-on-click-overlay="false" class="container_2302231345" round>
        <div class="content" @click="okAction">
            <img src="@/assets/imgs/common/popup_failed.png">
            <div class="button">立即领取（{{ countdown }}s）</div>
        </div>
    </van-popup>
</template>

<script>

export default {
    name: "HHFailPopup",
    props: {obj: Object},
    data() {
        return {
            timerId: null,
            countdown: 3,
        }
    },
    watch: {
        'obj.v': {
            handler() {
                if (this.obj.v) {
                    this.startTimer();
                }
            },
        },
    },
    beforeDestroy() {
        this.clearTimer();
    },
    methods: {
        startTimer() {
            this.clearTimer();
            this.countdown = 3;
            this.timerId = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    this.okAction();
                }
            }, 1000);
        },
        clearTimer() {
            this.timerId && clearInterval(this.timerId);
        },
        okAction() {
            this.clearTimer();
            this.obj.v = false;
            this.$emit('click');
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2302231345 {
    width: 85%;
    background-color: #FFFFFF;

    .content {
        position: relative;

        img {
            display: block;
            max-width: 100%;
        }

        .button {
            position: absolute;
            width: 2.0rem;
            height: 0.40rem;
            line-height: 0.40rem;
            border-radius: 0.20rem;
            text-align: center;
            background-color: #FF4C13;
            font-size: 0.16rem;
            color: #FFFFFF;
            bottom: 0.15rem;
            left: 50%;
            transform: translateX(-50%);
        }
    }
}

</style>

