import moment from "moment";
import CryptoJS from "crypto-js";
import { Toast } from "vant";
import { PremiumRate, PremiumRate1 } from "./src";
import { actionTracking } from "@/assets/js/api";
import { bxStorage, } from "@/utils/store_util";
import { createPromotionOrder, fetchCreateTKZengXianOrder, upgradeTKJCubeProduct, createTKJPreUnderwritingOrder } from "@/api/insurance-api";
import { isCardNo, isInWx, isMaskedAndT1Phone, isPersonName, isPhoneNum, TraceLogInfoKeys, url_safe_b64_encode, GetAge } from "@/assets/js/common";

export const createOrderInfo = (action = '') => {
    return {
        checked: false,
        hintVisible: true,
        isLowPricePage: true,
        source: action, // 页面来源
        action: action, //  back: '返回',promotion: '促活跳转',follow: '结果跳转',direct: '直发',forward: '转发',result: '结果页',promote: '促活'
        productKey: TraceLogInfoKeys.az_tkj_qn_by_jd_cube_base,
        identifier: '',
        relation: 1,    // 为谁投保 1:本人；2:配偶；3:儿女；4:父母；
        insurance: 1,   // 有无社保
        repay: 1,       // 缴费方式
        autoRenewalType: -1, // 自动续期类型
        channel: '1',
        channelCode: '',
        m: '',          // 链接自带的加密手机号
        tel: '',        // 操作人手机号
        mTel: '',       // 加密手机号
        infoNo: '',     // 订单号
        policyNo: '',    // 保单号
        totalPremium: 0,
        upgradePremium: 0,
        starPhone: '',
        callbackUrl: '',

        phoneNo: '',    // 手机号
        name1: '',      // 本人W
        name2: '',      // 配偶
        name3: '',      // 儿女
        name4: '',      // 父母
        idCard1: "",
        idCardType1: '01',
        idCard2: "",
        idCardType2: '01',
        idCard3: "",
        idCardType3: '01',
        idCard4: "",
        idCardType4: '01',
        sourcePage: '',
        school: '0', // 0非默认升级；1默认升级
        paymentCode: -1, // NSF评分

        // 回溯相关
        fromId: '82515',
        productCode: '10N01625',
        productName: '泰康2024百万医疗险',
        proposalNo: '',
        traceBackUuid: '',

        step: 'step1',
        smsCode: '',
        authFailObj: {}, // 实名认证失败
    }
}

export const checkOrderParams = (orderInfo, page) => {
    const { productKey, infoNo, mTel, channel, checked, channelCode, sourcePage, school, traceBackUuid, } = orderInfo;
    const { name1, idCard1, idCardType1, phoneNo, starPhone, relation, repay, insurance, totalPremium, upgradePremium, autoRenewalType } = orderInfo;
    const params = {
        infoNo: infoNo,
        relation: relation,
        insurance: insurance,
        paymentPlan: repay,
        planKey: productKey,
        channelId: channel,
        page: page,
        channelCode,
        sourcePage,
        traceBackUuid,
        operatorPhone: '',
        holderName: '',
        holderIdCard: '',
        holderPhone: '',
        insuredName: '',
        insuredIdCard: '',
        insuredPhone: '',
    };

    const certName = `name${relation}`;
    const insuredName = orderInfo[certName];
    if (!isPersonName(insuredName)) {
        return { code: 1, msg: '请填写正确的被保人姓名', };
    }
    params.insuredName = insuredName;

    const certNo = `idCard${relation}`;
    const insuredIdCard = orderInfo[certNo];
    const icTypeInsured = orderInfo[`idCardType${relation}`];
    if (!isCardNo(insuredIdCard)) {
        return { code: 1, msg: '请填写正确的被保人身份证号' };
    }
    params.insuredIdCard = insuredIdCard;

    if (!isPersonName(name1)) {
        return { code: 1, msg: '请填写正确的投保人姓名' };
    }
    params.holderName = name1;

    if (!isCardNo(idCard1)) {
        return { code: 1, msg: '请填写正确的投保人身份证号' };
    }
    params.holderIdCard = idCard1;

    if (!isPhoneNum(phoneNo) && !isMaskedAndT1Phone(starPhone, mTel)) {
        return { code: 1, msg: '请填写正确的手机号码' };
    }

    const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;
    params.holderPhone = phone;
    params.insuredPhone = phone;
    params.operatorPhone = mTel || phoneNo;

    const age = GetAge(idCard1)
    const age1 = GetAge(insuredIdCard)

    if (age < 18) {
        return { code: 1, msg: '投保人年龄小于18周岁，可选择为儿女投保的方式进行投保' };
    }

    if (relation != 1) {
        if (idCard1 == insuredIdCard) {
            return { code: 1, msg: '您与被保人身份证号码不能相同' };
        }
    }

    if (relation == 2) {
        const agender = (+idCard1.slice(16, 17)) % 2;
        const agender1 = (+insuredIdCard.slice(16, 17)) % 2;
        if (agender == agender1) {
            return { code: 1, msg: '配偶双方性别不能相同' };
        }
    }

    if (relation == 3 && (age - age1 < 16)) {
        return { code: 1, msg: '您与儿女年龄至少要相差16周岁' };
    }

    if (relation == 4 && (age1 - age < 16)) {
        return { code: 1, msg: '您与父母年龄至少要相差16周岁' };
    }

    const index = window.location.href.indexOf('/ZYBX/');
    let successUrl = `${window.location.href.substring(0, index)}/ZYBX/TK40/Upgrade`;
    successUrl = `${successUrl}?tb_uuid=${traceBackUuid}`;

    const obj = {
        channel,
        school,
        mTel,
        starPhone,
        upgradePremium,
        totalPremium,
        relation,
        name1,
        idCard1,
        phoneNo,
        [certNo]: insuredIdCard,
        [certName]: insuredName,
    }

    const param = url_safe_b64_encode(JSON.stringify(obj));
    successUrl = `${successUrl}&bizParams=${param}`;
    orderInfo.callbackUrl = successUrl;

    const extendParams = {
        school,
        autoRenewalType,
        successUrl,
        failUrl: window.location.href.split("?")[0],
        platformId: isInWx() ? "WX" : "WAP",
    };

    if (idCardType1 == '08') {
        extendParams.holderIdCardType = idCardType1;
    }

    if (icTypeInsured == '08') {
        extendParams.insuredIdCardType = icTypeInsured;
    }


    params.extendParams = extendParams;

    saveOrderInfo(orderInfo);

    if (!checked) {
        return { code: 2, msg: '用户协议未同意', params }; //
    }

    return { code: 0, params };
}

export const submitPreUnderwritingOrder = (params) => {
    return new Promise((resolve, reject) => {
        createTKJPreUnderwritingOrder(params).then(r => {
            const { result } = r.data || {};
            if (result == 1) {
                resolve();
            } else {
                reject({ reason: '预核保失败', msg: '预核保失败' });
            }
        }).catch(err => {
            reject({ reason: '接口出错', msg: JSON.stringify(err) });
        });
    });
}

export const submitOrderInfo = (params, orderInfo,) => {
    const v = getFailIdentity(orderInfo);
    if (v) {
        return Promise.reject({ reason: '核保失败', msg: v });
    }

    return new Promise((resolve, reject) => {
        fetchCreateTKZengXianOrder(params).then(result => {
            orderResultHandle(result, orderInfo, resolve, reject);
        }).catch(err => {
            reject({ reason: '接口出错', msg: JSON.stringify(err) });
        });
    });
}

export const submitPromoteOrderInfo = (params, orderInfo) => {
    return new Promise((resolve, reject) => {
        createPromotionOrder(params).then(result => {
            orderResultHandle(result, orderInfo, resolve, reject);
        }).catch(err => {
            reject({ reason: '接口出错', msg: JSON.stringify(err) });
        });
    });
}

const orderResultHandle = (result, orderInfo, resolve, reject) => {
    const { code, msg, data } = result.data || {};
    if (code != 2000) {
        return reject({ reason: '核保失败', msg: msg });
    }

    const { orderNo, success, msg: message, payforURL, extentMap } = data || {};
    if (orderNo) {
        orderInfo.infoNo = orderNo;
    }

    if (extentMap && extentMap.proposalNo) {
        orderInfo.proposalNo = extentMap.proposalNo || '';
    }

    if (!success) {
        let v = message;
        if (v.indexOf('不通过') >= 0 || v.indexOf('未通过') >= 0) {
            v = '实名认证未通过，请核对修改姓名和身份证号';
            saveFailIdentity(orderInfo, v);
        }
        return reject({ reason: '核保失败', msg: v });
    }
    return resolve(payforURL);
}

export const upgradeOrderInfo = (params) => {
    return new Promise((resolve, reject) => {
        upgradeTKJCubeProduct(params).then((res) => {
            const { code, msg, data } = res.data || {};
            if (code != 2000) {
                return reject({ code: 1, msg: msg });
            }
            return resolve({ code: 0 });
        }).catch(() => {
            return reject({ code: 1, msg: "接口出错" });
        });
    });
}

export const calculatePremium = (idCardNo, insurance, repay) => {
    if (!idCardNo || idCardNo.length != 18) {
        return { low: 0, high: 0 };
    }

    const birthday = idCardNo.substr(6, 8);
    const age = moment().add(1, 'days').diff(moment(birthday), 'year'); // 计算年龄
    const premiumObj = PremiumRate.find(item => item.min <= age && item.max >= age);
    const premiumObj1 = PremiumRate1.find(item => item.min <= age && item.max >= age);

    // 年龄超出投保范围
    if (!premiumObj || !premiumObj1) {
        return { low: 0, high: 0 };
    }

    const obj = repay == 0 ? premiumObj.year : premiumObj.month;
    const obj1 = repay == 0 ? premiumObj1.year : premiumObj1.month;

    const premium = insurance == 0 ? obj.data2 : obj.data1;
    const premium1 = insurance == 0 ? obj1.data2 : obj1.data1;

    return { low: premium, high: premium1 };
}

export const getPolicyVersion = (idCard, isBaseVersion = true) => {
    if (!idCard || idCard.length != 18) {
        return 'v1';
    }

    const versionList1 = [
        { min: 0, max: 50, version: 'v1' },
        { min: 51, max: 55, version: 'v2' },
        { min: 56, max: 60, version: 'v3' },
        { min: 61, max: 65, version: 'v4' },
    ]

    const versionList2 = [
        { min: 0, max: 50, version: 'v5' },
        { min: 51, max: 55, version: 'v6' },
        { min: 56, max: 60, version: 'v8' },
        { min: 61, max: 65, version: 'v7' },
    ]

    const birthday = idCard.substr(6, 8);
    const age = moment().add(1, 'days').diff(moment(birthday), 'year'); // 计算年龄
    const versionList = isBaseVersion ? versionList1 : versionList2;
    const obj = versionList.find(item => item.min <= age && item.max >= age) || { version: 'v1' };

    return obj.version;
}

export const eventTracking = (orderInfo, name, time = 0) => {
    const { action, productKey, mTel, tel, channel, phoneNo, identifier } = orderInfo;
    const map = {
        back: '返回',
        promotion: '促活跳转',
        follow: '结果跳转',
        direct: '直发',
        forward: '转发',
        result: '结果页',
        promote: '促活'
    };
    const prefix = map[action] ? `${map[action]}-` : '';
    const page = `${prefix}艾泽泰康健百万魔方${identifier}`;
    const phone = mTel || tel || (isPhoneNum(phoneNo) ? phoneNo : '');

    actionTracking({
        page: `${page}(${productKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: productKey,
        time: time,
    }).then(res => {
        const { code, data } = res;
        if (code == 2000) {
            const { mobileId } = data || {};
            mobileId && (orderInfo.mTel = mobileId);
        }
    });
}

export const loadOrderInfo = (key = 'TK40Info') => {
    return bxStorage.getObjItem(key) || null;
}

export const saveOrderInfo = (orderInfo, key = 'TK40Info') => {
    bxStorage.setObjItem(key, orderInfo);
}

export const showToast = (message = '', duration = 2000) => {
    Toast({
        message: message,
        duration: duration, // 弹窗时间毫秒
        position: 'middle',
        forbidClick: true,
    });
}

export const inputEndEditing = () => {
    const inputList = document.getElementsByTagName('input') || [];
    for (const input of inputList) {
        input.blur && input.blur();
    }
}

const adjustInputPosition = () => {
    const node = document.querySelector("#id_input_x");
    node && node.scrollIntoView && node.scrollIntoView(true);
};

export const createNoticeList = () => {
  const noticeList = []
  for (let idx = 0; idx < 100; idx++) {
    const tailNumber = (Math.random() * 8999 + 1000).toFixed(0)
    noticeList.push(tailNumber)
  }
  return noticeList
}

export const dispatchWithBitwise = (v = 0, message = "", duration = 2000) => {
    // 1 toast(1) 2收键盘(1<<1) 4输入框位置调整(1<<2)
    if (v & 0b1) {
        showToast(message, duration);
    }

    if (v & 0b10) {
        inputEndEditing();
    }

    if (v & 0b100) {
        adjustInputPosition();
    }
};

const saveFailIdentity = (orderInfo, reason) => {
    const { relation, name1, idCard1,} = orderInfo;
    const certName = orderInfo[`name${relation}`];
    const certNo = orderInfo[`idCard${relation}`];
    const md5 = CryptoJS.MD5(`${name1}${idCard1}${certName}${certNo}`).toString();
    orderInfo.authFailObj[md5] = reason;
    console.log(md5);
}

const getFailIdentity = (orderInfo) => {
    const { relation, name1, idCard1,} = orderInfo;
    const certName = orderInfo[`name${relation}`];
    const certNo = orderInfo[`idCard${relation}`];
    const md5 = CryptoJS.MD5(`${name1}${idCard1}${certName}${certNo}`).toString();
    return orderInfo.authFailObj[md5]
}
