<template>
	<div class="container_2303061710">
		<div style="overflow: auto;flex: 1;">
			<div class="header">
				<img src="~@/assets/imgs/NW/NWTK03/promotion1.png">
				<p class="count-down">
					<span>{{ minutes }}</span>:
					<span>{{ seconds }}</span>后订单失效，请尽快完成支付
				</p>
			</div>
			<div class="section">
				<div class="section-header">保障计划</div>
				<div v-for="(item, index) in planSummary.slice(0, 5)" :key="index" class="section-item">
					<span>{{ item.key }}</span>
					<span v-html="item.value" class="section-item-point"></span>
				</div>
			</div>
			<div class="section">
				<div class="section-header">
					<span>投保人</span>
					<span @click="onEdit" class="section-header-edit">编辑</span>
				</div>
				<div v-for="(item, index) in holderInfo" :key="index" class="section-item">
					<span>{{ item.key }}</span>
					<span class="section-item-value">{{ item.value }}</span>
				</div>
			</div>
			<div class="section">
				<div class="section-header">
					<span>被保人</span>
					<span @click="onEdit" class="section-header-edit">编辑</span>
				</div>
				<div v-for="(item, index) in insuredInfo" :key="index" class="section-item">
					<span>{{ item.key }}</span>
					<span class="section-item-value">{{ item.value }}</span>
				</div>
			</div>
			<div class="premium-x">
				月缴更轻松，每{{ premiumObj.name }}<span class="number">{{ premiumObj.val }}</span>元
			</div>
			<div class="policy-x">
				<div>
					我确认并接受
					<span class="view-txt" @click.stop="viewAction('健康告知', true)">《健康告知》</span>
					<span class="view-txt" @click.stop="viewAction('免责声明', true)">《免责声明》</span>
					<span class="view-txt" @click.stop="viewAction('产品说明及投保须知', true)">《产品说明及投保须知》</span>
					<span class="view-txt" @click.stop="viewAction('保险条款', true)">《保险条款》</span>
					<span class="view-txt" @click.stop="viewAction('特别约定', true)">《特别约定》</span>
					<span class="view-txt" @click.stop="viewAction('重要提示', true)" v-if="importantTipVisible">《重要提示》</span>
					<span class="view-txt" @click.stop="viewAction('个人信息保护政策', true)">《个人信息保护政策》</span>
					<span class="view-txt" @click.stop="viewAction('客户告知书', true)">《客户告知书》</span>
					<span class="view-txt" @click.stop="viewAction('费率表', false)">《费率表》</span>。
				</div>
			</div>
		</div>
		<div class="bottom-button" @click="onSubmit">立即支付</div>
		<UUPolicyPopup :obj="policyObj" :orderInfo="orderInfo"></UUPolicyPopup>
		<UUTabPopup :obj="policyObj" @ok="onAcceptPolicy" :orderInfo="orderInfo"></UUTabPopup>
	</div>
</template>

<script>
import { bxStorage, } from "@/utils/store_util";
import { planSummary1, } from "./src";
import UUTabPopup from "./components/UUTabPopup";
import UUPolicyPopup from "./components/UUPolicyPopup";
import { domainPathMap, } from "@/views/ZYBX/src";
import { fetchPromoteOrderInfo } from "@/api/insurance-api";
import { url_safe_b64_encode } from "@/assets/js/common";
import { calculatePremium, showToast, eventTracking, submitPromoteOrderInfo, createOrderInfo, getPolicyVersion } from "./function";
import { getDailyRateFromMonthlyRate } from '@/utils/rate';

export default {
	name: "Promotion",
	components: { UUPolicyPopup, UUTabPopup },
	data() {
		const orderInfo1 = {
			holderName: '',
			holderIdCard: '',
			holderPhone: '',
			insuredName: '',
			insuredIdCard: '',
			expireTime: 0,
		};

		const orderInfo = createOrderInfo();

		Object.assign(orderInfo, orderInfo1);

		return {
			planSummary: planSummary1,
			minutes: 0,
			seconds: 0,
			orderInfo,
			policyObj: { v: false, page: '', v1: false, page1: '', belongs: 'v1' },
		}
	},
	computed: {
		premiumObj() {
			if (!this.orderInfo.totalPremium) {
				return {
					name: '月',
					val: '0.02',
				};
			}
			return getDailyRateFromMonthlyRate(this.orderInfo.totalPremium);
		},

		holderInfo() {
			const { holderName, holderIdCard, holderPhone } = this.orderInfo;
			return [
				{ key: '姓名', value: holderName },
				{ key: '身份证', value: holderIdCard },
				{ key: '手机号', value: holderPhone },
			]
		},

		insuredInfo() {
			const { insuredName, insuredIdCard, insurance } = this.orderInfo;
			return [
				{ key: '姓名', value: insuredName },
				{ key: '身份证', value: insuredIdCard },
				{ key: '有无社保', value: insurance == 1 ? '有' : '无' },
			]
		},
		importantTipVisible() {
			const { insuredIdCard } = this.orderInfo;
			const belongs = getPolicyVersion(insuredIdCard, this.orderInfo.isLowPricePage)
			return belongs != 'v4' && belongs != 'v7'
		}
	},

	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.init();
	},

	methods: {
		//初始化
		init() {
			let inQry = this.$route.query || {}; // 必须：加密的infoNo

			const { channel, infoNo } = inQry;
			this.orderInfo.channel = channel || '1';
			this.orderInfo.identifier = 'TK40Promotion1';
			this.orderInfo.action = 'promote';
			this.orderInfo.source = 'promote';
			this.orderInfo.expireTime = bxStorage.getRawItem('TK40ExpireTime') || 0;

			this.fetchOrderInfo(infoNo);
		},

		fetchOrderInfo(infoNo) {
			if (!infoNo) {
				this._entryBehaviorReport();
				return this.editOrderInfo(true);
			}

			this.$toast.loading({
				message: '正在请求数据\n请稍候',
				forbidClick: true,
				duration: 0,
			});

			fetchPromoteOrderInfo(infoNo).then(res => {
				this.$toast.clear(true);
				const data = res.data || {};
				this.orderInfoHandle(data);
			}).catch(err => {
				this.$toast.clear(true);
				this.editOrderInfo(true);
			}).finally(() => {
				this._entryBehaviorReport();
			});
		},

		orderInfoHandle(data) {
			Object.assign(this.orderInfo, data);

			const { operatorPhone, insuredIdCard, insurance, repay, infoNo, } = this.orderInfo;
			if (!infoNo) {
				return this.editOrderInfo(true);
			}

			this.orderInfo.mTel = operatorPhone;
			this.orderInfo[`idCard${this.orderInfo.relation}`] = insuredIdCard
			this.orderInfo.totalPremium = calculatePremium(insuredIdCard, insurance, repay).low;

			this.startCountdown();
		},

		startCountdown() {
			if (Date.now() - this.orderInfo.expireTime > 0) {
				this.orderInfo.expireTime = Date.now() + 60 * 60 * 1000;
				bxStorage.setRawItem('TK40ExpireTime', this.orderInfo.expireTime);
			}

			requestAnimationFrame(() => {
				const diff = Math.floor((this.orderInfo.expireTime - Date.now()) / 1000);
				this.minutes = ('00' + Math.floor(diff / 60)).slice(-2);
				this.seconds = ('00' + (diff - this.minutes * 60)).slice(-2);

				this.startCountdown();
			})
		},

		onEdit() {
			this._actionTracking('点击编辑按钮');
			this.editOrderInfo();
		},

		editOrderInfo(delay, msg) {
			const { channel, mTel } = this.orderInfo;

			const params = { channel, mTel, action: 'promotion', source: 'promotion' }
			const bizParams = url_safe_b64_encode(JSON.stringify(params));
			const href = domainPathMap['TK40Index2'] + `?bizParams=${bizParams}`;

			if (!delay) {
				return setTimeout(() => {
					return window.location.href = href;
				}, 250);
			}

			showToast(msg || '该订单无效，正在跳转到首页');

			setTimeout(() => {
				return window.location.href = href;
			}, 2000);
		},

		onSubmit() {
			const { infoNo, channel, productKey } = this.orderInfo;

			this._actionTracking('点击促活立即支付按钮');

			this.$toast.loading({
				message: '订单提交中\n请稍候',
				forbidClick: true,
				duration: 0,
			});

			const params = { infoNo: infoNo, page: `infoKey:${productKey}&page:promotion1`, channelId: channel };
			submitPromoteOrderInfo(params, this.orderInfo).then(url => {
				this.$toast.clear(true);
				return window.location.href = url;
			}).catch(err => {
				const message = err.msg || '';
				this.editOrderInfo(true, message);
			});
		},

		onAcceptPolicy() {
			this.onSubmit();
		},

		viewAction(name, isPolicy) {
			this.policyObj[isPolicy ? 'v1' : 'v'] = true;
			this.policyObj[isPolicy ? 'page1' : 'page'] = name;
		},

		_entryBehaviorReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('支付促活页', domContentLoadedEventEnd - fetchStart);
		},

		_actionTracking(name, time = 0) {
			eventTracking(this.orderInfo, name, time);
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
.container_2303061710 {
	height: 100%;
	box-sizing: border-box;
	background-color: #f2f2f2;
	display: flex;
	flex-direction: column;

	img {
		max-width: 100%;
	}

	.header {
		position: relative;

		.count-down {
			position: absolute;
			top: 0.90rem;
			left: 0.20rem;
			font-size: 0.14rem;
			color: #ffffff;
			display: flex;
			align-items: center;
			font-size: 0.14rem;
			font-weight: 700;

			span {
				padding: 0.04rem 0.05rem;
				text-align: center;
				border-radius: 0.04rem;
				background-color: #fd5b02;
			}
		}
	}

	.section+.section {
		margin-top: 0.1rem;
	}

	.section {
		background-color: #ffffff;

		.section-header {
			padding: 0.15rem 0.1rem;
			font-size: 0.17rem;
			font-weight: 700;

			display: flex;
			align-items: center;
			justify-content: space-between;

			.section-header-edit {
				color: #0b78ff;
			}
		}

		.section-item {
			padding: 0 0.1rem 0.15rem;
			font-size: 0.14rem;
			font-weight: 500;
			color: #666666;

			display: flex;
			align-items: center;
			justify-content: space-between;

			.section-item-value {
				color: #191919;
				font-size: 0.16rem;
				font-weight: 500;
			}

			.section-item-point {
				color: #fd8a25;
				font-weight: bold;
			}
		}
	}

	.premium-x {
		margin: 0.15rem 0;
		color: #333333;
		font-size: 0.14rem;
		text-align: center;

		.number {
			font-size: 0.16rem;
			font-weight: 700;
			color: #ff491d;
		}
	}

	.policy-x {
		padding: 0.1rem;
		font-size: 0.13rem;
		color: #333333;
		line-height: 1.6;
		text-align: justify;
		background-color: #ffffff;

		.view-txt {
			color: #ff8c41;
			font-weight: 500;
		}
	}

	.bottom-button {
		padding: 0.16rem 0;
		color: #ffffff;
		font-size: 0.17rem;
		font-weight: 700;
		text-align: center;
		background-color: #fd8a25;
	}
}
</style>
