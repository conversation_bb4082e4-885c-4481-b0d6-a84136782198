<template>
	<div class="container_2208310930">
		<div id="id_header_banner" class="header">
			<img :src="bannerUrl" alt="">
		</div>
		<div class="main-x">
			<UUInputBox :orderInfo="orderInfo" @input="onTextInput"></UUInputBox>
			<div class="premium">
				<span class="number">{{ premiumObj.count }}</span>元{{ premiumObj.suffix }}/月<span class="light-txt">（依据费率表）</span>
			</div>
			<div id="id_action_button" class="actionButton" @click="onSubmitClick">
				立即投保
				<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
			</div>
			<div class="policy-box">
				<van-icon class="policy-icon" :name="orderInfo.checked ?'checked':'circle'" @click="orderInfo.checked = !orderInfo.checked" />
				我已阅读并同意
				<span class="read" @click.stop="onViewPolicy('健康告知',true)">《健康告知》</span>
				<span class="read" @click.stop="onViewPolicy('产品说明及投保须知',true)">《产品说明及投保须知》</span>
				<span class="read" @click.stop="onViewPolicy('保险条款',true)">《保险条款》</span>
				<span class="read" @click.stop="onViewPolicy('特别约定',true)">《特别约定》</span>
				<span class="read" @click.stop="onViewPolicy('免责声明',true)">《免责声明》</span>
				<span class="read" @click.stop="onViewPolicy('重要提示',true)" v-if="importantTipVisible">《重要提示》</span>
				<span class="read" @click.stop="onViewPolicy('隐私政策',true)">《隐私政策》</span>
                <span class="read" @click.stop="onViewPolicy('泰康在线授权协议',true)">《泰康在线授权协议》</span>
                <span class="read" @click.stop="onViewPolicy('代收扣款服务协议',true)">《代收扣款服务协议》</span>
                <span class="read" @click.stop="onViewPolicy('用户授权协议',true)">《用户授权协议》</span>
				<br>
				<span>
					保费与年龄和有⽆社保相关，此保费以0-65周岁为例，详⻅
					<span class="read" @click.stop="onViewPolicy('费率表',)">《费率表》</span>
				</span>
			</div>
		</div>
		<HHPlan :obj="policyObj" :orderInfo="orderInfo" @click="onViewPolicy('保障详情')"></HHPlan>
		<van-tabs v-model="currentTab" scrollspy sticky>
			<van-tab key="产品特色" name="产品特色" title="产品特色">
				<div class="section">
					<!-- <div class="section-title">产品特色</div> -->
					<img :src="contentUrl" alt="产品特色">
				</div>
			</van-tab>
			<van-tab key="理赔说明" name="理赔说明" title="理赔说明">
				<div class="section">
					<div class="section-title">理赔说明</div>
					<HHClaimProcess></HHClaimProcess>
				</div>
			</van-tab>
		</van-tabs>
		<div class="copyright">
			<div class="wrapper">
				<img src="@/assets/imgs/logo/lechengshi_1.png">
				<img src="@/assets/imgs/logo/taikangzaixian.png">
			</div>
			<p>产品名称：全能保·百万医疗险</p>
			<p>本产品由泰康在线财产保险股份有限公司授权<br>乐城十保险经纪有限公司销售服务</p>
			<p style="margin-top: 0.10rem;">互联网专属产品<br>本页面由乐城十保险经纪有限公司提供</p>
		</div>
		<div v-if="bottomButtonVisible" class="bottomButton" @click="onSubmitClick">
			立即投保<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
		</div>
		<UUPolicyPopup :obj="policyObj" :orderInfo="orderInfo"></UUPolicyPopup>
		<UUFailPopup :obj="failObj" @click="jumpToFailLink"></UUFailPopup>
		<UUSilentPopup :obj="policyObj" :obj1="orderInfo" @click="silentUpgrade"></UUSilentPopup>
		<UUTabPopup :obj="policyObj" @ok="onAcceptPolicy" :orderInfo="orderInfo"></UUTabPopup>
		<HHWarnHint :obj="orderInfo" @policy="onViewPolicy('客户告知书', false)"></HHWarnHint>
		<TKRecord1 ref="ref_record" :recordObj="orderInfo"></TKRecord1>
	</div>
</template>

<script>
import moment from "moment";
import { isInWx, isMaskedAndT1Phone, isPhoneNum, TraceLogInfoKeys, url_safe_b64_encode, url_safe_b64_decode } from "@/assets/js/common";
import { checkOrderParams, createOrderInfo, eventTracking, inputEndEditing, loadOrderInfo, saveOrderInfo, showToast, submitOrderInfo, getPolicyVersion, } from "./function";
import HHPlan from "./components/HHPlan";
import HHClaimProcess from "./components/HHClaimProcess";
import UUFailPopup from "./components/UUFailPopup";
import UUTabPopup from "./components/UUTabPopup";
import UUInputBox from "./components/UUInputBox1";
import UUSilentPopup from "./components/UUSilentPopup";
import UUPolicyPopup from "./components/UUPolicyPopup";
import HHWarnHint from "./components/HHWarnHint";
import TKRecord1 from "@/views/components/TKRecord1";
import { fetchInfoByOrderNo, fetchRoundRobinWithAgeCommonFail, fetchStarPhoneV4, } from "@/api/insurance-api";
import { domainPathMap } from "@/views/ZYBX/src";
import ImgUrl1 from '@/assets/imgs/TaiKang/TK40/img01.png'
import ImgUrl2 from '@/assets/imgs/TaiKang/TK40/img01_1.png'

import ImgUrl3 from '@/assets/imgs/TaiKang/TK40/img02.png'
import ImgUrl4 from '@/assets/imgs/TaiKang/TK40/img02.png'
import { onClickNwFilingNumber } from '@/utils/filing_number';

export default {
	name: "Index11", // 推广版
	components: {
		HHPlan, HHClaimProcess, HHWarnHint,
		UUPolicyPopup, UUInputBox, UUTabPopup,
		TKRecord1, UUSilentPopup, UUFailPopup,
	},
	data() {
		const orderInfo = createOrderInfo('direct');
		return {
			orderInfo,
			currentTab: '',	   // 切换的tab
			autoSubmit: true,	// 自动拉起支付
			bottomButtonVisible: false,
			policyObj: { v: false, page: '', v1: false, page1: '', v2: false, isAccept: false, belongs: 'v1' },
			failObj: { visible: false, showTimer: false, path: '' },
		}
	},
	computed: {
		orderPage() {
			const { source, action, school, identifier } = this.orderInfo;
			const productKey = school == 1 ? TraceLogInfoKeys.az_tkj_qn_by_bk_jd_default_cube_base : TraceLogInfoKeys.az_tkj_qn_by_bk_jd_cube_base;
			return `infoKey:${productKey}&page:${identifier}&act:${action}&src:${source}`;
		},
		// 投保费率数据
		premiumObj() {
			const { totalPremium } = this.orderInfo;
			if (totalPremium) {
				return { count: totalPremium, suffix: '' };
			}
			return { count: '0.60', suffix: '' };
		},
		bannerUrl() {
			const belongs = getPolicyVersion(this.orderInfo[`idCard${this.orderInfo.relation}`], this.orderInfo.isLowPricePage)
			return belongs == 'v4' ? ImgUrl2 : ImgUrl1
		},
		contentUrl() {
			const belongs = getPolicyVersion(this.orderInfo[`idCard${this.orderInfo.relation}`], this.orderInfo.isLowPricePage)
			return belongs == 'v4' ? ImgUrl4 : ImgUrl3
		}, 
		importantTipVisible() { 
			const belongs = getPolicyVersion(this.orderInfo[`idCard${this.orderInfo.relation}`], this.orderInfo.isLowPricePage)
			return belongs != 'v4' && belongs != 'v7'
		}
	},
	created() {
		this.init();
	},
	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		const enterUpgrade = loadOrderInfo('TK40_Upgrade') || 0;
		if (enterUpgrade) {
			this.fetchInfoByOrderNo('', true);
		}

		this.$nextTick(() => { // 监听滚动事件
			this.actionButtonObserver();
			this.addObserverForBanner();
		});
	},
	methods: {
		onClickNwFilingNumber,
		SetIseeBiz(value) {
			this.orderInfo.traceBackUuid = value;
		},
		actionButtonObserver() {
			if (window.IntersectionObserver) {
				const observer = new IntersectionObserver((entries, observer) => {
					this.bottomButtonVisible = !entries[0].isIntersecting;
				}, { threshold: 0.10 });

				const buttonNode = document.getElementById('id_action_button');
				buttonNode && observer.observe(buttonNode);
			}
		},
		addObserverForBanner() {
			if (window.IntersectionObserver) {
				const observer = new IntersectionObserver((entries, observer) => {
					const entry = entries[0];
					if (!entry.isIntersecting) {
						observer.unobserve(entry.target);
						this.scrollTimeReport();
					}
				}, { threshold: 0.10 });

				const buttonNode = document.getElementById('id_header_banner');
				buttonNode && observer.observe(buttonNode);
			}
		},

		onAcceptPolicy() {
			this.orderInfo.checked = true;
			if (this.orderInfo.v2) {
				return;
			}
			if (this.policyObj.isAccept) {
				return this.policyObj.v2 = true;
			}
			this.onSubmitClick();
		},

		silentUpgrade(value) {
			this._actionTracking(`首页：${value ? '默认升级' : '正常升级'}`);
			this.orderInfo.school = value ? 1 : 0;
			this.orderInfo.infoKey = value ? TraceLogInfoKeys.az_tkj_qn_by_bk_jd_default_cube_base : TraceLogInfoKeys.az_tkj_qn_by_bk_jd_cube_base;
			this.onSubmitOrder();
		},
		// 初始化
		init() {
			const query = this.$route.query || {};
			query.source = query.source || 'direct'; // source要以链接携带的参数为准
			query.action = query.action || 'direct'; // action要以链接携带的参数为准
			query.sourcePage = query.sourcePage || ''; // sourcePage要以链接携带的参数为准
			query.channelCode = query.cld || query.channelCode || '';

			const inStore = loadOrderInfo() || {};
			Object.assign(this.orderInfo, inStore, query);

			try {
				if (query.bizParams) {
					const params = JSON.parse(url_safe_b64_decode(query.bizParams));
					Object.assign(this.orderInfo, params);
				}
			} catch (error) {

			}

			this.orderInfo.identifier = 'TK40Index11';
			this.orderInfo.productKey = TraceLogInfoKeys.az_tkj_qn_by_bk_jd_default_cube_base;
			this.orderInfo.school = '1';
			this.orderInfo.isLowPricePage = true;
			this.orderInfo.hintVisible = false;
			this.orderInfo.checked = this.orderInfo.channel >= 1000;
            this.orderInfo.autoRenewalType = this.orderInfo.autoRenewalType == 1 ? 1 : 0;

			const { phoneNo, starPhone, mTel } = this.orderInfo;
			if (!isPhoneNum(phoneNo) && isMaskedAndT1Phone(starPhone, mTel)) {
				this.orderInfo.phoneNo = starPhone;
			} else {
				this.orderInfo.starPhone = '';
			}

			this.fetchPhoneNumber();
		},
		// 手机号解密
		fetchPhoneNumber() {
			const { m, phoneNo, starPhone, mTel } = this.orderInfo;
			if (!m || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
				return this._entryReport();
			}

			const params = { encryptContent: m };
			fetchStarPhoneV4(params).then(res => {
				const { encryptPhone, showPhone } = res.data;
				if (isMaskedAndT1Phone(showPhone, encryptPhone)) {
					this.orderInfo.mTel = encryptPhone;
					this.orderInfo.starPhone = showPhone;
					saveOrderInfo(this.orderInfo);
				}
			}).finally(() => {
				return this._entryReport();
			});
		},
		// 输入框输入，自动拉起下单
		onTextInput() {
			saveOrderInfo(this.orderInfo);

			if (this.autoSubmit) {
				this.onSubmitClick('AUTO');
			}
		},
		onPhoneBoxClick() {
			saveOrderInfo(this.orderInfo);
		},
		// 点击提交按钮
		onSubmitClick(from) {
			const { code, msg, } = checkOrderParams(this.orderInfo, this.orderPage);
			if (from != 'AUTO' || code == 0 || msg == '用户协议未同意') {
				this._actionTracking('点击立即投保按钮');
			}
			if (code != 0) {
				if (msg == '用户协议未同意') {
					this.showToastAndEndEdit();
					return this.onViewPolicy('健康告知', true, true);
				}
				return (from != 'AUTO') && this.showToastAndEndEdit(msg);
			}

			this.showToastAndEndEdit();
			this.policyObj.v2 = true;
		},

		onSubmitOrder() {
			this.showToastAndEndEdit();

			this.$toast.loading({
				message: '订单提交中\n请稍候',
				forbidClick: true,
				duration: 0,
			});

			if (isInWx()) {
				this.orderInfo.paymentCode = 0;
			} else {
				this.orderInfo.paymentCode = 1;
			}
			this.createSubmitOrder();
		},

		createSubmitOrder() {
			this._actionTracking('点击立即领取按钮');

			const { code, msg, params } = checkOrderParams(this.orderInfo, this.orderPage);
			if (this.orderInfo.school != 1) {
				params.planKey = TraceLogInfoKeys.az_tkj_qn_by_bk_jd_cube_base;
			}

			params.extendParams = JSON.stringify(params.extendParams || {});

			submitOrderInfo(params, this.orderInfo,).then(url => {
				this.$refs.ref_record && this.$refs.ref_record.uploadVideo();
				setTimeout(() => {
					this.$toast.clear(true);
					window.location.href = url;
				}, 600);
			}).catch(err => {
				this.$toast.clear(true);
				const message = err.msg || '';
				if (message.includes('未通过') || this.orderInfo.channel < 1000) {
					return this.showToastAndEndEdit(message);
				}
				if (!message.includes('不能重复购买') && !message.includes('已存在保单') && !message.includes('投保份数')) {
					return this.fetchFailPath();
				}
				this.fetchInfoByOrderNo(message);
			}).finally(() => {
				saveOrderInfo(this.orderInfo);
			});
		},

		// 根据orderNo查询订单信息
		fetchInfoByOrderNo(message, enterUpgrade) {
			const { relation, callbackUrl } = this.orderInfo;
			const idCardNo = this.orderInfo[`idCard${relation}`];
			const params = { infoKey: TraceLogInfoKeys.az_tkj_qn_by_bk_jd_cube_base, insuredIdCard: idCardNo };

			fetchInfoByOrderNo(params).then(r => {
				const { code, data } = r.data;
				if (code != 2000 || !data) {
					return !enterUpgrade && this.fetchFailPath();
				}

				const { infoNo } = data || {};
				if (infoNo && callbackUrl && callbackUrl.indexOf('http') >= 0) {
					const params = `infoNo=${infoNo}`;
					let href = callbackUrl.replace(/&?infoNo=[^?&]*/ig, '');
					href = href.indexOf('?') > 0 ? `${href}&${params}` : `${href}?${params}`;

					return window.location.href = href;
				}

				return !enterUpgrade && this.fetchFailPath();
			}).catch((err) => {
				return !enterUpgrade && this.fetchFailPath();
			});
		},

		fetchFailPath() {
			const { channel, phoneNo, mTel, relation } = this.orderInfo;

			const params = {
				channelId: channel,
				idCard: this.orderInfo[`idCard${relation}`],
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				robinKey: 'tk_mf_common_fail',
				currentPage: 'TK40',
			}

			fetchRoundRobinWithAgeCommonFail(params).then(result => {
				const { path } = result.data || {};
				this.failObj.path = path;
			}).finally(() => {
				if (this.failObj.path == 'NotFound') {
					return this.showToastAndEndEdit('投保失败，您可以选择为家人投保');
				}
				
				if (!this.failObj.path) {
					this.failObj.path = 'NWDD01Index1';
				}

				this.failObj = { ...this.failObj, visible: true, showTimer: true, };
				this._actionTracking(`显示核保失败弹窗(${this.failObj.path})`);
			});
		},

		jumpToFailLink() {
			let path = this.failObj.path;
			if (!domainPathMap[path]) {
				path = 'NWDD01Index1';
			}
			this._actionTracking(`点击核保失败图片(${path})`);

			const { channel, relation, mTel, phoneNo, channelCode, starPhone, identifier, name1, idCard1 } = this.orderInfo;

			const params = {
				channel, cld: channelCode, mTel, relation, source: identifier, action: 'forward',
				name1, idCard1, [`name${relation}`]: this.orderInfo[`name${relation}`], [`idCard${relation}`]: this.orderInfo[`idCard${relation}`]
			}

			if (isPhoneNum(phoneNo)) {
				params.phoneNo = phoneNo;
				params.starPhone = '';
			} else if (isMaskedAndT1Phone(starPhone, mTel)) {
				params.phoneNo = '';
				params.starPhone = starPhone;
			}

			const bizParams = url_safe_b64_encode(JSON.stringify(params));

			setTimeout(() => {
				const href = domainPathMap[path];
				window.location.href = `${href}?bizParams=${bizParams}`;
			}, 250);
		},

		onViewPolicy(name, isPolicy, isAccept) {
			this.policyObj.isAccept = isAccept;
			this.policyObj.belongs = 'v1';
			this.policyObj[isPolicy ? 'v1' : 'v'] = true;
			this.policyObj[isPolicy ? 'page1' : 'page'] = name;
		},

		scrollTimeReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd } = timing || {};
			const detentionTime = moment() - domContentLoadedEventEnd;
			this._actionTracking('首页滚动', detentionTime);
		},
		_entryReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);
		},
		_actionTracking(name, time = 0) {
			eventTracking(this.orderInfo, name, time);
		},
		showToastAndEndEdit(message) {
			message && showToast(message);
			inputEndEditing();
			document.body.scrollTop = document.documentElement.scrollTop = 300;
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2208310930 {
		width: 3.75rem;
		min-height: 100%;
		font-size: 0.15rem;
		background-color: #f8f8f8;

		img {
			display: block;
			max-width: 100%;
		}

		.section {
			background-color: #ffffff;

			.section-title {
				display: flex;
				justify-content: center;
				align-items: center;

				color: #333333;
				font-size: 0.18rem;
				font-weight: 500;
				line-height: 0.45rem;

				&::before,
				&::after {
					content: " ";
					width: 0.55rem;
					height: 0.13rem;
					background: no-repeat center/100%;
				}

				&::before {
					margin-right: 0.1rem;
					background-image: url("~@/assets/imgs/common/icon_needle_left.png");
				}

				&::after {
					margin-left: 0.1rem;
					background-image: url("~@/assets/imgs/common/icon_needle_right.png");
				}
			}
		}

		.main-x {
			background-color: #ffffff;
			margin: -0.65rem 0.1rem 0.12rem;
			position: relative;
			border-radius: .12rem;

			.premium {
				margin: 0.15rem 0 0.1rem;
				font-size: 0.16rem;
				font-weight: 500;
				text-align: center;

				.number {
					margin: 0 0.05rem;
					font-size: 0.2rem;
					color: #ff4509;
				}

				.light-txt {
					font-size: 0.12rem;
					color: #b3b3b3;
				}
			}

			.actionButton {
				position: relative;
				margin: 0.15rem auto;
				padding: 0.15rem 0;
				width: 3rem;
				border-radius: 999px;
				box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
				background: linear-gradient(
					270deg,
					rgb(255, 16, 46),
					rgb(253, 123, 69)
				);
				animation: button_animate 1.35s linear infinite;

				font-size: 0.2rem;
				color: #ffffff;
				font-weight: 700;
				text-align: center;

				.hand {
					position: absolute;
					top: 0.25rem;
					left: 75%;
					width: 18%;
					animation: hand_animate 1s linear infinite;
				}
			}

			.policy-box {
				padding: 0.08rem;
				border-radius: 0.06rem;
				background-color: #ffffff;

				font-size: 0.13rem;
				color: #666666;
				line-height: 0.2rem;
				text-align: justify;

				.policy-icon {
					color: #ff8c41;
					font-size: 0.16rem;
					vertical-align: -0.01rem;
				}

				.read {
					color: #ff8c41;
					font-weight: 500;
				}
			}
		}

		.bottomButton {
			position: fixed;
			inset: auto 0 0.25rem 0;
			margin: 0 auto;
			padding: 0.15rem 0;
			width: 3rem;
			font-size: 0.2rem;
			color: #ffffff;
			font-weight: 700;
			text-align: center;
			border-radius: 999px;
			box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
			background: linear-gradient(
				270deg,
				rgb(255, 16, 46),
				rgb(253, 123, 69)
			);
			animation: button_animate 1.35s linear infinite;

			.hand {
				position: absolute;
				top: 0.25rem;
				left: 75%;
				width: 18%;
				animation: hand_animate 1s linear infinite;
			}
		}

		.copyright {
			padding: 0 0 1rem;
			color: #969799;
			font-size: 0.12rem;
			line-height: 1.6;
			text-align: center;
			background-color: #f8f8f8;

			.wrapper {
				padding: 0.2rem 0 0.15rem;
				display: flex;
				align-items: center;
				justify-content: space-evenly;

				img {
					height: 0.40rem;
				}
			}
		}

		@keyframes button_animate {
			0% {
				transform: scale(1);
			}
			40% {
				transform: scale(1);
			}
			70% {
				transform: scale(0.95);
			}
			100% {
				transform: scale(1);
			}
		}

		@keyframes hand_animate {
			0% {
				transform: translate(-0.1rem, -0.1rem);
			}
			45% {
				transform: translate(0.1rem, 0);
			}
			70% {
				transform: translate(0.1rem, 0);
			}
			100% {
				transform: translate(-0.1rem, -0.1rem);
			}
		}
	}
</style>
