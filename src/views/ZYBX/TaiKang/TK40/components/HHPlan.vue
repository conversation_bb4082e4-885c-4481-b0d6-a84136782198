<template>
	<div class="container_2211020950">
		<div class="header">
			<span class="header-title">保障内容</span>
			<span class="header-more" @click="onDetailClick">查看详情</span>
		</div>
		<div class="feature-x">
			<div v-for="item in planFeatures" :key="item" class="feature-item">
				<van-icon color="#FF4509" name="gem" />
				{{ item }}
			</div>
		</div>
		<div class="summary-x">
			<div v-for="(item) in planSummary" :key="item.key" class="summary-item">
				<span>{{ item.key }}</span>
				<span class="summary-item-value" v-html="item.value"></span>
			</div>
		</div>
		<p class="mark" v-if="belongs == 'v1'">*本页面保额责任等信息以0-50岁为例，具体详见保障详情</p>
		<div v-if="isLong" :style="{'--deg':showAll ? '180deg' : '0deg'}" class="showAll" @click="showAll= !showAll">
			<span>{{ showText }}</span>
		</div>
	</div>
</template>

<script>
import { planFeatures, planSummaryObj, planSummary1} from "../src";
import { getPolicyVersion } from "../function";
export default {
	name: "HHPlan",
	props: { obj: Object, orderInfo: Object },
	data() {
		const isLong = planSummary1.length > 100;
		return {
			isLong,
			planFeatures,
			showAll: false,
		}
	},
	computed: {
		planSummary() {
            const belongs = getPolicyVersion(this.orderInfo[`idCard${this.orderInfo.relation}`], this.orderInfo.isLowPricePage)
			return planSummaryObj[belongs] || planSummary1;
			// if (this.showAll) {
			// 	return planSummary;
			// }
			// return planSummary.slice(0, 100);
		},
        belongs(){
            const belongs = getPolicyVersion(this.orderInfo[`idCard${this.orderInfo.relation}`], this.orderInfo.isLowPricePage)
            return belongs || 'v1'
        }, 

		showText() {
			return this.showAll ? '收起' : '查看更多';
		},
	},
	methods: {
		onDetailClick() {
			this.$emit('click',);
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2211020950 {
		color: #333333;
		font-size: 0.14rem;
		background-color: #ffffff;

		.header {
			display: flex;
			align-items: center;
			justify-content: space-between;

			padding: 0.1rem 0.15rem;
			font-weight: 500;
			border-bottom: #f2f2f2 1px solid;

			.header-title {
				font-size: 0.18rem;
			}

			.header-more {
				color: #ff4509;
			}
		}

		.feature-x {
			display: flex;
			align-items: center;
			justify-content: space-around;

			.feature-item {
				padding: 0.12rem 0;
				color: #555555;
				font-size: 0.14rem;
				font-weight: 500;
			}
		}

		.mark {
			margin: 0 0.15rem;
			padding: 0.1rem;
			font-size: 0.12rem;
			line-height: 1.5;
			color: #999999;
			border-radius: 0.06rem;
			background-color: #f3f3f3;
		}

		.summary-x {
			.summary-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0.1rem 0.15rem;

				.summary-item-value {
					color: #999999;
					flex-shrink: 0;
					line-height: 1.5em;
				}
			}

			.summary-item + .summary-item {
				border-top: #f2f2f2 1px dashed;
			}
		}

		.showAll {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #ff4509;
			line-height: 0.35rem;
			font-size: 0.13rem;

			&::after {
				content: " ";
				margin-left: 0.05rem;
				height: 0.08rem;
				width: 0.08rem;
				background: url("~@/assets/imgs/common/sprites.png") no-repeat 0 -0.32rem;
				background-size: 1.6rem 3.2rem;
				transition: 0.25s;
				transform: rotate(var(--deg));
			}
		}
	}
</style>
