<template>
	<van-popup v-model="visible" class="container_2211031700" position="bottom" round @close="hintClose">
		<img class="header" src="@/assets/imgs/TaiKang/tk_slogan_3.png">
		<div class="body">
			<div class="title">温馨提示</div>
			<div class="content">
				<p>您已进入泰康在线财险投保流程，请您仔细阅读保险条款、产品说明及投保须知、健康告知、<span class="policy" @click="onReadPolicy">客户告知书</span>等信息，为维护您的合法权益，您的操作轨迹将被记录。</p>
			</div>
		</div>
		<div class="footer" @click="closeAction">
			我知道了
		</div>
	</van-popup>
</template>

<script>

export default {
	name: "HHWarnHint", // 重要提醒
	props: { obj: Object },
	data() {
		return {
			timerId: null,
			visible: false,
		}
	},
	mounted() {
		setTimeout(() => {
			this.visible = this.obj.hintVisible;
		}, 500);

		this.timerId = setTimeout(() => {
			this.visible = false;
		}, 3500);
	},
	beforeDestroy() {
		this.timerId && clearTimeout(this.timerId);
	},
	methods: {
		closeAction() {
			this.visible = false;
		},
		hintClose() {
			this.obj.hintVisible = false;
		},
		onReadPolicy() {
			this.$emit('policy');
		},
	},
}

</script>

<style lang="less" scoped type="text/less">
	.container_2211031700 {
		padding-bottom: 0.3rem;
		width: 3.75rem;
		left: calc((100% - 3.75rem) * 0.5);

		.header {
			width: 100%;
		}

		.body {
			padding: 0.15rem 0.15rem 0.2rem;

			.title {
				font-size: 0.18rem;
				font-weight: 500;
				line-height: 0.35rem;
			}

			.subTitle {
				color: #999999;
				font-size: 0.12rem;
				line-height: 0.18rem;
			}

			.content {
				margin-top: 0.1rem;
				color: #474e66;
				font-size: 0.15rem;
				line-height: 0.22rem;

				.policy {
					color: #ff4509;
				}
			}
		}

		.footer {
			margin: 0 auto;
			width: 3.2rem;
			height: 0.48rem;
			line-height: 0.48rem;
			border-radius: 0.24rem;
			font-size: 0.16rem;
			font-weight: 500;
			text-align: center;
			color: #ffffff;
			background: linear-gradient(165deg, #fd9c52, #ff6600);
		}
	}
</style>

