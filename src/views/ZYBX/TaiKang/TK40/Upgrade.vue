<template>
	<div class="container_2302211530">
		<div class="header">
			<img src="@/assets/imgs/NW/NWTK01/img03.png">
			<div class="header-points">
				<img :src="bannerUrl">
			</div>
		</div>
		<div class="content">
			<div class="premium-x">
				<p>优化保障后每月保费由<span>{{premiumObj.totalPremium}}</span>元变更为<span>{{premiumObj.upgradePremium}}</span>元，自次月开始扣费并生效</p>
				<p>变更后产品计划的保障责任、赔付比例、免赔额详见<span class="view-txt" @click.stop="onViewPolicy('产品说明及投保须知',true)">《产品说明及投保须知》</span></p>
			</div>
			<div id="id_action_button" class="submit-button" @click="onSubmit(true)">
				变更保障计划
				<img alt="" class="hand" src="@/assets/imgs/common/icon_hand1.png">
			</div>
			<div class="policy-box">
				我已阅读并同意
				<span class="read" @click.stop="onViewPolicy('健康告知',true)">《健康告知》</span>
				<span class="read" @click.stop="onViewPolicy('产品说明及投保须知',true)">《产品说明及投保须知》</span>
				<span class="read" @click.stop="onViewPolicy('保险条款',true)">《保险条款》</span>
				<span class="read" @click.stop="onViewPolicy('特别约定',true)">《特别约定》</span>
				<span class="read" @click.stop="onViewPolicy('免责声明',true)">《免责声明》</span>
				<span class="read" @click.stop="onViewPolicy('重要提示',true)" v-if="importantTipVisible">《重要提示》</span>
				<span class="read" @click.stop="onViewPolicy('隐私政策',true)">《隐私政策》</span>
                <span class="read" @click.stop="onViewPolicy('泰康在线授权协议',true)">《泰康在线授权协议》</span>
                <span class="read" @click.stop="onViewPolicy('代收扣款服务协议',true)">《代收扣款服务协议》</span>
                <span class="read" @click.stop="onViewPolicy('用户授权协议',true)">《用户授权协议》</span>
				<br>
				<span>保费与被保人年龄、医保情况相关，详情请见
					<span class="read" @click.stop="onViewPolicy('费率表',)">《费率表》</span>
				</span>
			</div>
		</div>
		<HHPlan :obj="policyObj" @click="onViewPolicy('保障详情')" :orderInfo="orderInfo"></HHPlan>
		<van-tabs v-model="currentTab" scrollspy sticky>
			<van-tab key="产品特色" name="产品特色" title="产品特色">
				<div class="section">
					<!-- <div class="section-title">产品特色</div> -->
					<img alt="产品特色" :src="contentUrl">
				</div>
			</van-tab>
			<van-tab key="理赔说明" name="理赔说明" title="理赔说明">
				<div class="section">
					<div class="section-title">理赔说明</div>
					<HHClaimProcess></HHClaimProcess>
				</div>
			</van-tab>
		</van-tabs>
		<div class="copyright">
			<div class="wrapper">
				<img src="@/assets/imgs/logo/lechengshi_1.png">
				<img src="@/assets/imgs/logo/taikangzaixian.png">
			</div>
			<p>产品名称：全能保·百万医疗险</p>
			<p>本产品由泰康在线财产保险股份有限公司授权<br>乐城十保险经纪有限公司销售服务</p>
			<p style="margin-top: 0.10rem;">互联网专属产品<br>本页面由乐城十保险经纪有限公司提供</p>
		</div>
		<div v-if="bottomButtonVisible" class="bottomButton" @click="onSubmit(true)">
			变更保障计划<img alt="小手" class="hand" src="@/assets/imgs/common/icon_hand1.png">
		</div>
		<UUPolicyPopup :obj="policyObj" :orderInfo="orderInfo"></UUPolicyPopup>
		<UUTabPopup :obj="policyObj" @ok="onAcceptPolicy" :orderInfo="orderInfo"></UUTabPopup>
		<TKRecord1 ref="ref_record" :recordObj="orderInfo"></TKRecord1>
	</div>
</template>

<script>
import HHPlan from "./components/HHPlan";
import UUTabPopup from "./components/UUTabPopup";
import TKRecord1 from "@/views/components/TKRecord1";
import UUPolicyPopup from "./components/UUPolicyPopup";
import HHClaimProcess from "./components/HHClaimProcess";
import { TraceLogInfoKeys, url_safe_b64_decode } from "@/assets/js/common";
import { createOrderInfo, eventTracking, loadOrderInfo, saveOrderInfo, getPolicyVersion, } from "./function";

import ImgUrl1 from '@/assets/imgs/TaiKang/TK40/img04.png'
import ImgUrl2 from '@/assets/imgs/TaiKang/TK40/img04_1.png'

import ImgUrl5 from '@/assets/imgs/TaiKang/TK40/img02.png'
import { onClickNwFilingNumber } from '@/utils/filing_number';
export default {
	name: "Upgrade", // 升级页
	components: { HHPlan, UUPolicyPopup, UUTabPopup, TKRecord1, HHClaimProcess },
	data() {
		const orderInfo = createOrderInfo('direct');
		return {
			orderInfo,
			currentTab: '',
			policyObj: { v: false, page: '', v1: false, page1: '', belongs: 'v2' },
			bottomButtonVisible: false,
		}
	},
	computed: {
		premiumObj() {
			const { upgradePremium, totalPremium } = this.orderInfo;

			return { totalPremium, upgradePremium };
		},
        bannerUrl() {
            const belongs = getPolicyVersion(this.orderInfo[`idCard${this.orderInfo.relation}`], this.orderInfo.isLowPricePage)
            const imgMap = { v5: ImgUrl1, v6: ImgUrl1, v7: ImgUrl2, v8: ImgUrl1  }
			return imgMap[belongs] || ImgUrl1
		},
        contentUrl() {
            const belongs = getPolicyVersion(this.orderInfo[`idCard${this.orderInfo.relation}`], this.orderInfo.isLowPricePage)
            const imgMap = { v5: ImgUrl5, v6: ImgUrl5, v7: ImgUrl5, v8: ImgUrl5  }
            return imgMap[belongs] || ImgUrl5
		},
        importantTipVisible() { 
            const belongs = getPolicyVersion(this.orderInfo[`idCard${this.orderInfo.relation}`], this.orderInfo.isLowPricePage)
			return belongs != 'v4' && belongs != 'v7'
        }
	},
	created() {
		this.init();
	},
	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.$nextTick(() => { // 监听滚动事件
			this.actionButtonObserver();
		});

		this._entryReport();

		if (this.orderInfo.school == 1) {
			this.pushToResult();
		}
	},

	methods: {
		onClickNwFilingNumber,
		SetIseeBiz(value) {
			this.orderInfo.traceBackUuid = value;
		},

		actionButtonObserver() {
			if (window.IntersectionObserver) {
				const observer = new IntersectionObserver((entries, observer) => {
					this.bottomButtonVisible = !entries[0].isIntersecting;
				}, { threshold: 0.01 });

				const buttonNode = document.getElementById('id_action_button');
				buttonNode && observer.observe(buttonNode);
			}
		},

		// 初始化
		init() {
			const query = this.$route.query || {};
			query.source = query.source || 'direct'; // source要以链接携带的参数为准
			query.action = query.action || 'direct'; // action要以链接携带的参数为准
			query.school = query.school || '0'; // school要以链接携带的参数为准

			const inStore = loadOrderInfo();
			Object.assign(this.orderInfo, inStore, query);

			this.orderInfo.identifier = 'TK40Upgrade';
			this.orderInfo.productKey = TraceLogInfoKeys.az_tkj_qn_by_jd_cube_base;
			this.orderInfo.checked = true;
            this.orderInfo.isLowPricePage = false

			this.decodeUserInfo();

			saveOrderInfo(1, 'TK40_Upgrade');
		},

		decodeUserInfo() {
			const param = url_safe_b64_decode(this.orderInfo.bizParams);
			if (!param) return;

			const obj = JSON.parse(param);
			Object.assign(this.orderInfo, obj);
		},

		onSubmit() {
			this.onViewPolicy('健康告知', true)
		},

		pushToResult() {
			this._actionTracking('点击立即升级按钮');

			setTimeout(() => {
				const href = window.location.href.replace('/Upgrade', '/Result');
				window.location.href = href;
			}, 250);
		},

		onAcceptPolicy() {
			if (this.policyObj.page1 != '免责声明') {
				return this.onViewPolicy('免责声明', true);
			}

			this.orderInfo.checked = true;

			this.pushToResult();
		},

		onViewPolicy(name, isPolicy,) {
			this.policyObj[isPolicy ? 'v1' : 'v'] = true;
			this.policyObj[isPolicy ? 'page1' : 'page'] = name;
		},

		_entryReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('升级页', domContentLoadedEventEnd - fetchStart);
		},

		_actionTracking(name, time = 0) {
			eventTracking(this.orderInfo, name, time);
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302211530 {
		width: 3.75rem;
		min-height: 100%;
		font-size: 0.15rem;
		background-color: #ffffff;

		img {
			display: block;
			max-width: 100%;
		}

		.header {
			.header-points {
				position: relative;
				margin-top: -0.15rem;
				padding: 0.05rem 0.15rem 0;
				border-radius: 0.1rem;
				background-color: #ffffff;
			}
		}

		.view-txt {
			color: #ff8c41;
			font-weight: 500;
		}

		.content {
			.premium-x {
				font-size: 0.12rem;
				color: #333333;
				text-align: center;
				line-height: 0.2rem;
			}

			.policy-box {
				padding: 0.1rem;
				font-size: 0.13rem;
				line-height: 1.6;
				text-align: justify;
				background-color: #fff7f7;

				.read {
					color: #ff8c41;
					font-weight: 500;
				}
			}

			.submit-button {
				position: relative;
				margin: 0.1rem auto;
				padding: 0.15rem 0;
				width: 3rem;
				border-radius: 999px;
				box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
				background: linear-gradient(
					270deg,
					rgb(255, 16, 46),
					rgb(253, 123, 69)
				);
				animation: button_animate 1.35s linear infinite;

				font-size: 0.2rem;
				color: #ffffff;
				font-weight: 700;
				text-align: center;

				.hand {
					position: absolute;
					top: 0.25rem;
					left: 75%;
					width: 18%;
					animation: hand_animate 1s linear infinite;
				}
			}
		}

		.section {
			background-color: #ffffff;

			.section-title {
				display: flex;
				justify-content: center;
				align-items: center;

				color: #333333;
				font-size: 0.18rem;
				font-weight: 500;
				line-height: 0.45rem;

				&::before,
				&::after {
					content: " ";
					width: 0.55rem;
					height: 0.13rem;
					background: no-repeat center/100%;
				}

				&::before {
					margin-right: 0.1rem;
					background-image: url("~@/assets/imgs/common/icon_needle_left.png");
				}

				&::after {
					margin-left: 0.1rem;
					background-image: url("~@/assets/imgs/common/icon_needle_right.png");
				}
			}
		}

		.copyright {
			padding: 0 0 1rem;
			color: #969799;
			font-size: 0.12rem;
			line-height: 1.6;
			text-align: center;
			background-color: #f8f8f8;

			.wrapper {
				padding: 0.2rem 0 0.15rem;
				display: flex;
				align-items: center;
				justify-content: space-evenly;

				img {
					height: 0.40rem;
				}
			}
		}

		.bottomButton {
			position: fixed;
			inset: auto 0 0.25rem 0;
			margin: 0 auto;
			padding: 0.15rem 0;
			width: 3rem;
			font-size: 0.2rem;
			color: #ffffff;
			font-weight: 700;
			text-align: center;
			border-radius: 999px;
			box-shadow: rgba(255, 139, 139, 0.63) 0px 1px 16px 0px inset;
			background: linear-gradient(
				270deg,
				rgb(255, 16, 46),
				rgb(253, 123, 69)
			);
			animation: button_animate 1.35s linear infinite;

			.hand {
				position: absolute;
				top: 0.25rem;
				left: 75%;
				width: 18%;
				animation: hand_animate 1s linear infinite;
			}
		}

		.copyright {
			padding: 0.2rem 0.15rem 0.85rem;
			color: #666666;
			font-size: 0.12rem;
			line-height: 1.5;
			text-align: center;

			p {
				padding: 0.04rem 0;
			}
		}

		@keyframes button_animate {
			0% {
				transform: scale(1);
			}
			40% {
				transform: scale(1);
			}
			70% {
				transform: scale(0.95);
			}
			100% {
				transform: scale(1);
			}
		}

		@keyframes hand_animate {
			0% {
				transform: translate(-0.1rem, -0.1rem);
			}
			45% {
				transform: translate(0.1rem, 0);
			}
			70% {
				transform: translate(0.1rem, 0);
			}
			100% {
				transform: translate(-0.1rem, -0.1rem);
			}
		}
	}
</style>
