export const PremiumRate = [
    { min: 0, max: 55, month: { data1: 1.03, data2: 7.17 }, year: { data1: 11.25, data2: 78.23 } },
    { min: 56, max: 70, month: { data1: 2.78, data2: 19.43 }, year: { data1: 30.29, data2: 212.02 } },
]

export const PremiumRate1 = [
    { min: 0, max: 4, month: { data1: 91.51, data2: 227.89 }, year: { data1: 998, data2: 2486 } },
    { min: 5, max: 10, month: { data1: 46.89, data2: 113.39 }, year: { data1: 511.4, data2: 1236.8 } },
    { min: 11, max: 15, month: { data1: 28.91, data2: 62.65 }, year: { data1: 315.4, data2: 683.4 } },
    { min: 16, max: 20, month: { data1: 31.28, data2: 67.77 }, year: { data1: 341.2, data2: 739.2 } },
    { min: 21, max: 25, month: { data1: 51.09, data2: 100.82 }, year: { data1: 557.2, data2: 1099.8 } },
    { min: 26, max: 30, month: { data1: 61.08, data2: 129.03 }, year: { data1: 666.2, data2: 1407.4 } },
    { min: 31, max: 35, month: { data1: 80.14, data2: 172.64 }, year: { data1: 874.2, data2: 1883.2 } },
    { min: 36, max: 40, month: { data1: 100.92, data2: 233.66 }, year: { data1: 1100.8, data2: 2549 } },
    { min: 41, max: 45, month: { data1: 118.05, data2: 330.25 }, year: { data1: 1287.8, data2: 3602.6 } },
    { min: 46, max: 50, month: { data1: 130.08, data2: 416.82 }, year: { data1: 1418.8, data2: 4547 } },
    { min: 51, max: 55, month: { data1: 140.54, data2: 483.01 }, year: { data1: 1533, data2: 5269 } },
    { min: 56, max: 60, month: { data1: 183.14, data2: 630.13 }, year: { data1: 1998, data2: 6874 } },
    { min: 61, max: 65, month: { data1: 239.45, data2: 905.76 }, year: { data1: 2612, data2: 9881 } },
    { min: 66, max: 70, month: { data1: 266.30, data2: 1090.74 }, year: { data1: 2905, data2: 11899 } },
]

export const documentList = [
    { page: '健康告知', belongs: 'v2v3v4v5v6v7v8' },
    { page: '投保须知', belongs: 'v1v2v3v4v5v6v7v8' },
    {
        page: '保险条款', belongs: 'v1', list: [
            '住院费用医疗保险T款（互联网专属）条款',
        ],
    },
    {
        page: '保险条款', belongs: 'v2v3v4v5v6v7v8', list: [
            '住院费用医疗保险T款（互联网专属）条款',
            '互联网医院特定药品费用医疗保险C款（互联网专属）',
            '附加指定疾病扩展特需费用医疗保险（互联网专属）条款',
            '附加扩展门（急）诊医疗保险B款（互联网专属）条款',
            '附加个人住院补偿费用医疗保险条款',
        ],
    },
    { page: '责任免除', belongs: 'v1v2v3v4v5v6v7v8' },
    { page: '特别约定', belongs: 'v1v2v3v4v5v6v7v8' },
    { page: '重要信息', belongs: 'v1' },
    { page: '产品说明', belongs: 'v1v2v3v4v5v6v7v8' },
    { page: '百万医疗高危职业表', belongs: 'v2v3v4v5v6v7v8' },
];

export const pdfFileObj = {
    '健康告知': '/TaiKang/TK16/jkgz.pdf',
    '投保须知$v1': '/TaiKang/TK16/tbxz_v1.pdf',
    '投保须知$v2': '/TaiKang/TK16/tbxz_v2.pdf',
    '投保须知$v3': '/TaiKang/TK16/tbxz_v3.pdf',
    '投保须知$v4': '/TaiKang/TK16/tbxz_v4.pdf',
    '投保须知$v5': '/TaiKang/TK16/tbxz_v5.pdf',
    '投保须知$v6': '/TaiKang/TK16/tbxz_v6.pdf',
    '投保须知$v7': '/TaiKang/TK16/tbxz_v7.pdf',
    '投保须知$v8': '/TaiKang/TK16/tbxz_v8.pdf',
    '责任免除$v1': '/TaiKang/TK16/zrmc_v1.pdf',
    '责任免除$v2': '/TaiKang/TK16/zrmc_v2.pdf',
    '责任免除$v3': '/TaiKang/TK16/zrmc_v3.pdf',
    '责任免除$v4': '/TaiKang/TK16/zrmc_v4.pdf',
    '责任免除$v5': '/TaiKang/TK16/zrmc_v5.pdf',
    '责任免除$v6': '/TaiKang/TK16/zrmc_v6.pdf',
    '责任免除$v7': '/TaiKang/TK16/zrmc_v7.pdf',
    '责任免除$v8': '/TaiKang/TK16/zrmc_v8.pdf',
    '特别约定$v1': '/TaiKang/TK16/tbyd_v1.pdf',
    '特别约定$v2': '/TaiKang/TK16/tbyd_v2.pdf',
    '特别约定$v3': '/TaiKang/TK16/tbyd_v3.pdf',
    '特别约定$v4': '/TaiKang/TK16/tbyd_v4.pdf',
    '特别约定$v5': '/TaiKang/TK16/tbyd_v5.pdf',
    '特别约定$v6': '/TaiKang/TK16/tbyd_v6.pdf',
    '特别约定$v7': '/TaiKang/TK16/tbyd_v7.pdf',
    '特别约定$v8': '/TaiKang/TK16/tbyd_v8.pdf',
    '重要信息$v1': '/TaiKang/TK16/zyxx_v1.pdf',
    '产品说明$v1': '/TaiKang/TK16/cpsm_v1.pdf',
    '产品说明$v2': '/TaiKang/TK16/cpsm_v2.pdf',
    '产品说明$v3': '/TaiKang/TK16/cpsm_v3.pdf',
    '产品说明$v4': '/TaiKang/TK16/cpsm_v4.pdf',
    '产品说明$v5': '/TaiKang/TK16/cpsm_v5.pdf',
    '产品说明$v6': '/TaiKang/TK16/cpsm_v6.pdf',
    '产品说明$v7': '/TaiKang/TK16/cpsm_v7.pdf',
    '产品说明$v8': '/TaiKang/TK16/cpsm_v8.pdf',
    '百万医疗高危职业表': '/TaiKang/TK16/gwzyb.pdf',
    '住院费用医疗保险T款（互联网专属）条款': '/TaiKang/TK16/dir1.pdf',
    '互联网医院特定药品费用医疗保险C款（互联网专属）': '/TaiKang/TK16/dir2.pdf',
    '附加指定疾病扩展特需费用医疗保险（互联网专属）条款': '/TaiKang/TK16/dir3.pdf',
    '附加扩展门（急）诊医疗保险B款（互联网专属）条款': '/TaiKang/TK16/dir4.pdf',
    '附加个人住院补偿费用医疗保险条款': '/TaiKang/TK16/dir5.pdf',
}

//为谁投保
export const relations = [
    { key: "本人", value: 1 },
    { key: "配偶", value: 2 },
    { key: "儿女", value: 3 },
    { key: "父母", value: 4 },
]

//有无社保
export const insurances = [
    { key: "有医保(含新农合)", value: 1 },
    { key: "无医保", value: 0 },
]

//缴费方式
export const repays = [
    { key: "按月缴费(12期)", value: 1 },
    { key: "全额缴费", value: 0 },
]

export const planSummary = [
    { key: '社保内原发恶性肿瘤医疗保险金', value: '300万保额' },
    { key: '社保内特定疾病医疗保险金', value: '600万保额' },
    { key: '齿科服务：家庭健齿套餐', value: '免费赠送' },
]

export const planSummary1 = [
    { key: '社保内原发恶性肿瘤医疗保险金', value: '300万保额(共用300万)' },
    { key: '社保外原发恶性肿瘤医疗保险金', value: '300万保额(共用300万)' },
    { key: '社保内特定疾病医疗保险金', value: '600万保额(共用600万)' },
    { key: '社保外特定疾病医疗保险金', value: '600万保额(共用600万)' },
    { key: '社保内一般医疗保险金', value: '300万保额(共用300万)' },
    { key: '社保外一般医疗保险金', value: '300万保额(共用300万)' },
    { key: '质子重离子医疗保险金', value: '600万保额' },
]

export const planFeatures = [
    "保单可验真",
    "报销自费药",
    "住院可垫付"
]

export const planPoints = [
    { key: '投保年龄', value: '30天（含）-70周岁（含）' },
    { key: '等待期', value: '疾病30天，意外无等待期' },
    { key: '犹豫期', value: '15天' },
    { key: '医院范围', value: '二级以上公立普通部及保险人扩展医院的普通部' },
]

export const planPoints1 = [
    { key: '投保年龄', value: '30天（含）-70周岁（含）' },
    { key: '等待期', value: '报销型医疗险30天，给付型重疾90天，意外与续保无等待期' },
    { key: '犹豫期', value: '15天' },
    { key: '医院范围', value: '二级以上公立普通部及保险人扩展医院的普通部' },
]

export const planDetails = [
    {
        key: '社保内原发恶性肿瘤医疗保险金',
        value: '300万保额',
        text: '1.免赔额为5万元；<br>2.赔付比例：<br>（１）若以有社会医疗保险身份投保，且以社会医疗保险身份就诊并结算的，保险人按30%的比例进行赔付；<br>（２）若被保险人以有社会医疗保险身份投保，但未以社会医疗保险身份就诊并结算的，保险人按照18%的比例进行赔付；<br>（３）若被保险人以无社会医疗保险身份投保，保险人按照30%的比例进行赔付。'
    }, {
        key: '社保内特定疾病医疗保险金',
        value: '600万保额',
        text: '1.免赔额为5万元；<br>2.赔付比例：<br>（１）若以有社会医疗保险身份投保，且以社会医疗保险身份就诊并结算的，保险人按30%的比例进行赔付；<br>（２）若被保险人以有社会医疗保险身份投保，但未以社会医疗保险身份就诊并结算的，保险人按照18%的比例进行赔付；<br>（３）若被保险人以无社会医疗保险身份投保，保险人按照30%的比例进行赔付。<br>3.特定疾病:<br>共有128种，包括四类组别：心脏或心血管类特定疾病、脑中风或神经系统类特定疾病、其他类特定疾病、特殊类特定疾病，各组下的特定疾病及定义以本合同释义部分为准。'
    }, {
        key: '齿科服务：家庭健齿套餐',
        value: '免费赠送',
        text: ''
    },
]

export const planDetails1 = [
    {
        key: '社保内原发恶性肿瘤医疗保险金',
        value: '300万保额',
        text: '1.社保内恶性肿瘤医疗保险金、社保外恶性肿瘤医疗保险金共用保险金额600万<br>2.社保内恶性肿瘤医疗保险金的免赔额为0元<br>3.社保内原发恶性肿瘤医疗保险金的赔偿比例为：<br>（１）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例100%进行赔偿；<br>（２）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例60%进行赔偿；<br>（３）被保险人以未参加社会基本医疗保险或公费医疗身份投保，按照本合同约定的赔偿比例100%进行赔偿。'
    }, {
        key: '社保外原发恶性肿瘤医疗保险金',
        value: '300万保额',
        text: '1.社保内恶性肿瘤医疗保险金、社保外恶性肿瘤医疗保险金共用保险金额600万<br>2.社保外恶性肿瘤医疗保险金的免赔额为0元<br>3.社保外原发恶性肿瘤医疗保险金的赔偿比例为100%。'
    }, {
        key: '社保内特定疾病医疗保险金',
        value: '600万保额',
        text: '1.社保内特定疾病医疗保险金、社保外特定疾病医疗保险金共用保险金额600万<br>2.社保内特定疾病医疗保险金的免赔额为0元。<br>3.社保内特定疾病医疗保险金的赔偿比例为：<br>（１）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例100%进行赔偿；<br>（２）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例60%进行赔偿；<br>（３）被保险人以未参加社会基本医疗保险或公费医疗身份投保，按照本合同约定的赔偿比例100%进行赔偿。'
    }, {
        key: '社保外特定疾病医疗保险金',
        value: '600万保额',
        text: '1.社保内特定疾病医疗保险金、社保外特定疾病医疗保险金共用保险金额600万<br>2.社保外特定疾病医疗保险金的免赔额为0元。<br>3.社保外特定疾病医疗保险金的赔偿比例为100%。'
    }, {
        key: '社保内一般医疗保险金',
        value: '300万保额',
        text: '1.社保内一般医疗保险金、社保外一般医疗保险金共用保险金额300万元<br>2.社保内一般医疗保险金、社保外一般医疗保险金共用免赔额为1万元<br>3.社保内一般医疗保险金的赔偿比例为：<br>（１）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例100%进行赔偿；<br>（２）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例60%进行赔偿；<br>（３）被保险人以未参加社会基本医疗保险或公费医疗身份投保，按照本合同约定的赔偿比例100%进行赔偿。'
    }, {
        key: '社保外一般医疗保险金',
        value: '300万保额',
        text: '1.社保内一般医疗保险金、社保外一般医疗保险金共用保险金额300万元<br>2.社保内一般医疗保险金、社保外一般医疗保险金共用免赔额为1万元<br>3.社保外一般医疗保险金的赔偿比例为100%。'
    }, {
        key: '质子重离子医疗保险金',
        value: '600万保额',
        text: '1.质子重离子医疗保险金额为600万<br>2.质子重离子医疗保险金的免赔额为0元。<br>3.质子重离子医疗保险金的赔偿比例为100%。'
    },
]

export const askAnswerList = [
    {
        q: '投保后或升级后，责任何时生效？',
        a: '本产品于投保并支付保险费后次日零时生效；如先投保基础版，后续通过批改升级方式投保升级版，批增责任生效日为批增责任交费成功当日零时。'
    }, {
        q: '升级后的保险期间会变化吗？',
        a: '升级后保单的保险止期同升级前基础版的保险止期，保险期间不会变化。'
    }, {
        q: '等待期是怎么规定的？',
        a: '1）本产品首次投保或非续保时，个人重大疾病保险金责任自合同生效之日起30日（含）为等待期，因意外伤害导致罹患个人重大疾病保险金条款约定的重大疾病无等待期；本产品首次投保或非续保时，医疗保险责任自合同生效之日起30日（含）为等待期，意外伤害无等待期。2）如果首先投保基础版，后续升级到“升级版”，升级版责任不重新计算等待期。'
    }, {
        q: '基础版和升级版有何区别？',
        a: '基础版保障社保内原发恶性肿瘤医疗保险金和社保内特定疾病医疗保险金，百万医疗责任的免赔额较高赔付比例也较低。升级版在基础版责任的基础上，降低医疗责任免赔额的同时提升了赔付比例，并扩展了其它附加险责任，升级版享有更全面的保障。'
    }, {
        q: '升级版各年龄段责任有何差异？',
        a: '对于基础版：本产品0周岁（出生满30天，含第30天）-70周岁被保险人社保内原发恶性肿瘤医疗保险金300万，5万免赔，社保内特定疾病医疗保险金600万，5万免赔保险责任。各年龄保额也有差异，具体请参考保障详情。对于升级版： 全年龄层通用包含社保内原发恶性肿瘤医疗保险金（必须）、社保外原发恶性肿瘤医疗保险金、社保内特定疾病医疗保险金、社保外特定疾病医疗保险金、社保内一般医疗保险金、社保外一般医疗保险金、质子重离子医疗保险金；0-5周岁再包含的责任为：恶性肿瘤院外特种药品费用保险金、附加个人住院补偿费用医疗保险；5-11周岁再包含的责任为：恶性肿瘤院外特种药品费用保险金、附加个人住院补偿费用医疗保险、附加扩展门（急）诊医疗保险金；11-21周岁再包含的责任为：恶性肿瘤院外特种药品费用保险金、附加个人住院补偿费用医疗保险、附加扩展门（急）诊医疗保险金、附加指定疾病扩展特需医疗保险金；21-41周岁再包含的责任为：恶性肿瘤院外特种药品费用保险金、附加个人住院补偿费用医疗保险、附加扩展门（急）诊医疗保险金、附加指定疾病扩展特需医疗保险金、互联网医院特定药品费用医疗保险金 ；41-46周岁再包含的责任为：恶性肿瘤院外特种药品费用保险金、附加个人住院补偿费用医疗保险、附加扩展门（急）诊医疗保险金、附加指定疾病扩展特需医疗保险金；46-51周岁再包含的责任为：恶性肿瘤院外特种药品费用保险金、附加个人住院补偿费用医疗保险、附加扩展门（急）诊医疗保险金； 51-61周岁再包含的责任为：恶性肿瘤院外特种药品费用保险金、附加个人住院补偿费用医疗保险； 61-66周岁再包含的责任为：恶性肿瘤院外特种药品费用保险金。'
    }, {
        q: '保险人扩展的医院包括哪些医院？',
        a: '指定医院包括泰康同济(武汉)医院、泰康仙林鼓楼医院，保险人保留扩展权利，名单详见扩展承保的医院清单www.tk.cn/service/yljgypqd/ypqd/'
    }, {
        q: '投保成功后在哪里查看保单？',
        a: '可以通过关注“泰康在线保险”微信公众号或下载“泰康在线”app查看电子保单。'
    },
]
