<template>
	<div class="container_2303061710">
		<div class="header">
			<p class="product-name">泰超能·百万医疗险</p>
			<p class="count-down">
				<span>{{ minutes }}</span>:
				<span>{{ seconds }}</span>后订单失效，请尽快完成支付
			</p>
		</div>
		<div style="overflow: auto;flex: 1;">
			<div class="section">
				<div class="section-header">保障计划</div>
				<div v-for="(item,index) in planSummary.slice(0, 5)" :key="index" class="section-item">
					<span>{{ item.key }}</span>
					<span v-html="item.value" class="section-item-point"></span>
				</div>
			</div>
			<div class="section">
				<div class="section-header">
					<span>投保人</span>
					<span @click="onEdit" class="section-header-edit">编辑</span>
				</div>
				<div v-for="(item,index) in holderInfo" :key="index" class="section-item">
					<span>{{ item.key }}</span>
					<span class="section-item-value">{{ item.value }}</span>
				</div>
			</div>
			<div class="section">
				<div class="section-header">
					<span>被保人</span>
					<span @click="onEdit" class="section-header-edit">编辑</span>
				</div>
				<div v-for="(item,index) in insuredInfo" :key="index" class="section-item">
					<span>{{ item.key }}</span>
					<span class="section-item-value">{{ item.value }}</span>
				</div>
			</div>
			<div class="premium-x">月缴更轻松，每月<span class="number">{{ premium }}</span>元</div>
			<div class="policy-x">
				<div>
					我确认并接受
					<span class="view-txt" @click.stop="viewAction('投保须知',true)">《投保须知》</span>
					<span class="view-txt" @click.stop="viewAction('责任免除',true)">《责任免除》</span>
					<span class="view-txt" @click.stop="viewAction('重要信息',true)">《重要信息》</span>
					<span class="view-txt" @click.stop="viewAction('特别约定',true)">《特别约定》</span>
					<span class="view-txt" @click.stop="viewAction('产品说明',true)">《产品说明》</span>
					<span class="view-txt" @click.stop="viewAction('保险条款',true)">《保险条款》</span>
					<span class="view-txt" @click.stop="viewAction('费率表',false)">《费率表》</span>。
				</div>
			</div>
		</div>
		<div class="bottom-button" @click="onSubmit">立即支付</div>
		<HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
		<HHTabViewer :obj="policyObj" @ok="onAcceptPolicy"></HHTabViewer>
	</div>
</template>

<script>
import { bxStorage, } from "@/utils/store_util";
import { planSummary, } from "./src";
import HHTabViewer from "./components/HHTabViewer";
import HHPolicyViewer from "./components/HHPolicyViewer";
import { domainPathMap, } from "@/views/ZYBX/src";
import { fetchPromoteOrderInfo } from "@/api/insurance-api";
import { url_safe_b64_decode } from "@/assets/js/common";
import { calculatePremium, showToast, eventTracking, submitPromoteOrderInfo, createOrderInfo } from "./function";

export default {
	name: "Promotion",
	components: { HHPolicyViewer, HHTabViewer },
	data() {
		const orderInfo1 = {
			holderName: '',
			holderIdCard: '',
			holderPhone: '',
			insuredName: '',
			insuredIdCard: '',
			expireTime: 0,
		};

		const orderInfo = createOrderInfo();

		Object.assign(orderInfo, orderInfo1);

		return {
			planSummary,
			minutes: 0,
			seconds: 0,
			orderInfo,
			policyObj: { v: false, page: '', v1: false, page1: '', belongs: 'v1' },
		}
	},
	computed: {
		premium() {
			if (!this.orderInfo.totalPremium) {
				return '1.03';
			}
			return (+this.orderInfo.totalPremium).toFixed(2);
		},

		holderInfo() {
			const { holderName, holderIdCard, holderPhone } = this.orderInfo;
			return [
				{ key: '姓名', value: holderName },
				{ key: '身份证', value: holderIdCard },
				{ key: '手机号', value: holderPhone },
			]
		},

		insuredInfo() {
			const { insuredName, insuredIdCard, insurance } = this.orderInfo;
			return [
				{ key: '姓名', value: insuredName },
				{ key: '身份证', value: insuredIdCard },
				{ key: '有无社保', value: insurance == 1 ? '有' : '无' },
			]
		},
	},

	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.init();
	},

	methods: {
		//初始化
		init() {
			let inQry = this.$route.query || {}; // 必须：加密的infoNo

			if (inQry.param) {
				const param = url_safe_b64_decode(inQry.param);
				if (param) {
					inQry = JSON.parse(param);
				}
			}

			const { channel, infoNo } = inQry;
			this.orderInfo.channel = channel || '1';
			this.orderInfo.identifier = 'TK31Promotion1';
			this.orderInfo.action = 'promote';
			this.orderInfo.source = 'promote';
			this.orderInfo.expireTime = bxStorage.getRawItem('TK31ExpireTime') || 0;

			this.fetchOrderInfo(infoNo);
		},

		fetchOrderInfo(infoNo) {
			if (!infoNo) {
				this._entryBehaviorReport();
				return this.editOrderInfo(true);
			}

			this.$toast.loading({
				message: '正在请求数据\n请稍候',
				forbidClick: true,
				duration: 0,
			});

			fetchPromoteOrderInfo(infoNo).then(res => {
				this.$toast.clear(true);
				const data = res.data || {};
				this.orderInfoHandle(data);
			}).catch(err => {
				this.$toast.clear(true);
				this.editOrderInfo(true);
			}).finally(() => {
				this._entryBehaviorReport();
			});
		},

		orderInfoHandle(data) {
			Object.assign(this.orderInfo, data);

			const { operatorPhone, insuredIdCard, insurance, repay, infoNo, } = this.orderInfo;
			if (!infoNo) {
				return this.editOrderInfo(true);
			}

			this.orderInfo.mTel = operatorPhone;
			this.orderInfo.totalPremium = calculatePremium(insuredIdCard, insurance, repay).low;

			this.startCountdown();
		},

		startCountdown() {
			if (Date.now() - this.orderInfo.expireTime > 0) {
				this.orderInfo.expireTime = Date.now() + 60 * 60 * 1000;
				bxStorage.setRawItem('TK31ExpireTime', this.orderInfo.expireTime);
			}

			requestAnimationFrame(() => {
				const diff = Math.floor((this.orderInfo.expireTime - Date.now()) / 1000);
				this.minutes = ('00' + Math.floor(diff / 60)).slice(-2);
				this.seconds = ('00' + (diff - this.minutes * 60)).slice(-2);

				this.startCountdown();
			})
		},

		onEdit() {
			this._actionTracking('点击编辑按钮');
			this.editOrderInfo();
		},

		editOrderInfo(delay, msg) {
			const { channel, mTel } = this.orderInfo;

			const href = domainPathMap['TK31Index1'] + `?action=promotion&source=promotion&channel=${channel}&mTel=${mTel}`;

			if (!delay) {
				return setTimeout(() => {
					return window.location.href = href;
				}, 250);
			}

			showToast(msg || '该订单无效，正在跳转到首页');

			setTimeout(() => {
				return window.location.href = href;
			}, 2000);
		},

		onSubmit() {
			const { infoNo, channel, productKey } = this.orderInfo;

			this._actionTracking('点击促活立即支付按钮');

			this.$toast.loading({
				message: '订单提交中\n请稍候',
				forbidClick: true,
				duration: 0,
			});

			const params = { infoNo: infoNo, page: `infoKey:${productKey}&page:promotion`, channelId: channel };
			submitPromoteOrderInfo(params, this.orderInfo).then(url => {
				this.$toast.clear(true);
				return window.location.href = url;
			}).catch(err => {
				const message = err.msg || '';
				this.editOrderInfo(true, message);
			});
		},

		onAcceptPolicy() {
			this.onSubmit();
		},

		viewAction(name, isPolicy) {
			this.policyObj[isPolicy ? 'v1' : 'v'] = true;
			this.policyObj[isPolicy ? 'page1' : 'page'] = name;
		},

		_entryBehaviorReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('支付促活页', domContentLoadedEventEnd - fetchStart);
		},

		_actionTracking(name, time = 0) {
			eventTracking(this.orderInfo, name, time);
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2303061710 {
		height: 100%;
		box-sizing: border-box;
		background-color: #f2f2f2;

		display: flex;
		flex-direction: column;

		.header {
			padding: 0.15rem 0 0.1rem 0.15rem;
			color: #ffffff;
			background: url("~@/assets/imgs/TaiKang/promotion1.png") no-repeat;
			background-size: cover;

			.product-name {
				font-size: 0.2rem;
				font-weight: 700;
			}

			.count-down {
				margin-top: 0.1rem;
				display: flex;
				align-items: center;

				font-size: 0.14rem;
				font-weight: 700;

				span {
					padding: 0.04rem 0.05rem;
					text-align: center;
					border-radius: 0.04rem;
					background-color: #fd5b02;
				}
			}
		}

		.section + .section {
			margin-top: 0.1rem;
		}

		.section {
			background-color: #ffffff;

			.section-header {
				padding: 0.15rem 0.1rem;
				font-size: 0.17rem;
				font-weight: 700;

				display: flex;
				align-items: center;
				justify-content: space-between;

				.section-header-edit {
					color: #0b78ff;
				}
			}

			.section-item {
				padding: 0 0.1rem 0.15rem;
				font-size: 0.14rem;
				font-weight: 500;
				color: #666666;

				display: flex;
				align-items: center;
				justify-content: space-between;

				.section-item-value {
					color: #191919;
					font-size: 0.16rem;
					font-weight: 500;
				}

				.section-item-point {
					color: #fd8a25;
					font-weight: bold;
				}
			}
		}

		.premium-x {
			margin: 0.15rem 0;
			color: #333333;
			font-size: 0.14rem;
			text-align: center;

			.number {
				font-size: 0.16rem;
				font-weight: 700;
				color: #ff491d;
			}
		}

		.policy-x {
			padding: 0.1rem;
			font-size: 0.13rem;
			color: #333333;
			line-height: 1.6;
			text-align: justify;
			background-color: #ffffff;

			.view-txt {
				color: #ff8c41;
				font-weight: 500;
			}
		}

		.bottom-button {
			padding: 0.16rem 0;
			color: #ffffff;
			font-size: 0.17rem;
			font-weight: 700;
			text-align: center;
			background-color: #fd8a25;
		}
	}
</style>
