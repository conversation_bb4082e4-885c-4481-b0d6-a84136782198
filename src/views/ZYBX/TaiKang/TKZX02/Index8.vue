<template>
	<div class="container_2212161330">
		<div>
			<img src="@/assets/imgs/TaiKang/TKZX02/img_1_9.png">
		</div>
		<OrderInputBox :orderInfo="orderInfo" class="inputBox" @focus="onTextFocus" @input="onTextInput" @submit="submit">
			<template #policy>
				<div v-if="!isStep2" class="read-box">
					您已进入投保流程，为保障您的权益，请仔细阅读保险条款等内容，我们将安全记录您的操作行为。未注册的手机将自动创建泰康账户，点击按钮即表示您已阅读并同意
					<span class="read-text" @click.stop="onReadPolicy('隐私声明')">《隐私声明》</span>
					<span class="read-text" @click.stop="onReadPolicy('会员注册协议')">《会员注册协议》</span>
					<span class="read-text" @click.stop="onReadPolicy('泰康通行证用户服务协议')">《泰康通行证用户服务协议》</span>
					<span class="read-text" @click.stop="onReadPolicy('泰康通行证隐私政策')">《泰康通行证隐私政策》</span>
				</div>
				<div v-if="isStep2" class="read-box">
					<van-icon color="#FF7000" :name="orderInfo.checked ?'checked':'circle'" size="0.16rem" @click="orderInfo.checked = !orderInfo.checked" />
					我已阅读并同意
					<span class="read-text" @click.stop="onReadPolicy('投保须知')">《投保须知》</span>
					<span class="read-text" @click.stop="onReadPolicy('保险条款')">《保险条款》</span>
					<span class="read-text" @click.stop="onReadPolicy('特别约定')">《特别约定》</span>
					，并符合
					<span class="read-text" @click.stop="onReadPolicy('健康告知')">《健康告知》</span>
					投保条件。
				</div>
			</template>
		</OrderInputBox>
		<!--        <div class="remark">您已进入投保流程，为保障您的权益，请仔细阅读保险条款等内容，我们将安全记录您的操作行为。</div>-->
		<img alt="泰康保障内容" class="introduce" src="@/assets/imgs/TaiKang/TKZX02/img_2_6.png">
		<div class="copyright">
			<p>泰康在线财产保险股份有限公司</p>
			<p>泰康重疾条款注册号: C00019932612021122224913</p>
			<p>互联网专属产品</p>
			<p>京ICP备09074081号-19</p>
		</div>
		<PolicyPopup :obj="policyObj" @checkAction="checkPolicy"></PolicyPopup>
		<ResultPopup :obj="resultObj" @click="goToResultLink"></ResultPopup>
		<BackPopup :obj="backObj" @click="goToBackLink"></BackPopup>
		<ImpReminder :obj="orderInfo"></ImpReminder>
	</div>
</template>

<script>
import ImpReminder from "./components/ImpReminder";
import PolicyPopup from "./components/PolicyPopup";
import OrderInputBox from "./components/OrderInputBox3";
import ResultPopup from "./components/ResultPopup";
import BackPopup from "./components/BackPopup";
import { domainPathMap, } from "@/views/ZYBX/src";
import { createMultiFreeOrder, fetchRoundRobinWithAgeZXResultNew, fetchRoundRobinWithPhoneOrderFilter, fetchStarPhoneV4, userWeComInfo, } from '@/api/insurance-api';
import { reviseIdCard, reviseName, GetAge, isAIChannel, isCardNo, isPersonName, isPhoneNum, object2QueryParams, TraceLogInfoKeys } from "@/assets/js/common";
import { createOrderInfo, eventTracking, isStarPhone, loadOrderInfo, msgToast, saveOrderInfo, } from "./src";

export default {
	name: "TKZX02Index8",
	data() {
		const orderInfo = createOrderInfo();
		return {
			orderInfo: orderInfo,
			policyObj: { visible: false, page: '' },
			backObj: { visible: false, showTimer: false, path: '' },
			resultObj: { visible: false, showTimer: false, path: '' },
			submitTimerId: null,
			prevName: '',
		}
	},
	components: {
		BackPopup,
		ResultPopup,
		OrderInputBox,
		PolicyPopup,
		ImpReminder,
	},
	computed: {
		isStep2() {
			return this.orderInfo.step == 'step2';
		},
	},
	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.init();

		this.$nextTick(() => {
			this.pushHistory();
			window.addEventListener("popstate", this.stackPopHandle);
		});
	},
	beforeDestroy() {
		window.removeEventListener("popstate", this.stackPopHandle);
	},
	methods: {
		//初始化
		init() {
			const inQry = this.$route.query || {};
			const orderInfo = loadOrderInfo();

			Object.assign(this.orderInfo, orderInfo);
			this.orderInfo.page = 'TKZX02Index8';
			this.orderInfo.productKey = TraceLogInfoKeys.tk_jj_free_send;
			this.orderInfo.sourcePage = '泰康经纪直投V231204';
			this.orderInfo.channel = inQry.channel || orderInfo.channel || '1';
			this.orderInfo.channelCode = inQry.cld || orderInfo.channelCode || '';

			if (this.orderInfo.received) {
				this._entryReport();
				return this.pushToResult('done', true);
			}

			inQry.externalUserId = inQry.externalUserId || ''; // 企微
			inQry.userId = inQry.userId || ''; // 企微

			Object.assign(this.orderInfo, inQry);

			this.orderInfo.hasCode = false;
			if (isPhoneNum(this.orderInfo.phoneNo) || isStarPhone(this.orderInfo)) {
				this.orderInfo.step = 'step2';
			} else {
				this.orderInfo.step = 'step1';
			}

			this.fetchPhoneNumber();
		},

		fetchPhoneNumber() {
			const { m, phoneNo, channelEnMContent, channelEnMCode } = this.orderInfo;
			if ((!m && !channelEnMContent) || isPhoneNum(phoneNo) || isStarPhone(this.orderInfo)) {
				return this._entryReport();
			}

			const params = {};
			if (m) {
				params.encryptContent = m;
			} else {
				params.channelEnMContent = channelEnMContent;
				params.channelEnMCode = channelEnMCode;
			}
			fetchStarPhoneV4(params).then(res => {
				const { encryptPhone, showPhone } = res.data;
				this.orderInfo.mTel = encryptPhone;
				this.orderInfo.starPhone = showPhone;
				this.orderInfo.phoneNo = showPhone;
				if (isStarPhone(this.orderInfo)) {
					this.orderInfo.step = 'step2';
				}
				saveOrderInfo(this.orderInfo);
			}).finally(() => {
				return this._entryReport();
			});
		},

		onReadPolicy(page) {
			const policyObj = { visible: true, page: page };
			this.policyObj = policyObj;
		},

		checkPolicy() {
			this.orderInfo.checked = true;
			this.submit();
		},

		onTextInput({ key, value }) {
			let isChanged = false;
			const channel = this.orderInfo.channel;
			if (key === 'phone' && isPhoneNum(value)) {
				isChanged = true;
				if (isAIChannel(channel)) {
					this._actionTracking('首页-完成输入手机号');
				}
			} else if (key === 'idCard' && isCardNo(value)) {
				isChanged = true;
				if (isAIChannel(channel)) {
					this._actionTracking('首页-完成输入身份证');
				}
			} else if (key === 'name' && isPersonName(value)) {
				if (this.prevName != value) {
					this.prevName = value;
					if (isAIChannel(channel)) {
						this._actionTracking(`首页-完成输入姓名`);
					}
				}
			}
			if (!isChanged) return;

			saveOrderInfo(this.orderInfo);
		},

		onTextFocus({ key, value }) {
			const channel = this.orderInfo.channel;
			if (!isAIChannel(channel)) return
			if (key === 'idCard' && !value) {
				this._actionTracking('首页-开始输入身份证');
			} else if (key === 'name' && !value) {
				this._actionTracking('首页-开始输入姓名');
			}
		},

		submit() {
			this.orderInfo.name1 = reviseName(this.orderInfo.name1);
			this.orderInfo.idCard1 = reviseIdCard(this.orderInfo.idCard1);

			const { name1, phoneNo, starPhone, idCard1, verCode, hasCode, checked } = this.orderInfo;
			let msg = '';
			if (this.isStep2) {
				if (!isPersonName(name1)) {
					msg = '请输入正确的姓名';
				} else if (!isCardNo(idCard1)) {
					msg = '请输入正确的身份证号码';
				} else if (!checked) {
					return this.onReadPolicy('健康告知');
				}
				if (msg) {
					msgToast(msg);
					return false;
				}
				this._actionTracking('首页-点击第二步立即领取');
			} else {
				if (!isPhoneNum(phoneNo) && (phoneNo.length != 11 || phoneNo != starPhone)) {
					msg = '请输入正确的手机号';
				} else if (hasCode && verCode.length != 4) {
					msg = '请输入验证码';
				}
				if (msg) {
					msgToast(msg);
					return false;
				}
				this._actionTracking('首页-点击第一步立即领取');
				this.orderInfo.step = 'step2';
				saveOrderInfo(this.orderInfo);
				this.userWeComInfo();
				return false;
			}

			if (this.submitTimerId) return;
			this.submitTimerId = setTimeout(() => {
				this.submitTimerId && clearTimeout(this.submitTimerId);
				this.submitTimerId = null;
			}, 2000);

			this.submitOrder();
		},

		submitOrder() {
			const { name1, idCard1, phoneNo, relation, traceBackUuid, productKey } = this.orderInfo;
			const { infoNo, channel, page, mTel, channelCode, sourcePage } = this.orderInfo;
            const planKeys = [productKey];
            if (GetAge(idCard1) > 30) {
                const rxh_infoKey = Math.random() < 0.5 ? TraceLogInfoKeys.rxh_gr_disease_send : TraceLogInfoKeys.rxh_hn_traffic_send;
                planKeys.push(rxh_infoKey);
            }
            
			const params = {
				page,
				sourcePage,
				infoNo,
				holderName: name1,
				holderIdCard: idCard1,
				holderPhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				relation,
				planKeys: planKeys,
				channelId: channel,
				operatorPhone: mTel || phoneNo,
				channelCode: channelCode || '',
				traceBackUuid: traceBackUuid,
			}

			this.$toast.loading({
				message: '订单提交中\n请稍候',
				forbidClick: true,
				duration: 0,
			});

			createMultiFreeOrder(params).finally(() => {
				this.pushToResult('success', true);
			});
		},

		userWeComInfo() {
			const { externalUserId, userId, phoneNo, mTel, channel } = this.orderInfo;
			if (!externalUserId) return;

			const params = {
				channelId: channel,
				externalUserId,
				serviceId: userId,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
			}
			userWeComInfo(params).then(res => {

			});
		},

		pushToResult(state, save) {
			if (save) {
				this.orderInfo.received = 1;
				saveOrderInfo(this.orderInfo);
			}

			this.fetchResultPath();
		},

		fetchResultPath() {
			const { channel, phoneNo, idCard1, mTel } = this.orderInfo;

			const params = {
				channelId: channel,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				idCard: idCard1,
				robinKey: 'tkjj_zx_to_mf',
			}

			fetchRoundRobinWithAgeZXResultNew(params).then(result => {
				const { path } = result.data || {};
				this.resultObj.path = path || '';
			}).finally(() => {
				this.$toast.clear(true);

				if (!domainPathMap[this.resultObj.path]) {
					this.resultObj.path = 'TK15Index1';
				}

				this.resultObj = { ...this.resultObj, visible: true, showTimer: true };
			});
		},

		goToResultLink() {
			this._actionTracking(`首页-点击弹框好的马上去(${this.resultObj.path})`);

			const { channelCode, channel, name1, idCard1, phoneNo, starPhone, mTel, page, sourcePage, } = this.orderInfo;
			const relation = GetAge(idCard1) < 18 ? 3 : 1;
			const params = {
				channel, cld: channelCode, relation, source: page, sourcePage, mTel, action: 'follow',
				[`name${relation}`]: name1, [`idCard${relation}`]: idCard1,
			}

			if (isPhoneNum(phoneNo)) {
				params.phoneNo = phoneNo;
				params.starPhone = '';
			} else {
				params.phoneNo = '';
				params.starPhone = starPhone;
			}

			const href = domainPathMap[this.resultObj.path];
			const query = object2QueryParams(params);

			setTimeout(() => {
				window.location.href = `${href}?${query}`;
			}, 250);
		},

		fetchBackPath() {
			const { channel, phoneNo, idCard1, mTel } = this.orderInfo;

			const params = {
				channelId: channel,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				robinKey: 'tkjj_zx_to_back',
			}

			if (isCardNo(idCard1)) {
				params.idCard = idCard1;
				fetchRoundRobinWithAgeZXResultNew(params).then(result => {
					const { path } = result.data || {};
					this.backObj.path = path || '';
				}).finally(() => {
					this.backPathHandler();
				});
			} else {
				fetchRoundRobinWithPhoneOrderFilter(params).then(result => {
					const { path } = result.data || {};
					this.backObj.path = path || '';
				}).finally(() => {
					this.backPathHandler();
				});
			}
		},

		backPathHandler() {
			if (!domainPathMap[this.backObj.path]) {
				this.backObj.path = 'TK15Index1';
			}
			this.backObj = { ...this.backObj, visible: true };
			this._actionTracking(`首页-点击返回按钮(${this.backObj.path})`);
		},

		goToBackLink() {
			this._actionTracking(`首页-点击返回弹框图片(${this.backObj.path})`);

			const { channelCode, channel, name1, idCard1, relation, phoneNo, starPhone, mTel, page, sourcePage, } = this.orderInfo;
			const params = { channel, cld: channelCode, relation, source: page, sourcePage, mTel, name1, idCard1, action: 'back', }

			if (isPhoneNum(phoneNo)) {
				params.phoneNo = phoneNo;
				params.starPhone = '';
			} else {
				params.phoneNo = '';
				params.starPhone = starPhone;
			}

			const href = domainPathMap[this.backObj.path];
			const query = object2QueryParams(params);

			setTimeout(() => {
				window.location.href = `${href}?${query}`;
			}, 250);
		},

		_entryReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);

            if (!/^\d+$/.test(this.orderInfo.channel)) {
                this.orderInfo.channel = '333411';
            }
		},

		_actionTracking(name, time) {
			eventTracking(this.orderInfo, name, time);
		},

		pushHistory() {
			const state = { title: "title", url: "#" };
			window.history.pushState(state, "title", "");
		},

		stackPopHandle() {
			if (this.isStep2) {
				this.orderInfo.step = 'step1';
				saveOrderInfo(this.orderInfo);
			} else {
				this.fetchBackPath();
			}
			this.pushHistory();
		},
	},
}

</script>

<style lang="less" scoped type="text/less">
	.container_2212161330 {
		min-height: 100%;
		font-size: 0.15rem;
		background-color: #195dec;

		img {
			display: block;
			max-width: 100%;
		}

		.inputBox {
			.read-box {
				padding: 0.15rem;
				font-size: 0.13rem;
				color: #464646;
				line-height: 1.6;
				text-align: justify;

				.read-text {
					color: #ff832d;
					font-weight: 500;
				}
			}
		}

		.introduce {
			margin: 0.2rem auto 0.15rem;
			width: 3.45rem;
		}

		.remark {
			margin: 0.25rem 0.1rem 0;
			color: rgba(255, 255, 255, 0.8);
			font-size: 0.13rem;
			font-weight: 500;
			text-align: justify;
			line-height: 1.5;
		}

		.copyright {
			padding: 0.2rem 0 0.25rem;
			text-align: center;
			color: rgba(255, 255, 255, 0.6);
			font-size: 0.12rem;
			font-weight: 500;
			line-height: 1.6;
		}
	}
</style>


