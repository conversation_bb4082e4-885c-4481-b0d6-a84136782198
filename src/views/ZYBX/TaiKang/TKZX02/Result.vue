<template>
    <div class="container">
        <img class="icon" src="@/assets/imgs/send_success.png">
        <div class="product">
            <div class="submitButton" @click="buttonClick">
                立即投保
            </div>
        </div>
        <div class="countdown">{{ countdown }}秒后自动跳转</div>
    </div>
</template>

<script>

import {createOrderInfo, eventTracking, loadOrderInfo,} from "./src";

export default {
    name: "Result",
    components: {},
    data() {
        const orderInfo = createOrderInfo();
        return {
            orderInfo: orderInfo,
            countdown: 3,
            countdownTimer: null,
        }
    },

    mounted() {
        const orderInfo = loadOrderInfo();
        Object.assign(this.orderInfo, orderInfo);
        this._actionTracking(`支付结果页`);

        this.startCountDown();
    },

    beforeDestroy() {
        this.countdownTimer && clearInterval(this.countdownTimer);
    },

    methods: {
        startCountDown() {
            this.countdown = 3;
            this.countdownTimer = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    this.buttonClick();
                }
            }, 1000);
        },
        buttonClick() {
            this._actionTracking(`支付结果页-点击好的马上去(TK02Index1)`);

            this.countdownTimer && clearInterval(this.countdownTimer);
            setTimeout(() => {
                const {channelCode, channel, name1, idCard1, phoneNo, mTel, page} = this.orderInfo;
                this.$router.replace({
                    name: 'TK02Index1',
                    query: {cld: channelCode, channel, name1, idCard1, phoneNo, mTel, source: page, action: 'follow'},
                });
            }, 250);
        },
        _actionTracking(name, time = 0) {
            eventTracking(this.orderInfo, name, time);
        },
    },
}
</script>

<style scoped lang="less" type="text/less">

.container {
    width: 3.75rem;
    min-height: 100%;
    font-size: 0.15rem;
    background-color: #F2F2F2;

    .icon {
        display: block;
        margin: 0 auto;
        width: 80%;
    }

    .product {
        position: relative;
        margin: 0 auto;
        height: 2.54rem;
        width: 3.49rem;
        background: url("../../../../assets/imgs/common/popup600.png");
        background-size: 100%;

        .submitButton {
            position: absolute;
            left: 15%;
            bottom: 0.20rem;
            width: 70%;
            height: 0.50rem;
            line-height: 0.50rem;
            border-radius: 0.25rem;
            background: linear-gradient(180deg, #FF5B33, #FF2900);
            box-shadow: 0 0.03rem 0.1rem 0 rgba(255, 117, 1, 0.48), inset 0 -0.035rem 0 0 #FF5201;
            animation: banner_btn 1.35s linear infinite;

            font-size: 0.2rem;
            color: #FFFFFF;
            font-weight: 700;
            text-align: center;
        }
    }

    .countdown {
        color: #999999;
        text-align: center;
    }

    @keyframes banner_btn {
        0% {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
        40% {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
        70% {
            -webkit-transform: scale(.95);
            transform: scale(.95);
        }
        100% {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
    }
}

</style>
