<template>
	<div class="container_2212161330">
		<div class="banner">
			<img class="banner-img" src="@/assets/imgs/TaiKang/TKZX02/img_1_4.png">
		</div>
		<OrderInputBox :orderInfo="orderInfo" class="inputBox" @focus="onTextFocus" @input="onTextInput" @submit="submit">
			<template #policy>
				<div v-if="!isStep2" class="read-box">
					您已进入投保流程，为保障您的权益，请仔细阅读保险条款等内容，我们将安全记录您的操作行为。未注册的手机将自动创建泰康账户，点击按钮即表示您已阅读并同意
					<span class="read-text" @click.stop="onReadPolicy('隐私声明')">《隐私声明》</span>
					<span class="read-text" @click.stop="onReadPolicy('会员注册协议')">《会员注册协议》</span>
					<span class="read-text" @click.stop="onReadPolicy('泰康通行证用户服务协议')">《泰康通行证用户服务协议》</span>
					<span class="read-text" @click.stop="onReadPolicy('泰康通行证隐私政策')">《泰康通行证隐私政策》</span>
				</div>
				<div v-if="isStep2" class="read-box">
					<van-icon :color="orderInfo.checked ? '#FF7000':'#333333'" :name="orderInfo.checked ?'checked':'circle'" size="0.16rem" @click="orderInfo.checked = !orderInfo.checked" />
					我已阅读并同意
					<span class="read-text" @click.stop="onReadPolicy('投保须知')">《投保须知》</span>
					<span class="read-text" @click.stop="onReadPolicy('保险条款')">《保险条款》</span>
					<span class="read-text" @click.stop="onReadPolicy('特别约定')">《特别约定》</span>
					，并符合
					<span class="read-text" @click.stop="onReadPolicy('健康告知')">《健康告知》</span>
					投保条件。
				</div>
			</template>
		</OrderInputBox>
		<!--        <div class="remark">您已进入投保流程，为保障您的权益，请仔细阅读保险条款等内容，我们将安全记录您的操作行为。</div>-->
		<van-swipe :autoplay="4000" class="swipe-box" indicator-color="#FF832D">
			<van-swipe-item><img alt="泰康保障内容" src="@/assets/imgs/TaiKang/TKZX02/img_2_3.png"></van-swipe-item>
		</van-swipe>
		<div class="copyright">
			<p>泰康在线财产保险股份有限公司</p>
			<p>泰康重疾条款注册号: C00019932612021122224913</p>
			<p>互联网专属产品</p>
			<p>京ICP备09074081号-19</p>
		</div>
		<PolicyPopup :obj="policyObj" @checkAction="checkPolicy"></PolicyPopup>
		<ResultPopup :obj="resultObj" @click="goToResultLink"></ResultPopup>
		<BackPopup :obj="backObj" @click="goToBackLink"></BackPopup>
		<ImpReminder :obj="orderInfo" mode="red"></ImpReminder>
        <!--录屏-->
		<TKRecord ref="ref_record" :recordObj="orderInfo"></TKRecord>
	</div>
</template>

<script>
import { reviseIdCard, reviseName, GetAge, isCardNo, isPersonName, isPhoneNum, TraceLogInfoKeys } from "@/assets/js/common";
import { createMultiFreeOrder, fetchRoundRobinWithAgeZXResultNew, fetchRoundRobinWithPhoneOrderFilter, fetchStarPhoneV4, userWeComInfo, } from '@/api/insurance-api';
import ImpReminder from "./components/ImpReminder";
import PolicyPopup from "./components/PolicyPopup";
import OrderInputBox from "./components/OrderInputBox2";
import ResultPopup from "./components/ResultPopup";
import BackPopup from "./components/BackPopup";
import { createOrderInfo, eventTracking, isStarPhone, loadOrderInfo, msgToast, saveOrderInfo, } from "./src";
import { domainPathMap, } from "@/views/ZYBX/src";
import moment from "moment";
import TKRecord from "@/views/components/TKRecord";

export default {
	name: "TKZX02Index5",
	data() {
		const orderInfo = createOrderInfo();
		return {
			orderInfo: orderInfo,
			policyObj: { visible: false, page: '' },
			backObj: { visible: false, showTimer: false, path: '' },
			resultObj: { visible: false, showTimer: false, path: '' },
			submitTimerId: null,
			prevName: '',
		}
	},
	components: {
		BackPopup,
		ResultPopup,
		OrderInputBox,
		PolicyPopup,
		ImpReminder,
        TKRecord
	},
	computed: {
		isStep2() {
			return this.orderInfo.step == 'step2';
		},
	},
	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.init();

		this.$nextTick(() => {
			this.pushHistory();
			window.addEventListener("popstate", this.stackPopHandle, false);
		});
	},
	beforeDestroy() {
		window.removeEventListener("popstate", this.stackPopHandle);
	},
	methods: {
		//初始化
		init() {
			const inQry = this.$route.query || {};
			const orderInfo = loadOrderInfo();

			Object.assign(this.orderInfo, orderInfo);
			this.orderInfo.page = 'TKZX02Index5';
			this.orderInfo.productKey = TraceLogInfoKeys.iyb_tk_free_send;
			this.orderInfo.sourcePage = '保通参数泰康直投V240312';
			this.orderInfo.channel = inQry.channel || orderInfo.channel || '1';
			this.orderInfo.channelCode = inQry.cld || orderInfo.channelCode || '';

			if (this.orderInfo.received) {
				this._entryReport();
				return this.pushToResult('done', true);
			}

			inQry.relationCorpId = inQry.relationCorpId || ''; // 暖哇企微
			inQry.relationExtUserId = inQry.relationExtUserId || inQry.externalUserId || ''; // 暖哇企微

			Object.assign(this.orderInfo, inQry);

			if (isPhoneNum(this.orderInfo.phoneNo) || isStarPhone(this.orderInfo)) {
				this.orderInfo.step = 'step2';
			} else {
				this.orderInfo.step = 'step1';
			}

			this.fetchPhoneNumber();
		},

		fetchPhoneNumber() {
			const { m, phoneNo, channelEnMContent, channelEnMCode } = this.orderInfo;
			if ((!m && !channelEnMContent) || isPhoneNum(phoneNo) || isStarPhone(this.orderInfo)) {
				return this._entryReport();
			}

			const params = {};
			if (m) {
				params.encryptContent = m;
			} else {
				params.channelEnMContent = channelEnMContent;
				params.channelEnMCode = channelEnMCode;
			}
			fetchStarPhoneV4(params).then(res => {
				const { encryptPhone, showPhone } = res.data;
				this.orderInfo.mTel = encryptPhone;
				this.orderInfo.starPhone = showPhone;
				this.orderInfo.phoneNo = showPhone;
				if (isStarPhone(this.orderInfo)) {
					this.orderInfo.step = 'step2';
				}
				saveOrderInfo(this.orderInfo);
			}).finally(() => {
				return this._entryReport();
			});
		},

		onReadPolicy(page) {
			const policyObj = { visible: true, page: page };
			this.policyObj = policyObj;
		},

		checkPolicy() {
			this.orderInfo.checked = true;
			this.submit();
		},

		onTextInput({ key, value }) {
			let isChanged = false;
			const channel = this.orderInfo.channel;
			if (key === 'phone' && isPhoneNum(value)) {
				isChanged = true;
				this._actionTracking('首页-完成输入手机号');
			} else if (key === 'idCard' && isCardNo(value)) {
				isChanged = true;
				const birthday = value.substr(6, 8);
				const gender = value.substr(16, 1);
				this.orderInfo.birthday1 = moment(birthday).format('YYYY-MM-DD');
				this.orderInfo.gender1 = (+gender) % 2 == 1 ? '1' : '0';
				this._actionTracking('首页-完成输入身份证');
			} else if (key === 'name' && isPersonName(value)) {
				if (this.prevName != value) {
					this.prevName = value;
					this._actionTracking(`首页-完成输入姓名`);
				}
			}
			if (!isChanged) return;

			saveOrderInfo(this.orderInfo);
		},

		onTextFocus({ key, value }) {
			if (key === 'idCard' && !value) {
				this._actionTracking('首页-开始输入身份证');
			} else if (key === 'name' && !value) {
				this._actionTracking('首页-开始输入姓名');
			}
		},

		submit() {
			this.orderInfo.name1 = reviseName(this.orderInfo.name1);
			// this.orderInfo.idCard1 = reviseIdCard(this.orderInfo.idCard1);

			const { name1, phoneNo, starPhone, checked } = this.orderInfo;
			const { idCard1, idCardType1, birthday1, } = this.orderInfo;

			let msg = '';
			if (this.isStep2) {
				if (!isPersonName(name1)) {
					msg = '请输入正确的姓名';
				} else if (idCardType1 == '08' && idCard1.length != 15 && idCard1.length != 18 || idCardType1 == '01' && idCard1.length != 18) {
					msg = '请填写正确的投保人证件号';
				}
				else if (idCard1.length == 18 && !isCardNo(idCard1)) {
					msg = '请填写正确的投保人证件号';
				}
				else if (idCardType1 == '08' && !birthday1) {
					msg = '请填写正确的投保人出生日期';
				} else if (!checked) {
					return this.onReadPolicy('健康告知');
				}
				if (msg) {
					msgToast(msg);
					return false;
				}
				this._actionTracking('首页-点击第二步立即领取');
			} else {
				if (!isPhoneNum(phoneNo) && (phoneNo.length != 11 || phoneNo != starPhone)) {
					msg = '请输入正确的手机号';
				}
				if (msg) {
					msgToast(msg);
					return false;
				}
				this._actionTracking('首页-点击第一步立即领取');
				this.orderInfo.step = 'step2';
				saveOrderInfo(this.orderInfo);
				this.userWeComInfo();
				return false;
			}

			if (this.submitTimerId) return;
			this.submitTimerId = setTimeout(() => {
				this.submitTimerId && clearTimeout(this.submitTimerId);
				this.submitTimerId = null;
			}, 2000);

			this.submitOrder();
		},

		submitOrder() {
			const { name1, phoneNo, relation, traceBackUuid, productKey } = this.orderInfo;
			const { infoNo, channel, page, mTel, channelCode, sourcePage } = this.orderInfo;
			const { idCard1, idCardType1, birthday1, gender1 } = this.orderInfo;

			const extendParams = {};
			if (idCardType1 == '08') {
				const obj = {
					holderIdCardType: idCardType1,
					holderBirthday: birthday1,
					holderSex: gender1,
				}
				Object.assign(extendParams, obj);
			}

			const params = {
				page,
				sourcePage,
				infoNo,
				holderName: name1,
				holderIdCard: idCard1,
				holderPhone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				relation,
				planKeys: [productKey],
				channelId: channel,
				operatorPhone: mTel || phoneNo,
				channelCode: channelCode || '',
				traceBackUuid: traceBackUuid,
				extendParams: JSON.stringify(extendParams),
			}

			this.$toast.loading({
				message: '订单提交中\n请稍候',
				forbidClick: true,
				duration: 0,
			});

			createMultiFreeOrder(params).then(response => {
				const { code, data } = response.data || {};
				if (code != 2000) {
					return this.pushToResult('error', true);
				}

				const { success, orderNo } = data || {};
				if (orderNo) {
					this.orderInfo.infoNo = orderNo;
				}
				this.pushToResult(success ? 'success' : 'error', true);
			});
		},

		userWeComInfo() {
			const { relationCorpId, relationExtUserId, phoneNo, mTel } = this.orderInfo;
			if (!relationExtUserId) return;

			const params = {
				corpId: relationCorpId,
				externalUserId: relationExtUserId,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
			}
			userWeComInfo(params).then(res => {

			});
		},

		pushToResult(state, save) {
			if (save) {
				this.orderInfo.received = 1;
				saveOrderInfo(this.orderInfo);
			}

			this.fetchResultPath();
		},

		fetchResultPath() {
			const { channel, phoneNo, idCard1, mTel } = this.orderInfo;

			const params = {
				channelId: channel,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				idCard: idCard1,
				robinKey: 'tc_tk_to_mf',
			}

			fetchRoundRobinWithAgeZXResultNew(params).then(result => {
				const { path } = result.data || {};
				this.resultObj.path = path || '';
			}).finally(() => {
				this.$toast.clear(true);

				if (!domainPathMap[this.resultObj.path]) {
					this.resultObj.path = 'TK30Index1';
				}

				if (channel < 1000) {
					this.resultObj.path = 'TK30Index1';
				}

				this.resultObj = { ...this.resultObj, visible: true, showTimer: true };
			});
		},

		goToResultLink() {
			this._actionTracking(`首页-点击弹框好的马上去(${this.resultObj.path})`);

			const { channelCode, channel, name1, phoneNo, starPhone, mTel, page, sourcePage, } = this.orderInfo;
			const { idCardType1, birthday1, gender1, idCard1, } = this.orderInfo;

			const age = GetAge(idCard1);
			const relation = age < 18 ? 3 : 1;
			let href = domainPathMap[this.resultObj.path];
			href = `${href}?channel=${channel}&cld=${channelCode}&name${relation}=${name1}&idCard${relation}=${idCard1}&idCardType${relation}=${idCardType1}&birthday${relation}=${birthday1}&gender${relation}=${gender1}&relation=${relation}&source=${page}&mTel=${mTel}&action=follow&sourcePage=${sourcePage}`;

			if (isPhoneNum(phoneNo)) {
				href = `${href}&phoneNo=${phoneNo}&starPhone=`;
			} else {
				href = `${href}&phoneNo=&starPhone=${starPhone}`;
			}

			setTimeout(() => {
				window.location.href = href;
			}, 250);
		},

		fetchBackPath() {
			const { channel, phoneNo, idCard1, mTel } = this.orderInfo;

			const params = {
				channelId: channel,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				robinKey: 'tc_tk_free_send_back',
			}

			if (isCardNo(idCard1)) {
				params.idCard = idCard1;
				fetchRoundRobinWithAgeZXResultNew(params).then(result => {
					const { path } = result.data || {};
					this.backObj.path = path || '';
				}).finally(() => {
					this.backPathHandler();
				});
			} else {
				fetchRoundRobinWithPhoneOrderFilter(params).then(result => {
					const { path } = result.data || {};
					this.backObj.path = path || '';
				}).finally(() => {
					this.backPathHandler();
				});
			}
		},

		backPathHandler() {
			if (!domainPathMap[this.backObj.path] || this.orderInfo.channel < 1000) {
				this.backObj.path = 'NWTK02Index1';
			}
			this.backObj = { ...this.backObj, visible: true };
			this._actionTracking(`首页-点击返回按钮(${this.backObj.path})`);
		},

		goToBackLink() {
			this._actionTracking(`首页-点击返回弹框图片(${this.backObj.path})`);

			let href = domainPathMap[this.backObj.path];
			const {
				channel, name1, phoneNo, starPhone, idCard1, mTel, page, channelCode, sourcePage,
			} = this.orderInfo;

			href = `${href}?cld=${channelCode}&channel=${channel}&name1=${name1}&idCard1=${idCard1}&mTel=${mTel}&action=back&source=${page}&sourcePage=${sourcePage}`;
			if (isPhoneNum(phoneNo)) {
				href = `${href}&phoneNo=${phoneNo}&starPhone=`;
			} else {
				href = `${href}&phoneNo=&starPhone=${starPhone}`;
			}
			setTimeout(() => {
				window.location.href = href;
			}, 250);
		},

		_entryReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('首页', domContentLoadedEventEnd - fetchStart);

			if (!/^\d+$/.test(this.orderInfo.channel)) {
				this.orderInfo.channel = '333411';
			}
		},

		_actionTracking(name, time) {
			eventTracking(this.orderInfo, name, time);
		},

		pushHistory() {
			const state = { title: "title", url: "#" };
			window.history.pushState(state, "title", "");
		},

		stackPopHandle() {
			if (this.isStep2) {
				this.orderInfo.step = 'step1';
				saveOrderInfo(this.orderInfo);
			} else {
				this.fetchBackPath();
			}
			this.pushHistory();
		},
	},
}

</script>

<style lang="less" scoped type="text/less">
	/deep/ .van-swipe__indicator {
		background-color: #999999;
	}

	.container_2212161330 {
		min-height: 100%;
		font-size: 0.15rem;
		background-color: #f0f0f0;

		.banner {
			position: relative;

			.expire-container {
				position: absolute;
				width: 2.35rem;
				height: 0.26rem;
				line-height: 0.3rem;
				left: 50%;
				top: 0.42rem;
				transform: translateX(-50%);
				text-align: center;
				border-radius: 0.13rem;
				color: #333333;
				font-weight: 500;
				background-color: rgba(255, 255, 255, 0.2);

				.expire-time {
					margin-left: 0.1rem;
					font-weight: 700;
					color: #f7333a;
				}
			}

			.banner-img {
				display: block;
				width: 100%;
			}
		}

		.inputBox {
			margin: -1.3rem 0.15rem 0.1rem;

			.read-box {
				padding: 0.15rem;
				font-size: 0.13rem;
				color: #464646;
				line-height: 1.6;
				text-align: justify;

				.read-text {
					color: #ff832d;
					font-weight: 500;
				}
			}
		}

		.swipe-box {
			margin: 0.2rem auto 0;
			width: 3.55rem;
			box-sizing: border-box;

			img {
				width: 100%;
				display: block;
			}
		}

		.remark {
			margin: 0.25rem 0.1rem 0;
			color: rgba(255, 255, 255, 0.8);
			font-size: 0.13rem;
			font-weight: 500;
			text-align: justify;
			line-height: 1.5;
		}

		.copyright {
			padding: 0.2rem 0 0.25rem;
			text-align: center;
			color: rgba(51, 51, 51, 0.6);
			font-size: 0.13rem;
			font-weight: 500;
			line-height: 0.2rem;
		}
	}
</style>


