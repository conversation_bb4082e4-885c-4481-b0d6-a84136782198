<template>
    <van-popup v-model="obj.visible" class="container_2210121530" position="bottom" round>
        <van-area :area-list="areaList" :value="obj.code" title="地区" @cancel="onCancelClick" @confirm="onOkClick"/>
    </van-popup>
</template>
<script>
import {areaCityObj} from '@/utils/AreaCity';

export default {
    name: "HHAreaPopup",
    props: {obj: Object},
    data() {
        return {
            areaList: areaCityObj,
        }
    },
    methods: {
        onOkClick(data) {
            this.obj.visible = false;
            this.$emit('confirm', data);
        },
        onCancelClick() {
            this.obj.visible = false;
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2210121530 {
    width: 3.75rem;
    left: calc((100% - 3.75rem) * 0.5);
    background-color: unset;
}

</style>


