<template>
	<van-popup v-model="obj.v" class="container_2211041610" position="center">
		<div class="container">
			<p class="header">证件类型</p>
			<div class="content">
				<van-radio-group v-model="obj.type">
					<van-radio v-for="v in typeList" class="radio-item" :key="v.key" :name="v.type" @click="onRadioChange(v)" checked-color="#F08E39">{{ v.key }}</van-radio>
				</van-radio-group>
			</div>
		</div>
		<div class="button" @click="closeAction"></div>
	</van-popup>
</template>

<script>

export default {
	name: "HHICTypeViewer",
	props: { obj: Object, },
	data() {
		return {
			typeList: [
				{ key: '居民身份证', value: '身份证号', type: '01' },
				{ key: '外国人永久居留身份证', value: '永居证号', type: '08' },
			]
		}
	},
	methods: {
		onRadioChange(obj) {
			this.$emit('click', { relation: this.obj.relation, type: obj.type });
			this.obj.v = false;
		},
		closeAction() {
			this.obj.v = false;
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2211041610 {
		width: 3.2rem;
		background-color: unset;

		.container {
			padding: 0.05rem 0 0 0.15rem;
			border-radius: 0.1rem;
			background-color: #ffffff;

			.header {
				padding: 0.15rem 0 0.1rem;
				font-size: 0.17rem;
				font-weight: bold;
				border-bottom: #f2f2f2 1px solid;
			}

			.content {
				margin: 0.1rem 0;
				font-size: 0.15rem;
				line-height: 0.25rem;
				color: #333333;

				.radio-item {
					padding-bottom: 0.15rem;
				}
			}
		}

		.button {
			margin: 0.1rem auto;
			width: 0.32rem;
			height: 0.32rem;
			background: url("~@/assets/imgs/common/sprites.png") no-repeat -1.92rem 0;
			background-size: 3.2rem 6.4rem;
		}
	}
</style>
