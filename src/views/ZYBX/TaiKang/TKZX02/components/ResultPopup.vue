<template>
    <van-popup v-model="obj.visible" :close-on-click-overlay="false" class="container_2303200935" position="center">
        <div class="link-img">
            <img :class="{'img-pzh':isPZH}" :src="popupImg" class="img">
            <div class="bottom" :style="{bottom:obj.isExpand || isPZH ? '0.15rem' : '0.35rem'}">
                <div v-if="obj.isExpand">本次领取的电信新卡为<b>限时免费活动</b></div>
                <div v-if="obj.isExpand">请<b>尽快激活</b>，领取补贴话费！</div>
                <div :class="{'link-btn-pzh':isPZH}" class="link-btn" @click="onImgClick">立即{{ isPZH ? '抢购' : '领取' }}（{{ time }}s）</div>
            </div>
        </div>
    </van-popup>
</template>

<script>
import img05 from "@/assets/imgs/common/popup600_5.png"
import img08 from "@/assets/imgs/TaiKang/TKZX02/img_8.png"

export default {
    name: "resultPopup",
    props: {
        obj: Object
    },
    data() {
        return {
            imgList: {img05, img08},
            timeInterval: null,
            time: 3,
        }
    },
    computed: {
        isPZH() {
            const path = this.obj.path || '';
            return path.includes('CGWPZH01');
        },
        popupImg() {
            let path = this.obj.path || '';

            if (this.isPZH) {
                return this.imgList.img08;
            }

            return this.imgList.img05;
        }
    },
    watch: {
        obj(data) {
            if (data.visible) {
                this.timeInterval && clearInterval(this.timeInterval);
                if (!data.showTimer) return;
                this.time = 3;
                this.timeInterval = setInterval(() => {
                    if (this.time > 1) {
                        this.time--
                    } else {
                        this.onImgClick()
                        clearInterval(this.timeInterval)
                    }
                }, 1000);
            }
        },
    },
    mounted() {

    },
    beforeDestroy() {
        this.timeInterval && clearInterval(this.timeInterval);
    },
    methods: {
        onImgClick() {
            this.obj.visible = false;
            this.$emit('click');
        },
        onClose() {
            this.timeInterval && clearInterval(this.timeInterval);
            this.obj.visible = false;
            this.$emit('close');
        }
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2303200935 {
    width: 3.50rem;
    background-color: unset;

    .link-img {
        position: relative;

        .img {
            margin: 0 auto;
            width: 3.50rem;
            border-radius: 10px;
            display: block;
        }

        .img-pzh {
            width: 2.90rem;
        }

        .bottom {
            position: absolute;
            bottom: 0.15rem;
            left: 0;
            right: 0;
            color: #FFFFFF;
            text-align: center;
            font-size: 0.12rem;
            line-height: 1.25;

            .link-btn {
                margin: 0.05rem auto 0;
                width: 2.2rem;
                height: 0.45rem;
                line-height: 0.45rem;
                border-radius: 0.5rem;
                text-align: center;
                background-color: #FD8A25;
                font-size: 0.16rem;
                color: #FFFFFF;
            }

            .link-btn-pzh {
                background-color: #cc311e;
            }
        }
    }
}

</style>

