<template>
    <van-popup v-model="obj.visible" class="container_2302140945" position="bottom" round>
        <div class="header" @click="close">{{ title }}</div>
        <div id="popup-content" class="content">
            <Policy1 v-if="obj.page == '隐私声明'"></Policy1>
            <Policy2 v-if="obj.page == '会员注册协议'"></Policy2>
            <Policy3 v-if="obj.page == '泰康通行证用户服务协议'"></Policy3>
            <Policy4 v-if="obj.page == '泰康通行证隐私政策'"></Policy4>
            <Policy5 v-if="obj.page == '投保须知'"></Policy5>
            <!-- <Policy6 v-if="obj.page == '特别约定'"></Policy6> -->
            <Policy7 v-if="obj.page == '健康告知'"></Policy7>
            <OrderRule3 v-if="obj.page == '活动规则3'"></OrderRule3>
            <PDFViewer v-if="fileUrl" :file="fileUrl"></PDFViewer>
            <img v-for="(image,index) in imageList" :key="index" v-lazy="image" class="content-image">
        </div>
        <div class="footer">
            <div class="button" @click="checkClose">
                我已逐页阅读并同意
            </div>
        </div>
    </van-popup>
</template>

<script>
import PDFViewer from "@/views/ZYBX/components/PDFViewer";
import Policy1 from "./policies/Policy1";
import Policy2 from "./policies/Policy2";
import Policy3 from "./policies/Policy3";
import Policy4 from "./policies/Policy4";
import Policy5 from "./policies/Policy5";
import Policy6 from "./policies/Policy6";
import Policy7 from "./policies/Policy7";
import OrderRule3 from "./OrderRule3";
import {pdfFileObj} from "../src";

const imageDir = {
    '保险条款': {path: 'TaiKang/TKZX01/policy/dir2', count: 20, name: 'img_', suffix: 'jpg', prefix: ''},
    '泰康重疾保险条款': {path: 'TaiKang/TKZX01/policy/dir2', count: 20, name: 'img_', suffix: 'jpg', prefix: ''},
    '泰康飞铁保保险条款': {path: 'IYBTK/TKZX01/dir1', count: 10, name: 'img_', suffix: 'jpg', prefix: ''},
    '太平任逍遥保险条款': {path: 'IYB/IYBTPZX02/dir1', count: 10, name: 'img_', suffix: 'png', prefix: '0'},
    '平安重疾保险条款': {path: 'IYB/IYBPA/IYBPAZX03/dir1', count: 28, name: 'img_', suffix: 'png', prefix: '0'},
    '平安意外保险条款': {path: 'IYB/IYBPA/IYBPAZX03/dir2', count: 7, name: 'img_', suffix: 'png', prefix: '0'},
    '泰康千元重疾保险条款': {path: 'IYB/IYBTKZX01/dir1', count: 23, name: 'img_', suffix: 'png', prefix: '0'},
}

export default {
    name: "PolicyPopup",
    components: {
        OrderRule3, Policy1, Policy2, Policy3, Policy4, Policy5,
        Policy6, Policy7, PDFViewer,
    },
    props: {
        obj: Object,
    },
    watch: {
        obj() {
            setTimeout(() => {
                this.scrollToTop();
            }, 250);
        },
    },
    computed: {
        title() {
            const page = this.obj.page;
            if (page.indexOf('保险条款') >= 0) {
                return '保险条款';
            }

            return page.split('#')[0].replace(/\d/g, '');
        },
        fileUrl() {
            return pdfFileObj[this.obj.page] || '';
        },
        imageList() {
            const cdnLink = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/picture';
            const page = this.obj.page;
            const imageList = [];
            const splits = page.split('#');
            for (const v of splits) {
                const obj = imageDir[v];
                if (!obj) continue;

                const {path, count, name, prefix, suffix} = obj;
                for (let i = 0; i < count; i++) {
                    const idx = i > 9 ? i : `${prefix}${i}`;
                    let temp = `${cdnLink}/${path}/${name}${idx}.${suffix}?version=${this.version}`;
                    imageList.push(temp);
                }
            }

            return imageList;
        },
    },
    methods: {
        close() {
            this.obj.visible = false;
        },
        checkClose() {
            this.obj.visible = false;
            this.$emit('checkAction');
        },
        scrollToTop() {
            document.getElementById('popup-content').scrollTop = 0
        }
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2302140945 {
    height: 80%;
    width: 3.75rem;
    left: calc((100% - 3.75rem) * 0.5);

    display: flex;
    flex-direction: column;

    .header {
        position: relative;
        height: 0.5rem;
        line-height: 0.5rem;
        color: #333333;
        font-size: 0.17rem;
        font-weight: 700;
        text-align: center;

        &::after {
            content: "";
            position: absolute;
            top: 0.17rem;
            right: 0.15rem;
            height: 0.16rem;
            width: 0.16rem;
            background: url("~@/assets/imgs/common/sprites.png") no-repeat -1.12rem 0;
            background-size: 1.6rem 3.2rem;
        }
    }

    .content {
        overflow: auto;
        flex: 1;

        .content-image {
            display: block;
            width: 100%;
        }
    }

    .footer {
        background-color: #FFFFFF;
        border-top: #F2F2F2 1px solid;

        .button {
            margin: 0.08rem 0.2rem;
            height: 0.44rem;
            line-height: 0.44rem;
            border-radius: 0.22rem;
            font-size: 0.16rem;
            font-weight: 500;
            text-align: center;
            color: #FFFFFF;
            background: linear-gradient(to right, rgba(255, 20, 28, 0.65), rgba(255, 0, 0, 0.8));
        }
    }
}

</style>
