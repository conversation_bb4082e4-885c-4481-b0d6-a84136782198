<template>
    <div class="container_2302221700">
        <PDFViewer :file="fileUrl"></PDFViewer>
    </div>
</template>

<script>

import PDFViewer from "@/views/ZYBX/components/PDFViewer";
import {pdfFileObj} from "../src";

export default {
    name: "HHFileViewer",
    components: {PDFViewer},
    props: {fileName: String},
    computed: {
        fileUrl() {
            if (this.fileName.indexOf('http') >= 0) {
                return this.fileName;
            }
            return pdfFileObj[this.fileName || ''];
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2302221700 {
    height: 100%;
}

</style>
