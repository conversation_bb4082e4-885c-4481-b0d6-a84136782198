<template>
    <van-popup v-model="obj.visible" :close-on-click-overlay="false" class="container_2210121530" position="center">
        <div class="content">
            <img class="header" src="@/assets/imgs/TaiKang/TKZX02/img_6.png">
            <div class="box" @click="clearTimer">
                <div class="box-mark">{{obj.count || 100}}元联通话费<b> 新卡 </b>收件信息</div>
                <div class="box-item">
                    <span class="box-item-label">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</span>
                    <div class="box-item-input">{{ starMarkName }}</div>
                </div>
                <div class="box-item">
                    <span class="box-item-label">手机号码</span>
                    <div class="box-item-input">{{ starMarkPhone }}</div>
                </div>
                <div class="box-item">
                    <span class="box-item-label">身份证号</span>
                    <div class="box-item-input">{{ starMarkIdCard }}</div>
                </div>
                <div class="box-item" @click="onSelect">
                    <span class="box-item-label">地&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;区</span>
                    <div :class="{'input-placeholder':!obj1.province}" class="box-item-input">
                        {{ (obj1.province + obj1.city + obj1.county) || '点击选择收货地区' }}
                    </div>
                </div>
                <div class="box-item">
                    <span class="box-item-label">详细地址</span>
                    <input v-model="obj1.address" class="box-item-input"
                           placeholder="具体到街道、门牌号、小区等" type="text">
                </div>
                <div class="box-bottom-remark">*激活新卡后24小时内,<b>{{obj.count || 100}}元话费自动到账 请勿手动充值</b></div>
            </div>
            <div class="bottom">
                <div class="button" @click="confirmAction">免费领取<span v-if="timerId">（{{ countdown }}s）</span></div>
            </div>
        </div>
    </van-popup>
</template>

<script>

import {isPhoneNum, secureIdCard, secureName, securePhone} from "@/assets/js/common";

export default {
    name: "HHNextPopup",
    props: {obj: Object, obj1: Object},
    data() {
        return {
            timerId: null,
            countdown: 6,
        }
    },
    computed: {
        starMarkPhone() {
            const {phoneNo, starPhone} = this.obj1;
            return isPhoneNum(phoneNo) ? securePhone(phoneNo) : starPhone;
        },
        starMarkName() {
            return secureName(this.obj1.name1);
        },
        starMarkIdCard() {
            return secureIdCard(this.obj1.idCard1);
        },
    },
    watch: {
        obj(data) {
            if (data.visible && data.showTimer) {
                this.countdown = 6;
                this.timerId = setInterval(() => {
                    if (this.countdown > 0) {
                        return this.countdown--
                    }
                    this.timeEndAction();
                }, 1000);
            }
        },
    },
    beforeDestroy() {
        this.clearTimer();
    },
    methods: {
        confirmAction() {
            this.clearTimer();
            this.$emit('click');
        },
        timeEndAction() {
            this.clearTimer();
            this.$emit('timeEnd');
        },
        onSelect() {
            this.$emit('select',);
        },
        clearTimer() {
            this.timerId && clearInterval(this.timerId);
            this.timerId = null;
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2210121530 {
    background-color: unset;

    .content {
        position: relative;
        width: 3.27rem;
        height: 5.10rem;
        background: url("~@/assets/imgs/TaiKang/TKZX02/img_7.png") no-repeat center bottom;
        background-size: 3.27rem 3.92rem;

        .header {
            margin: 0rem auto 0;
            display: block;
            width: 3.0rem;
        }

        .box {
            margin: 0 auto;
            width: 3.0rem;
            background-color: #FFFFFF;

            .box-mark {
                padding-top: 0.10rem;
                color: #F6552B;
                font-size: 0.15rem;
                text-align: center;
            }

            .box-bottom-remark {
                margin: 0 0 0.05rem;
                font-size: 0.11rem;
                line-height: 1.4;
                text-align: center;
                color: #666666;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .box-item {
                display: flex;
                align-items: center;

                .box-item-label, .box-item-input {
                    font-size: 0.15rem;
                }

                .box-item-label {
                    width: 0.90rem;
                    font-weight: 700;
                    text-align: center;
                }

                .box-item-input {
                    line-height: 0.45rem;
                    height: 0.45rem;
                    flex: 1;
                    border: 0;
                    outline: none;
                    background-color: unset;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }

                .input-placeholder {
                    color: #666666;
                }
            }

            .box-item ~ .box-item {
                border: solid #F0F0F0;
                border-width: 1px 0 0 0;
            }
        }

        .bottom {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0.30rem;

            .button {
                margin: 0 auto;
                width: 2.2rem;
                height: 0.44rem;
                line-height: 0.44rem;
                border-radius: 0.22rem;
                text-align: center;
                background-color: #FD8A25;
                font-size: 0.16rem;
                font-weight: bold;
                color: #FFFFFF;
            }
        }
    }
}

</style>


