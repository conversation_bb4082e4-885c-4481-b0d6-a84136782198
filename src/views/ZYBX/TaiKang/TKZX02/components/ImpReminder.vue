<template>
    <Popup v-model="obj.reminderVisible" :closeOnClickModal="true" class="container_2212191300" position="bottom">
        <img v-if="mode=='light'" class="header" src="@/assets/imgs/TaiKang/tk_slogan_1.png">
        <img v-else-if="mode=='red'" class="header" src="@/assets/imgs/TaiKang/tk_slogan_2.png">
        <img v-else class="header" src="@/assets/imgs/TaiKang/tk_slogan.png">
        <div class="body">
            <div class="title">温馨提示，您已进入投保流程：</div>
            <div class="content">请仔细阅读保障责任、保险条款、投保须知、特别约定等信息，为维护您的合法权益，您的操作将被记录。</div>
        </div>
        <div class="footer" @click="onClick">
            我知道了
        </div>
    </Popup>
</template>

<script>
import {Popup} from 'mint-ui';

export default {
    name: "ImpReminder", // 重要提醒
    components: {Popup},
    props: {
        obj: Object,
        mode: {
            type: String,
            default: 'dark',
        },
    },
    data() {
        return {
            countTimer: null,
        }
    },
    mounted() {
        this.countTimer = setTimeout(() => {
            this.obj.reminderVisible = false;
        }, 3000);
    },
    beforeDestroy() {
        this.countTimer && clearTimeout(this.countTimer);
    },
    methods: {
        onClick() {
            this.obj.reminderVisible = false;
        },
    },
}

</script>

<style lang="less" scoped type="text/less">

.container_2212191300 {
    width: 100%;
    padding-bottom: 0.2rem;
    border-radius: 0.15rem 0.15rem 0 0;

    .header {
        width: 100%;
        height: 0.68rem;
    }

    .body {
        padding: 0.15rem 0.15rem 0.20rem;

        .title {
            font-size: 0.16rem;
            font-weight: 500;
            line-height: 0.35rem;
        }

        .content {
            color: #333333;
            font-size: 0.14rem;
            line-height: 0.20rem;
        }
    }

    .footer {
        margin: 0 auto;
        width: 3.20rem;
        height: 0.48rem;
        line-height: 0.48rem;
        border-radius: 0.24rem;
        font-size: 0.16rem;
        font-weight: 500;
        text-align: center;
        color: #FFFFFF;
        background: linear-gradient(165deg, #FD9C52, #FF6600);
    }
}

</style>

