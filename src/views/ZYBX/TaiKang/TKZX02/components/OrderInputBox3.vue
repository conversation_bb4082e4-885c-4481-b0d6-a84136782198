<template>
    <div class="container_2302131855">
        <div class="header">
            <slot name="mark"></slot>
            <div class="header-top">
                {{ title.text1 }}
                <span class="header-top-color">&nbsp;&nbsp;{{ title.text2 }}</span>
            </div>
        </div>
        <div v-if="isStep2">
            <div class="box-item">
                <span class="label">真实姓名</span>
                <input v-model="orderInfo.name1" class="input" maxlength="25"
                       placeholder="信息保密，仅用于生成保单" type="text" @focus="onFocus('name',$event.target.value)"
                       @input="onInput('name',$event.target.value)">
            </div>
            <div class="box-item">
                <span class="label">身份证号</span>
                <input class="input" type="text" placeholder="信息保密，仅用于生成保单"
                       v-model="orderInfo.idCard1" maxlength="18"
                       @input="onInput('idCard',$event.target.value)"
                       @focus="onFocus('idCard',$event.target.value)">
            </div>
            <div class="box-item">
                <span class="label">手机号码</span>
                <input :value="phoneFormat" class="input" placeholder="您的信息将被严格保密" readonly type="tel">
            </div>
        </div>
        <div v-else>
            <div class="box-item">
                <span class="label">手机号码</span>
                <input v-model="orderInfo.phoneNo" class="input" maxlength="11"
                       onkeyup="value=value.replace(/\D/g,'')"
                       placeholder="您的信息将被严格保密" type="tel" @focus="onFocus('phone',$event.target.value)"
                       @input="onInput('phone',$event.target.value)">
            </div>
        </div>
        <div class="submitButton" @click="submit">免费投保
            <img alt="hand" class="hand" src="@/assets/imgs/common/icon_hand2.png">
        </div>
        <slot name="policy"></slot>
    </div>
</template>

<script>

import {isPhoneNum, star_marked_phone} from "@/assets/js/common";

export default {
    name: "UUInputBox",
    props: {
        orderInfo: Object
    },
    data() {
        return {
            show: false,
            scrollTimer: null,
        }
    },
    computed: {
        isStep2() {
            return this.orderInfo.step == 'step2';
        },
        title() {
            if (this.isStep2) {
                return {text1: '仅剩1步', text2: '免费领取'};
            }
            return {text1: '仅剩2步', text2: '免费领取'};
        },
        phoneFormat() {
            const {phoneNo, starPhone} = this.orderInfo;
            return star_marked_phone(isPhoneNum(phoneNo) ? phoneNo : starPhone);
        }
    },
    methods: {
        submit() {
            this.$emit('submit');
        },
        onInput(key, value) {
            this.$emit('input', {key, value});
        },
        onFocus(key, value) {
            this.$emit('focus', {key, value});
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

/deep/ .van-number-keyboard__title {
    font-size: 1em;
}

.container_2302131855 {
    position: relative;
    margin: 0 0.15rem;
    padding: 1px 0 0.15rem;
    color: #333333;
    font-size: 0.16rem;
    border-radius: 0.10rem;
    background-color: #FFFFFF;;
    box-shadow: 0 1px 10px 0 rgba(200, 133, 85, 0.24);

    &::before {
        position: absolute;
        content: ' ';
        width: 0.50rem;
        height: 1.15rem;
        left: -0.15rem;
        top: 50%;
        transform: translateY(-50%);
        background: url("~@/assets/imgs/common/hand_left.png") no-repeat center;
        background-size: 100%;
    }

    &::after {
        position: absolute;
        content: ' ';
        width: 0.50rem;
        height: 1.15rem;
        right: -0.15rem;
        top: 50%;
        transform: translateY(-50%);
        background: url("~@/assets/imgs/common/hand_right.png") no-repeat center;
        background-size: 100%;
    }

    .header {
        margin: 0.18rem 0 0.20rem;

        .header-top {
            position: relative;
            font-size: 0.18rem;
            font-weight: 700;
            text-align: center;

            &::before {
                position: absolute;
                content: ' ';
                width: 0.20rem;
                height: 0.20rem;
                margin: -0.02rem 0 0 -0.25rem;
                background: url("~@/assets/imgs/common/icon_arrows_down.png") no-repeat center;
                background-size: 100%;
            }

            &::after {
                position: absolute;
                content: ' ';
                width: 0.20rem;
                height: 0.20rem;
                margin: -0.02rem 0 0 0.05rem;
                background: url("~@/assets/imgs/common/icon_arrows_down.png") no-repeat center;
                background-size: 100%;
            }

            .header-top-color {
                color: #ff3300;
            }
        }

        .header-bottom {
            margin-top: 0.05rem;
            text-align: center;
            color: #737680;
            font-size: 0.12rem;
        }
    }

    .box-item {
        margin: 0 0.15rem 0.12rem;
        display: flex;
        align-items: center;
        border-radius: 0.20rem;
        border: 1px solid #DDDDDD;
        background-color: #F8F8F8;

        .label, .input {
            font-size: 0.15rem;
        }

        .label {
            width: 0.9rem;
            font-weight: 700;
            text-align: center;
        }

        .input {
            height: 0.5rem;
            flex: 1;
            border: 0;
            outline: none;
            background-color: unset;
        }
    }

    .submitButton {
        position: relative;
        margin: 0.20rem 0.20rem 0;
        padding: 0.16rem 0;
        line-height: 1.0;
        color: #FFFFFF;
        font-size: 0.20rem;
        font-weight: 700;
        text-align: center;
        border-radius: 0.26rem;
        background: linear-gradient(180deg,#fdbe37,#ec6d15);
        animation: button_animation 1.35s linear infinite;

        .hand {
            position: absolute;
            top: 0.30rem;
            left: 75%;
            width: 18%;
            animation: hand_animation 1.35s linear infinite;
        }
    }

    @keyframes button_animation {
        0% {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
        40% {
            -webkit-transform: scale(.95);
            transform: scale(.95);
        }
        60% {
            -webkit-transform: scale(.97);
            transform: scale(.97);
        }
        100% {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
    }
    @keyframes hand_animation {
        0% {
            top: 0.10rem;
            left: 70%;
        }
        45% {
            top: 0.25rem;
            left: 75%;
        }
        70% {
            top: 0.25rem;
            left: 75%;
        }
        100% {
            top: 0.10rem;
            left: 70%;
        }
    }
}

</style>

