<template>
    <van-popup v-model="obj.visible" class="container_2302281630" position="center">
        <div class="content" @click="onOpen">
            <p class="title1">{{ textObj.text1 }}</p>
            <div class="title2">
                <span v-html="textObj.text2"></span>
                <span class="bold">{{ textObj.text3 }}</span>
            </div>
            <p class="title2">
                <span v-if="textObj.text5" class="bold">{{ textObj.text5 }}</span>
                <span v-html="textObj.text4"></span>
            </p>
            <p v-if="obj.showTimer" class="timer">{{ countDown }}秒后自动跳转</p>
        </div>
        <div class="button" @click="onClose"></div>
    </van-popup>
</template>

<script>

export default {
    name: "BackPopup",
    props: {obj: Object},
    data() {
        return {
            intervalTimer: null,
            countDown: 3,
        }
    },
    computed: {
        textObj() {
            return {text1: '❤ 专属红包权益', text2: '有一份保额', text3: '600万', text4: '「全面百万保障」等着您',};
        }
    },
    watch: {
        obj(data) {
            this.intervalTimer && clearInterval(this.intervalTimer);
            if (!data.showTimer || !data.visible) return;
            this.countDown = 3;
            this.intervalTimer = setInterval(() => {
                this.countDown--;
                if (this.countDown <= 0) {
                    this.onOpen();
                }
            }, 800);
        },
    },
    mounted() {

    },
    beforeDestroy() {
        this.intervalTimer && clearInterval(this.intervalTimer);
    },
    methods: {
        onOpen() {
            this.intervalTimer && clearInterval(this.intervalTimer);
            this.obj.visible = false;
            this.$emit('click');
        },
        onClose() {
            this.intervalTimer && clearInterval(this.intervalTimer);
            this.obj.visible = false;
            this.$emit('close');
        }
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2302281630 {
    width: 3.0rem;
    background-color: unset;

    .content2 {
        position: relative;

        .img {
            margin: 0 auto;
            width: 2.90rem;
            border-radius: 10px;
            display: block;
        }

        .link-btn {
            position: absolute;
            display: block;
            width: 2.2rem;
            height: 0.45rem;
            line-height: 0.45rem;
            border-radius: 0.5rem;
            text-align: center;
            background-color: #cc311e;
            font-size: 0.16rem;
            color: #FFFFFF;
            bottom: 0.10rem;
            left: 50%;
            transform: translateX(-50%);
        }
    }

    .content {
        overflow: hidden;
        width: 3.0rem;
        height: 3.7rem;
        background: url("../../../../../assets/imgs/common/redPacket.png") no-repeat;
        background-size: 100% auto;

        .title1, .title2, .title3, .timer {
            color: #F8E0B7;
            font-weight: 500;
            text-align: center;
        }

        .title1 {
            margin-top: 0.5rem;
            font-size: 0.22rem;
        }

        .title2 {
            margin-top: 0.20rem;
            font-size: 0.16rem;

            .bold {
                margin: 0 0.05rem;
                font-size: 0.30rem;
                font-weight: 700;
            }
        }

        .title3 {
            margin-top: 0.3rem;
            font-size: 0.18rem;
        }

        .timer {
            margin-top: 1.60rem;
            font-size: 0.16rem;
        }
    }

    .button {
        margin: 0.1rem auto;
        width: 0.32rem;
        height: 0.32rem;
        background: url("../../../../../assets/imgs/common/sprites.png") no-repeat -1.92rem 0;
        background-size: 3.2rem 6.4rem;
    }
}

</style>
