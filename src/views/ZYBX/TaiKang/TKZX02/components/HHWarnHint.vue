<template>
    <van-popup v-model="visible" class="container_2211031700" position="bottom" round @close="hintClose">
        <div class="header">消费者权益保障提示</div>
        <div class="content">您即将进入投保流程，请仔细阅读并确认保险条款、投保须知等重要内容，同时为保障您的权益，您在销售页面的操作将会被记录并同步承保保险公司。</div>
        <div class="footer">
            <div class="button" @click="closeAction">
                知道了
            </div>
        </div>
    </van-popup>
</template>

<script>

export default {
    name: "HHWarnHint", // 重要提醒
    props: {obj: Object},
    data() {
        return {
            timerId: null,
            visible: false,
        }
    },
    mounted() {
        setTimeout(() => {
            this.visible = this.obj.reminderVisible;
        }, 500);

        this.timerId = setTimeout(() => {
            this.visible = false;
        }, 3500);
    },
    beforeDestroy() {
        this.timerId && clearTimeout(this.timerId);
    },
    methods: {
        closeAction() {
            this.visible = false;
            this.obj.reminderVisible = false;
        },
        hintClose() {
            this.obj.reminderVisible = false;
        },
    },
}

</script>

<style lang="less" scoped type="text/less">

.container_2211031700 {
    width: 3.75rem;
    left: calc((100% - 3.75rem) * 0.5);

    display: flex;
    flex-direction: column;

    .header {
        padding: 0.15rem 0;
        color: #333333;
        font-size: 0.18rem;
        font-weight: 700;
        text-align: center;
    }

    .content {
        overflow: auto;
        flex: 1;

        padding: 0 0.10rem 0.25rem;
        font-size: 0.14rem;
        color: #333333;
        line-height: 1.60;
        text-align: justify;
    }

    .footer {
        background-color: #FFFFFF;
        border-top: #F2F2F2 1px solid;

        .button {
            margin: 0.08rem 0.2rem;
            height: 0.44rem;
            line-height: 0.44rem;
            border-radius: 0.22rem;
            font-size: 0.16rem;
            font-weight: 500;
            text-align: center;
            color: #FFFFFF;
            background: linear-gradient(to right, rgba(255, 20, 28, 0.65), rgba(255, 0, 0, 0.8));
        }
    }
}

</style>

