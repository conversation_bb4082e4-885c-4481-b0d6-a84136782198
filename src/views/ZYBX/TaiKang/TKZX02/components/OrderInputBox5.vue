<template>
    <div class="container_2302131855">
        <div class="header">
            <slot name="mark"></slot>
            <div class="header-top">
                {{ title.text1 }}
                <span class="header-top-color">&nbsp;&nbsp;{{ title.text2 }}</span>
            </div>
        </div>
        <div v-if="isStep2" class="box">
            <div class="box-item">
                <span class="box-item-label">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</span>
                <van-icon color="#F7333A" name="star" size="0.06rem"/>
                <input v-model="obj.name1" class="box-item-input" maxlength="25"
                       placeholder="信息保密，仅用于生成保单" type="text" @input="onInput('certName',$event.target.value)">
            </div>
            <div class="box-item">
                <span class="box-item-label">身份证号</span>
                <van-icon color="#F7333A" name="star" size="0.06rem"/>
                <input v-model="obj.idCard1" class="box-item-input" maxlength="18"
                       placeholder="信息保密，仅用于生成保单" type="text" @input="onInput('certNo',$event.target.value)">
            </div>
            <div class="box-item">
                <span class="box-item-label">手机号码</span>
                <van-icon color="#F7333A" name="star" size="0.06rem"/>
                <div class="box-item-input box-item-placeholder">
                    {{ phoneFormat }}
                </div>
            </div>
            <div class="box-item" @click="onSelect">
                <span class="box-item-label">地&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;区&nbsp;</span>
                <div :class="{'box-item-placeholder':!obj.province}" class="box-item-input">
                    {{ (obj.province + obj.city + obj.county) || '点击选择收货地区' }}
                </div>
                <van-icon name="location" size="0.15rem"/>
            </div>
            <div class="box-item">
                <span class="box-item-label">详细地址&nbsp;</span>
                <input v-model="obj.address" class="box-item-input"
                       placeholder="具体到街道、门牌号、小区等" type="text">
            </div>
        </div>
        <div v-else class="box">
            <div class="box-item">
                <span class="box-item-label">手机号码</span>
                <input v-model="obj.phoneNo" class="box-item-input" maxlength="11"
                       onkeyup="value=value.replace(/\D/g,'')"
                       placeholder="您的信息将被严格保密" type="tel" @input="onInput('phoneNo',$event.target.value)">
            </div>
        </div>
        <div class="submitButton" @click="submit" id="id_action_button">免费领取
            <img alt="hand" class="hand" src="@/assets/imgs/common/icon_hand2.png">
        </div>
        <slot name="policy"></slot>
    </div>
</template>

<script>

import {isPhoneNum, star_marked_phone} from "@/assets/js/common";

export default {
    name: "UUInputBox",
    props: {
        obj: Object
    },
    computed: {
        isStep2() {
            return this.obj.step == 'step2';
        },
        title() {
            if (this.isStep2) {
                return {text1: '仅剩1步', text2: '免费领取'};
            }
            return {text1: '仅剩2步', text2: '免费领取'};
        },
        phoneFormat() {
            const {phoneNo, starPhone} = this.obj;
            return star_marked_phone(isPhoneNum(phoneNo) ? phoneNo : starPhone);
        }
    },
    methods: {
        onInput(key, value) {
            this.$emit('input', {key, value});
        },
        submit() {
            this.$emit('submit');
        },
        onSelect() {
            this.$emit('select');
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2302131855 {
    position: relative;
    color: #333333;
    font-size: 0.16rem;
    border-radius: 0.15rem;
    background-color: #FFFFFF;;
    box-shadow: 0 1px 10px 0 rgba(200, 133, 85, 0.24);
    border: transparent solid;
    border-width: 1px 0;

    //&::before {
    //    position: absolute;
    //    content: ' ';
    //    width: 0.50rem;
    //    height: 1.15rem;
    //    left: -0.15rem;
    //    top: 50%;
    //    transform: translateY(-50%);
    //    background: url("../../../../../assets/imgs/common/hand_left.png") no-repeat center;
    //    background-size: 100%;
    //}
    //
    //&::after {
    //    position: absolute;
    //    content: ' ';
    //    width: 0.50rem;
    //    height: 1.15rem;
    //    right: -0.15rem;
    //    top: 50%;
    //    transform: translateY(-50%);
    //    background: url("../../../../../assets/imgs/common/hand_right.png") no-repeat center;
    //    background-size: 100%;
    //}

    .header {
        margin-top: 0.18rem;

        .header-top {
            position: relative;
            font-size: 0.18rem;
            font-weight: 700;
            text-align: center;

            &::before {
                position: absolute;
                content: ' ';
                width: 0.20rem;
                height: 0.20rem;
                margin: -0.02rem 0 0 -0.25rem;
                background: url("../../../../../assets/imgs/common/icon_arrows_down.png") no-repeat center;
                background-size: 100%;
            }

            &::after {
                position: absolute;
                content: ' ';
                width: 0.20rem;
                height: 0.20rem;
                margin: -0.02rem 0 0 0.05rem;
                background: url("../../../../../assets/imgs/common/icon_arrows_down.png") no-repeat center;
                background-size: 100%;
            }

            .header-top-color {
                color: #ff3300;
            }
        }

        .header-bottom {
            margin-top: 0.05rem;
            text-align: center;
            color: #737680;
            font-size: 0.12rem;
        }
    }

    .box {
        margin: 0.10rem 0.20rem 0.20rem;

        .box-item {
            display: flex;
            align-items: center;
            border: solid #F0F0F0;
            border-width: 0 0 1px 0;

            .box-item-label, .box-item-input {
                font-size: 0.15rem;
            }

            .box-item-label {
                font-weight: 700;
                text-align: center;
            }

            .box-item-input {
                margin-left: 0.10rem;
                line-height: 0.50rem;
                height: 0.50rem;
                flex: 1;
                border: 0;
                outline: none;
                background-color: unset;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }

            .box-item-placeholder {
                color: #999999;
            }
        }
    }

    .submitButton {
        position: relative;
        margin: 0.20rem 0.15rem 0.15rem;
        padding: 0.15rem 0;
        line-height: 1.0;
        color: #FFFFFF;
        font-size: 0.20rem;
        font-weight: 700;
        text-align: center;
        border-radius: 0.25rem;
        background-color: #F7333A;
        box-shadow: 0 1px 5px 0 #FF4B27;
        animation: button_animation 1.35s linear infinite;

        .hand {
            position: absolute;
            top: 0.30rem;
            left: 75%;
            width: 18%;
            animation: hand_animation 1.35s linear infinite;
        }
    }

    @keyframes button_animation {
        0% {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
        40% {
            -webkit-transform: scale(.95);
            transform: scale(.95);
        }
        60% {
            -webkit-transform: scale(.97);
            transform: scale(.97);
        }
        100% {
            -webkit-transform: scale(1);
            transform: scale(1);
        }
    }
    @keyframes hand_animation {
        0% {
            top: 0.10rem;
            left: 70%;
        }
        45% {
            top: 0.25rem;
            left: 75%;
        }
        70% {
            top: 0.25rem;
            left: 75%;
        }
        100% {
            top: 0.10rem;
            left: 70%;
        }
    }
}

</style>

