<template>
	<div class="container_2302131855">
		<div class="header">
			<slot name="mark"></slot>
			<div class="header-top">
				{{ title.text1 }}
				<span class="header-top-color">&nbsp;&nbsp;{{ title.text2 }}</span>
			</div>
		</div>
		<div v-if="isStep2">
			<div class="box-item">
				<span class="label">真实姓名</span>
				<input v-model="orderInfo.name1" class="input" maxlength="25" placeholder="信息保密，仅用于生成保单" type="text" @focus="onFocus('name',$event.target.value)" @input="onInput('name',$event.target.value)">
			</div>
			<div class="box-item">
				<span class="label" @click="onIdCardTypeChange(1)">{{ icLabelHolder }}<van-icon name="arrow-down" class="arrow" /></span>
				<input class="input" type="text" placeholder="信息保密，仅用于生成保单" v-model="orderInfo.idCard1" maxlength="18" @input="onInput('idCard',$event.target.value)" @focus="onFocus('idCard',$event.target.value)">
			</div>
			<div v-if="icLabelHolder == '永居证号'" class="box-item">
				<span class="label">出生日期</span>
				<input class="input" v-model="orderInfo.birthday1" readonly placeholder="请选择出生日期" @click="onBirthdaySelect(1)">
			</div>
			<div v-if="icLabelHolder == '永居证号'" class="box-item">
				<span class="label">性别</span>
				<span class="flex-rows input">
					<HHOption v-for="v in genderList" :key="v.key" :obj="{text:v.key,active:v.value == orderInfo.gender1,width:'0.75rem'}" @click="OnGenderChange(1,v.value)"></HHOption>
				</span>
			</div>
			<div class="box-item">
				<span class="label">手机号码</span>
				<input :value="phoneFormat" class="input" placeholder="您的信息将被严格保密" readonly type="tel">
			</div>
		</div>
		<div v-else>
			<div class="box-item">
				<span class="label">手机号码</span>
				<input v-model="orderInfo.phoneNo" class="input" maxlength="11" onkeyup="value=value.replace(/\D/g,'')" placeholder="您的信息将被严格保密" type="tel" @focus="onFocus('phone',$event.target.value)" @input="onInput('phone',$event.target.value)">
			</div>
		</div>
		<div class="submitButton" @click="submit">免费领取
			<img alt="hand" class="hand" src="@/assets/imgs/common/icon_hand2.png">
		</div>
		<slot name="policy"></slot>
		<HHICTypeViewer :obj="icTypeObj" @click="onIdCardTypeFinish"></HHICTypeViewer>
		<van-calendar :min-date="minDate" :max-date="maxDate" :default-date="defaultDate" v-model="icTypeObj.v1" @confirm="onBirthdayFinish" />
	</div>
</template>

<script>
import moment from "moment";
import HHOption from "./HHOption";
import HHICTypeViewer from "./HHICTypeViewer";
import { isPhoneNum, star_marked_phone } from "@/assets/js/common";

export default {
	name: "UUInputBox",
	components: { HHOption, HHICTypeViewer },
	props: {
		orderInfo: Object
	},
	data() {
		return {
			icTypeObj: { v: false, relation: 1, v1: false, type: '01' },
			minDate: new Date(1944, 0, 1),
			maxDate: new Date(),
			defaultDate: null,
			genderList: [
				{ key: '男', value: '1' },
				{ key: '女', value: '0' },
			]
		}
	},
	computed: {
		icLabelHolder() {
			return this.orderInfo.idCardType1 == '01' ? '身份证号' : '永居证号';
		},
		isStep2() {
			return this.orderInfo.step == 'step2';
		},
		title() {
			if (this.isStep2) {
				return { text1: '仅剩1步', text2: '免费领取' };
			}
			return { text1: '仅剩2步', text2: '免费领取' };
		},
		phoneFormat() {
			const { phoneNo, starPhone } = this.orderInfo;
			return star_marked_phone(isPhoneNum(phoneNo) ? phoneNo : starPhone);
		}
	},
	methods: {
		onBirthdaySelect(relation) {
			const birthday = this.orderInfo[`birthday${relation}`];
			this.defaultDate = moment(birthday || '1990-01-01').toDate();

			this.icTypeObj.relation = relation;
			this.icTypeObj.v1 = true;
		},
		onBirthdayFinish(date) {
			this.orderInfo[`birthday${this.icTypeObj.relation}`] = moment(date).format('YYYY-MM-DD');
			this.icTypeObj.v1 = false;
		},
		onIdCardTypeChange(relation) {
			const icType = this.orderInfo[`idCardType${relation}`];
			this.icTypeObj.type = icType;
			this.icTypeObj.relation = relation;
			this.icTypeObj.v = true;
		},
		onIdCardTypeFinish({ relation, type }) {
			this.orderInfo[`idCardType${relation}`] = type;
		},
		OnGenderChange(relation, value) {
			this.orderInfo[`gender${relation}`] = value;
		},
		submit() {
			this.$emit('submit');
		},
		onInput(key, value) {
			this.$emit('input', { key, value });
		},
		onFocus(key, value) {
			this.$emit('focus', { key, value });
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302131855 {
		position: relative;
		margin: 0 0.15rem;
		padding-top: 1px;
		color: #333333;
		font-size: 0.16rem;
		border-radius: 0.15rem;
		background-color: #ffffff;
		box-shadow: 0 1px 10px 0 rgba(200, 133, 85, 0.24);

		.flex-rows {
			display: flex;
			align-items: center;
			justify-content: space-around;
		}

		// &::before {
		//     position: absolute;
		//     content: ' ';
		//     width: 0.50rem;
		//     height: 1.15rem;
		//     left: -0.15rem;
		//     top: 50%;
		//     transform: translateY(-50%);
		//     background: url("~@/assets/imgs/common/hand_left.png") no-repeat center;
		//     background-size: 100%;
		// }

		// &::after {
		//     position: absolute;
		//     content: ' ';
		//     width: 0.50rem;
		//     height: 1.15rem;
		//     right: -0.15rem;
		//     top: 50%;
		//     transform: translateY(-50%);
		//     background: url("~@/assets/imgs/common/hand_right.png") no-repeat center;
		//     background-size: 100%;
		// }

		.header {
			margin: 0.18rem 0 0.2rem;

			.header-top {
				position: relative;
				font-size: 0.18rem;
				font-weight: 700;
				text-align: center;

				&::before {
					position: absolute;
					content: " ";
					width: 0.2rem;
					height: 0.2rem;
					margin: -0.02rem 0 0 -0.25rem;
					background: url("~@/assets/imgs/common/icon_arrows_down.png")
						no-repeat center;
					background-size: 100%;
				}

				&::after {
					position: absolute;
					content: " ";
					width: 0.2rem;
					height: 0.2rem;
					margin: -0.02rem 0 0 0.05rem;
					background: url("~@/assets/imgs/common/icon_arrows_down.png")
						no-repeat center;
					background-size: 100%;
				}

				.header-top-color {
					color: #ff3300;
				}
			}

			.header-bottom {
				margin-top: 0.05rem;
				text-align: center;
				color: #737680;
				font-size: 0.12rem;
			}
		}

		.box-item {
			padding: 0 0.15rem;
			display: flex;
			align-items: center;
			line-height: 0.5rem;
			color: #333333;
			border-bottom: #f2f2f2 1px solid;

			.label {
				width: 1rem;
				font-weight: 500;
			}

			.input {
				flex: 1;
				border: none;
				outline: none;
				width: 2rem;
				font-size: 0.17rem;
				background-color: unset;
			}

			.arrow {
				font-size: 0.12rem;
				color: #999999;
			}
		}

		// .box-item:not(:last-child) {
		// 	border-bottom: #f2f2f2 1px solid;
		// }

		.submitButton {
			position: relative;
			margin: 0.2rem 0.2rem 0;
			padding: 0.16rem 0;
			line-height: 1;
			color: #ffffff;
			font-size: 0.2rem;
			font-weight: 700;
			text-align: center;
			border-radius: 0.26rem;
			background: linear-gradient(180deg, #fdbe37, #ec6d15);
			animation: button_animation 1.35s linear infinite;

			.hand {
				position: absolute;
				top: 0.3rem;
				left: 75%;
				width: 18%;
				animation: hand_animation 1.35s linear infinite;
			}
		}

		@keyframes button_animation {
			0% {
				-webkit-transform: scale(1);
				transform: scale(1);
			}
			40% {
				-webkit-transform: scale(0.95);
				transform: scale(0.95);
			}
			60% {
				-webkit-transform: scale(0.97);
				transform: scale(0.97);
			}
			100% {
				-webkit-transform: scale(1);
				transform: scale(1);
			}
		}
		@keyframes hand_animation {
			0% {
				top: 0.1rem;
				left: 70%;
			}
			45% {
				top: 0.25rem;
				left: 75%;
			}
			70% {
				top: 0.25rem;
				left: 75%;
			}
			100% {
				top: 0.1rem;
				left: 70%;
			}
		}
	}
</style>

