import {Toast} from "vant";
import {isPhoneNum, TraceLogInfoKeys} from "@/assets/js/common";
import {actionTracking} from "@/assets/js/api";
import {bxStorage,} from "@/utils/store_util";

export const pdfFileObj = {
    '保通隐私政策': '/IYB/Common/bt_yinsizhengce.pdf',
    '保通太平隐私政策': '/IYB/Common/bt_tp_yinsizhengce.pdf',
    '惠诺康达隐私政策': '/IYB/Common/hnkd_yinsizhengce.pdf',
    '惠诺康达太平隐私政策': '/IYB/Common/hnkd_tp_yinsizhengce.pdf',
    '太平产品说明': '/IYB/IYBTPZX02/cpsm.pdf',
    '太平投保提示和须知': '/IYB/IYBTPZX02/tbxz.pdf',
    '太平责任免除': '/IYB/IYBTPZX02/zrcw.pdf',
    '太平健康告知': '/IYB/IYBTPZX02/jkgz.pdf',
    '太平理赔流程': '/IYB/IYBTPZX01/dir4.pdf',
    '产品说明及投保须知': '/IYB/IYBTKZX01/tbxz.pdf?v=1',
    '特别约定': '/IYB/IYBTKZX01/tbyd.pdf',
    
    '太平任逍遥意外伤害保险（互联网专属）条款': '/IYB/IYBTPZX02/dir1.pdf',
    '国任个人重大疾病保险（互联网专属）条款': '/IYB/IYBGRZX01/dir1.pdf',
    '国任责任免除': '/IYB/IYBGRZX01/zrcw.pdf',
    '国任健康告知': '/IYB/IYBGRZX01/jkgz.pdf',
    '国任投保须知': '/IYB/IYBGRZX01/tbxz.pdf',
    '太平隐私政策': '/IYB/Common/bt_tp_yinsizhengce.pdf',

    '泰康投保须知': '/IYB/IYBTKZX01/tbxz.pdf?v=1',
    '产品说明': '/IYB/IYBTPZX02/cpsm.pdf', // 太平产品说明
    '隐私政策': '/IYB/Common/bt_tp_yinsizhengce.pdf', // 太平隐私政策
    '理赔流程': '/IYB/IYBTPZX01/dir4.pdf', // 太平理赔流程
}

export const documentList = [
    {
        page: '投保须知', list: [
            { page: '太平投保提示和须知', },
            { page: '国任投保须知', },
        ]
    },
    {
        page: '保险条款', list: [
            { page: '太平任逍遥意外伤害保险（互联网专属）条款', },
            { page: '国任个人重大疾病保险（互联网专属）条款' },
        ]
    },
    { page: '特别约定' },
    { page: '产品说明' },
    {
        page: '责任免除', list: [
            { page: '太平责任免除', },
            { page: '国任责任免除', },
        ]
    },
    { page: '隐私政策' },
    { page: '理赔流程' },
    {
        page: '健康告知', list: [
            { page: '太平健康告知', },
            { page: '国任健康告知', },
        ]
    },
];

export const createOrderInfo = () => {
    const orderInfo = {
        received: 0,     // 是否已领取过
        reminderVisible: true,
        checked: true,
        verCode: '',
        hasCode: false, // 是否包含手机验证码
        forceCode: '0', // 链接里携带是否强制验证码
        step: 'step1',
        relation: 1,
        infoNo: '',
        m: '',
        tel: '',
        mTel: '',
        mrid: '',
        robotID: '',
        channel: '1',
        channelCode: '',
        age: 0,
        phoneNo: '',
        starPhone: '',
        name1: '',
        idCard1: '',
        idCardType1: '01',
        birthday1: '',
        gender1: '1',
        province: '',
        city: '',
        county: '',
        address: '',
        countyCode: '',
        addrExpand: false,
        productKey: TraceLogInfoKeys.tc_tk_free_send,
        page: '',
        sourcePage: '',
        tempSaveId: '',
        traceBackUuid: '',
        sfdm: '',
        csdm: '',
        expireTime: '',
        qz_gdt: '', // 信息流广告clickId
        qz_platform: '', // 信息流platform 腾讯广告传：TENG_XUN 头条广告传：TOU_TIAO

        relationCorpId:'', // 暖哇企微
        relationExtUserId:'', // 暖哇企微

        externalUserId:'',// 保通企微
        userId:'',// 保通企微

        partner: '123676',

        channelEnMCode: '',
        channelEnMContent: '',

        fromId: '77255',
        productCode: 'S20200683',
        productName: '泰康心心传递赠险',
    };

    return orderInfo;
}
export const isStarPhone = (obj) => {
    const {starPhone, mTel} = obj || {};
    return starPhone && mTel;
}

export const eventTracking = (orderInfo, name, time = 0,) => {
    const {productKey, mTel, tel, channel, phoneNo, page} = orderInfo;
    const phone = mTel || tel || (isPhoneNum(phoneNo) ? phoneNo : '');

    actionTracking({
        page: `泰康赠险${page}(${productKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: productKey,
        time: time,
    }).then(res => {
        const {code, data} = res;
        if (code == 2000) {
            const {mobileId} = data || {};
            if (mobileId) {
                orderInfo.mTel = mobileId;
                saveOrderInfo(orderInfo);
            }
        }
    });
}

export const msgToast = (message, duration) => {
    Toast({
        forbidClick: true,
        message: message || '',
        duration: duration || 2000, // 弹窗时间毫秒
    });
}

export const loadOrderInfo = () => {
    return bxStorage.getObjItem('TKZX02Info') || {};
}

export const saveOrderInfo = (orderInfo) => {
    bxStorage.setObjItem('TKZX02Info', orderInfo);
}

export const createNoticeList = () => {
    const noticeList = [];
    for (let idx = 0; idx < 100; idx++) {
        const tailNumber = (Math.random() * 8999 + 1000).toFixed(0);
        noticeList.push(tailNumber);
    }
    return noticeList;
}

