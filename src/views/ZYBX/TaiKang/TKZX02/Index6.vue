<template>
	<div class="container_2210191500">

	</div>
</template>

<script>

import { bxStorage, } from "@/utils/store_util";

export default {
	name: "TKZX02Index",
	created() {
		this.init();
	},
	methods: {
		init() {
			const PATH_DEFAULT = 'TKZX02Index7'; // 默认产品路径

			let path = bxStorage.getRawItem('TKZX02Random1Path') || '';

			const script = document.createElement('script');
			script.type = 'text/javascript';
			script.src = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/config/random_params.js';
			document.getElementsByTagName('head')[0].appendChild(script);
			script.onload = () => {
				try {
					const list = TKZX02_random1 || [[PATH_DEFAULT, 100]];
					const productList = list.map(item => item[0]);
					if (productList.includes(path)) {
						return this.pushToTarget(path);
					}

					const sum = list.reduce((a, b) => {
						return a + b[1];
					}, 0);

					list.forEach((item, index) => {
						item[1] = item[1] / sum * 100;
						if (index != 0) {
							item[1] += list[index - 1][1];
						}
					});

					const randomVal = Math.random() * 100;
					// console.log(randomVal, list);

					for (let k = 0; k < list.length; k++) {
						if (randomVal < list[k][1]) {
							path = list[k][0];
							return this.pushToTarget(path);
						}
					}
					this.pushToTarget(PATH_DEFAULT);
				} catch (err) {
					this.pushToTarget(PATH_DEFAULT);
				}
			}
			script.onerror = () => {
				this.pushToTarget(PATH_DEFAULT);
			}
		},
		pushToTarget(path) {
			const query = this.$route.query || {};
			bxStorage.setRawItem('TKZX02Random1Path', path);
			this.$router.replace({
				name: path,
				query: query,
			});
		},
	}
}
</script>

<style lang="less" scoped type="text/less">
	.container_2210191500 {
		width: 100%;
		height: 100%;
		background-color: #ffffff;
	}
</style>


