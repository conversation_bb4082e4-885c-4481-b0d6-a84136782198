<template>
	<div class="container_2303061710">
		<div class="header">
            <img src="@/assets/imgs/TaiKang/TK23/img03.png" />
			<p class="count-down">
				<span>{{ minutes }}</span>:
				<span>{{ seconds }}</span>
				<i class="count-down-i">后订单失效</i>

			</p>
		</div>
		<div class="section-box">
			<div class="section">
				<van-collapse v-model="activeNames" :border="false">
					<van-collapse-item name="1" :is-link="false" :border="false" class="collapse-box">
						<template #title>
							<div class="section-header">投保人</div>
						</template>
						<template #value>
							<div class="section-header-edit">{{ showText }}</div>
						</template>
						<div v-for="(item,index) in holderInfo" :key="index" class="section-item">
							<span>{{ item.key }}</span>
							<span class="section-item-value">{{ item.value }}</span>
						</div>
					</van-collapse-item>
                    <div class="collapse-line"></div>
					<van-collapse-item name="2" :is-link="false" disabled :border="false" class="collapse-box2">
						<template #title>
							<div class="section-header">被保人</div>
						</template>
						<template #value>
							<div class="section-header-edit"></div>
						</template>
						<div v-for="(item,index) in insuredInfo" :key="index" class="section-item">
							<span>{{ item.key }}</span>
							<span class="section-item-value">{{ item.value }}</span>
						</div>
					</van-collapse-item>
				</van-collapse>
			</div>
            <div class="footer-box">
				<img src="@/assets/imgs/TaiKang/TK23/img04.png" alt="">
			</div>
			<div class="premium-x">月缴更轻松，每月<span class="number">{{ premium }}</span>元</div>
			<div class="policy-x">
				<div>
					我已阅读并同意
					<span class="view-txt" @click.stop="viewAction('健康告知',true)">《健康告知》</span>
					<span class="view-txt" @click.stop="viewAction('投保须知',true)">《投保须知》</span>
					<span class="view-txt" @click.stop="viewAction('责任免除',true)">《责任免除》</span>
					<span class="view-txt" @click.stop="viewAction('特别约定',true)">《特别约定》</span>
					<span class="view-txt" @click.stop="viewAction('产品说明',true)">《产品说明》</span>
					<span class="view-txt" @click.stop="viewAction('保险条款',true)">《保险条款》</span>
					<span class="view-txt" @click.stop="viewAction('百万医疗高危职业表',true)">《百万医疗高危职业表》</span>
					<span class="view-txt" @click.stop="viewAction('费率表',false)">《费率表》</span>
				</div>
			</div>
		</div>
		<div class="bottom-button" @click="onSubmit">立即升级</div>
		<HHPolicyViewer :obj="policyObj"></HHPolicyViewer>
		<HHTabViewer :obj="policyObj" @ok="onAcceptPolicy"></HHTabViewer>
	</div>
</template>
<script>
import { bxStorage, } from "@/utils/store_util";
import HHTabViewer from "./components/HHTabViewer";
import HHPolicyViewer from "./components/HHPolicyViewer";
import { domainPathMap, } from "@/views/ZYBX/src";
import { fetchPromoteOrderInfo } from "@/api/insurance-api";
import { calculatePremium, showToast, eventTracking, createOrderInfo, getPolicyVersion } from "./function";
export default {
	name: "Promotion2",
	components: { HHPolicyViewer, HHTabViewer },
	data() {
		const orderInfo1 = {
			holderName: '',
			holderIdCard: '',
			holderPhone: '',
			insuredName: '',
			insuredIdCard: '',
			expireTime: 0,
            activeNames: ['2']
		};

		const orderInfo = createOrderInfo();
		Object.assign(orderInfo, orderInfo1);

		return {
			minutes: 0,
			seconds: 0,
			orderInfo,
			policyObj: { v: false, page: '', v1: false, page1: '', belongs: 'v2' },
            activeNames: ['2']
		}
	},
	computed: {
		premium() {
			if (!this.orderInfo.upgradePremium) {
				return '42.07';
			}
			return (+this.orderInfo.upgradePremium).toFixed(2);
		},

		holderInfo() {
			const { holderName, holderIdCard, holderPhone } = this.orderInfo;
			return [
				{ key: '姓名', value: holderName },
				{ key: '身份证', value: holderIdCard },
				{ key: '手机号', value: holderPhone },
			]
		},

		insuredInfo() {
			const { insuredName, insuredIdCard, insurance } = this.orderInfo;
			return [
				{ key: '姓名', value: insuredName },
				{ key: '身份证', value: insuredIdCard },
				{ key: '有无社保', value: insurance == 1 ? '有' : '无' },
			]
		},
        showText() {
			return this.activeNames.includes('1') ? '收起' : '展开';
		}
       
	},

	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.init();
	},

	methods: {
		//初始化
		init() {
			const inQry = this.$route.query || {}; // 必须：加密的infoNo
			const { channel, infoNo } = inQry;
			this.orderInfo.channel = channel || '1';
			this.orderInfo.identifier = 'TK16Promotion2';
			this.orderInfo.action = 'promote';
			this.orderInfo.source = 'promote';
			this.orderInfo.expireTime = bxStorage.getRawItem('TK16ExpireTime') || 0;

			this.fetchOrderInfo(infoNo);
		},

		fetchOrderInfo(infoNo) {
			if (!infoNo) {
				this._entryBehaviorReport();
				return this.editOrderInfo(true);
			}

			this.$toast.loading({
				message: '正在请求数据\n请稍候',
				forbidClick: true,
				duration: 0,
			});

			fetchPromoteOrderInfo(infoNo).then(res => {
				this.$toast.clear(true);
				const data = res.data || {};
				this.orderInfoHandle(data);
			}).catch(err => {
				this.$toast.clear(true);
				this.editOrderInfo(true);
			}).finally(() => {
				this._entryBehaviorReport();
			});
		},

		orderInfoHandle(data) {
			Object.assign(this.orderInfo, data);

			const { operatorPhone, insuredIdCard, insurance, repay, infoNo, infoKey } = this.orderInfo;
			if (!infoNo) {
				return this.editOrderInfo(true);
			}

			this.orderInfo.mTel = operatorPhone;
			this.orderInfo.upgradePremium = calculatePremium(insuredIdCard, insurance, repay).high;
			this.policyObj.belongs = getPolicyVersion(insuredIdCard);

			if (infoKey.indexOf('_upgrade') >= 0) {
				return this.pushToResult();
			}

			this.startCountdown();
		},

		startCountdown() {
			if (Date.now() - this.orderInfo.expireTime > 0) {
				this.orderInfo.expireTime = Date.now() + 60 * 60 * 1000;
				bxStorage.setRawItem('TK16ExpireTime', this.orderInfo.expireTime);
			}

			requestAnimationFrame(() => {
				const diff = Math.floor((this.orderInfo.expireTime - Date.now()) / 1000);
				this.minutes = ('00' + Math.floor(diff / 60)).slice(-2);
				this.seconds = ('00' + (diff - this.minutes * 60)).slice(-2);

				this.startCountdown();
			})
		},

		onEdit() {
			this._actionTracking('点击编辑按钮');
			this.editOrderInfo();
		},

		editOrderInfo(delay, msg) {
			const { channel, mTel } = this.orderInfo;

			const href = domainPathMap['TK16Index1'] + `?action=promotion&source=promotion&channel=${channel}&mTel=${mTel}`;

			if (!delay) {
				return setTimeout(() => {
					return window.location.href = href;
				}, 250);
			}

			showToast(msg || '该订单无效，正在跳转到首页');

			setTimeout(() => {
				return window.location.href = href;
			}, 2000);
		},

		onSubmit() {
			this._actionTracking('点击促活立即升级按钮');

			this.pushToResult();
		},

		pushToResult() {
			setTimeout(() => {
				const { channel, mTel, infoNo, traceBackUuid} = this.orderInfo;
				let href = window.location.href.split('?')[0];
				href = href.replace(/\/Promotion\d*/, '/Result');
				window.location.href = href + `?channel=${channel}&mTel=${mTel}&infoNo=${infoNo}&tb_uuid=${traceBackUuid}&fromPromote=2`;
			}, 250);
		},

		onAcceptPolicy() {
			this.onSubmit();
		},

		viewAction(name, isPolicy) {
			this.policyObj[isPolicy ? 'v1' : 'v'] = true;
			this.policyObj[isPolicy ? 'page1' : 'page'] = name;
		},

		_entryBehaviorReport() {
			const { timing } = window.performance || {};
			// console.log('页面加载性能 => ' + JSON.stringify(timing));
			const { domContentLoadedEventEnd, fetchStart } = timing || {};
			this._actionTracking('升级促活页', domContentLoadedEventEnd - fetchStart);
		},

		_actionTracking(name, time = 0) {
			eventTracking(this.orderInfo, name, time);
		},
	},
}
</script>


<style lang="less" scoped type="text/less">
	.container_2303061710 {
		height: 100%;
		box-sizing: border-box;
		background-color: #f2f2f2;

		display: flex;
		flex-direction: column;

        img {
			display: block;
			max-width: 100%;
		}

		.header {

			.count-down {
				position: absolute;
				top: 0.63rem;
				left: 50%;
				transform: translateX(-50%);
				vertical-align: bottom;

				display: flex;
				align-items: center;

				font-size: 0.18rem;
				font-weight: 400;
				color: rgba(255, 255, 255, 0.85);

				span {
					font-size: 0.22rem;
					text-align: center;
					font-weight: 600;
					padding: 0 0.04rem;
					color: #fff;
				}
				&-i {
					margin-top: 2px;
					font-style: normal;
				}
			}
		}

		.section-box {
			flex: 1;
			margin: 0.12rem 0;
            padding: 0 0.12rem;
		}

		.section {
            // 覆盖组件原有的边距
            /deep/ .van-cell{
				padding: 0.08rem 0.12rem;
			}
            /deep/ .van-collapse-item__content {
				padding: 0.08rem 0.12rem 0;
			}

            // 折叠面板设置圆角
            .collapse-box /deep/ .van-cell {
                border-radius: 0.08rem 0.08rem 0 0;
            }
            .collapse-box2 /deep/ .van-collapse-item__content {
                border-radius: 0 0 0.08rem 0.08rem;
            }

			.section-header {
				font-size: 0.16rem;
				font-weight: 500;
				color: #262626;
			}
			.section-header-edit {
				color: #0b78ff;
				font-weight: 400;
				font-size: 0.14rem;
			}

			.section-item {
				padding-bottom:0.08rem;
				font-size: 0.14rem;
				font-weight: 400;
				color: #666666;

				display: flex;
				align-items: center;
				justify-content: space-between;

				.section-item-value {
					color: #262626;
					font-size: 0.14rem;
					font-weight: 400;
				}
			}

            .collapse-line {
				position: relative;
				background-color: #fff;
				height: 1px;
				&::after {
					content: "";
					display: block;
					width: 3.27rem;
					position: absolute;
					left: 50%;
					transform: translateX(-50%);
					top: 0%;
					height: 1px;
					background-color: #e6e6e6;
				}
			}
		}
        .footer-box{
            margin-top: 0.12rem;
        }
		.premium-x {
			margin: 0.15rem 0;
			color: #333333;
			font-size: 0.14rem;
			text-align: center;

			.number {
				font-size: 0.16rem;
				font-weight: 700;
				color: #CE1D15;
			}
		}

		.policy-x {
			padding: 0.12rem;
            padding-top: 0.16rem;
			font-size: 0.13rem;
			color: #333333;
			line-height: 1.6;
			text-align: justify;
			background-color: #ffffff;
            margin: 0 -0.12rem;
            margin-bottom: 0.5rem;

			.view-txt {
				color: #DC2711;
				font-weight: 500;
			}
		}

		.bottom-button {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100vw;
			padding: 0.16rem 0;
			color: #ffffff;
			font-size: 0.17rem;
			font-weight: 700;
			text-align: center;
			background-color: #DC2711;
		}
	}
</style>
