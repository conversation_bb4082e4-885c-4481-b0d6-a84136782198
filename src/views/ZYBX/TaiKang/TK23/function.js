import moment from "moment";
import { Toast } from "vant";
import { PremiumRate, PremiumRate1 } from "./src";
import { actionTracking } from "@/assets/js/api";
import { bxStorage, } from "@/utils/store_util";
import { createPromotionOrder, fetchCreateTKZengXianOrder, fetchTKCubeUpgrade, } from "@/api/insurance-api";
import { isCardNo, isInWx, isMaskedAndT1Phone, isPersonName, isPhoneNum, TraceLogInfoKeys, url_safe_b64_encode, GetAge} from "@/assets/js/common";

export const createOrderInfo = (action = '') => {
    return {
        checked: false,

        source: action, // 页面来源
        action: action, //  back: '返回',promotion: '促活跳转',follow: '结果跳转',direct: '直发',forward: '转发',result: '结果页',promote: '促活'
        // 回溯相关
        fromId: '76497',
        productCode: 'N20240009',
        productName: '泰超能·百万医疗险',
        proposal_id: '',
        traceBackUuid: '',

        productKey: TraceLogInfoKeys.tc_tk_cn_medical_health_high_cube_base,
        identifier: '',
        autoRenewalType: 0,
        relation: 1,    // 为谁投保 1:本人；2:配偶；3:儿女；4:父母；
        insurance: 1,   // 有无社保
        repay: 1,       // 缴费方式
        channel: '1',
        channelCode: '',
        m: '',          // 链接自带的加密手机号
        tel: '',        // 操作人手机号
        mTel: '',       // 加密手机号
        infoNo: '',     // 订单号
        policyNo: '',    // 保单号
        totalPremium: 0,
        upgradePremium: 0,
        starPhone: '',

        phoneNo: '',    // 手机号
        name1: '',      // 本人
        name2: '',      // 配偶
        name3: '',      // 儿女
        name4: '',      // 父母
        idCard1: '',
        idCardType1: '01',
        birthday1: '',
        gender1: '1',
        idCard2: '',
        idCardType2: '01',
        birthday2: '',
        gender2: '1',
        idCard3: '',
        idCardType3: '01',
        birthday3: '',
        gender3: '1',
        idCard4: '',
        idCardType4: '01',
        birthday4: '',
        gender4: '1',
        sourcePage: '',
        callbackUrl: '',
        school: '0', // 0非默认升级；1默认升级
        paymentCode: -1, // NSF评分

        qz_gdt: '',
        qz_platform: '', // 信息流platform 腾讯广告传：TENG_XUN 头条广告传：TOU_TIAO
        advertiserName: '',
    }
}

export const checkOrderParams = (orderInfo, page) => {
    const { productKey, infoNo, mTel, channel, checked, autoRenewalType, channelCode, sourcePage, school, traceBackUuid, } = orderInfo;
    const { name1, idCard1, phoneNo, starPhone, relation, repay, insurance, totalPremium, upgradePremium, } = orderInfo;
    const params = {
        infoNo: infoNo,
        relation: relation,
        insurance: insurance,
        paymentPlan: repay,
        planKey: productKey,
        channelId: channel,
        page,
        channelCode,
        sourcePage,
        traceBackUuid,
        operatorPhone: '',
        holderName: '',
        holderIdCard: '',
        holderPhone: '',
        insuredName: '',
        insuredIdCard: '',
        insuredPhone: '',
    };

    const certName = `name${relation}`;
    const insuredName = orderInfo[certName];
    if (!isPersonName(insuredName)) {
        return { code: 1, msg: '请填写正确的被保人姓名', };
    }
    params.insuredName = insuredName;

    const certNo = `idCard${relation}`;
    const insuredIdCard = orderInfo[certNo];
    const icTypeInsured = orderInfo[`idCardType${relation}`];
    const birthInsured = orderInfo[`birthday${relation}`];
    const genderInsured = orderInfo[`gender${relation}`];
    if (icTypeInsured == '08' && insuredIdCard.length != 15 && insuredIdCard.length != 18 || icTypeInsured == '01' && insuredIdCard.length != 18) {
        return { code: 1, msg: '请填写正确的被保人证件号' };
    }
    if (insuredIdCard.length == 18 && !isCardNo(insuredIdCard)) {
        return { code: 1, msg: '请填写正确的被保人证件号' };
    }
    if (icTypeInsured == '08' && !birthInsured) {
        return { code: 1, msg: '请填写正确的被保人出生日期' };
    }
    params.insuredIdCard = insuredIdCard;

    if (!isPersonName(name1)) {
        return { code: 1, msg: '请填写正确的投保人姓名' };
    }
    params.holderName = name1;

    const { idCardType1, birthday1, gender1 } = orderInfo;
    if (idCardType1 == '08' && idCard1.length != 15 && idCard1.length != 18 || idCardType1 == '01' && idCard1.length != 18) {
        return { code: 1, msg: '请填写正确的投保人证件号' };
    }
    if (idCard1.length == 18 && !isCardNo(idCard1)) {
        return { code: 1, msg: '请填写正确的投保人证件号' };
    }
    if (idCardType1 == '08' && !birthday1) {
        return { code: 1, msg: '请填写正确的投保人出生日期' };
    }
    params.holderIdCard = idCard1;

    if (!isPhoneNum(phoneNo) && !isMaskedAndT1Phone(starPhone, mTel)) {
        return { code: 1, msg: '请填写正确的手机号码' };
    }

    const phone = isPhoneNum(phoneNo) ? phoneNo : mTel;
    params.holderPhone = phone;
    params.insuredPhone = phone;
    params.operatorPhone = mTel || phoneNo;

    const age = GetAge(idCard1);
    const age1 = GetAge(insuredIdCard);
    if (age < 18) {
        return { code: 1, msg: '投保人年龄小于18周岁，可选择为儿女投保的方式进行投保' };
    }

    if (relation != 1) {
        if (idCard1 == insuredIdCard) {
            return { code: 1, msg: '您与被保人身份证号码不能相同' };
        }
    }

    if (relation == 2) {
        const agender = (+idCard1.slice(16, 17)) % 2;
        const agender1 = (+insuredIdCard.slice(16, 17)) % 2;
       if (age1 < 18) {
            return { code: 1, msg: '配偶年龄不能小于18周岁' }
        }
        if (agender == agender1) {
            return { code: 1, msg: '配偶双方性别不能相同' };
        }
    }

    if (relation == 3 && (age - age1 < 16)) {
        return { code: 1, msg: '您与儿女年龄至少要相差16周岁' };
    }

    if (relation == 4 && (age1 - age < 16)) {
        return { code: 1, msg: '您与父母年龄至少要相差16周岁' };
    }

    const index = window.location.href.indexOf('/ZYBX/');
    let resultUrl = `${window.location.href.substring(0, index)}/ZYBX/TK23/Upgrade`;

    const obj = {
        channel,
        school,
        mTel,
        starPhone,
        upgradePremium,
        totalPremium,
        relation,
        name1,
        idCard1,
        phoneNo,
        [certNo]: insuredIdCard,
        [certName]: insuredName,
    }

    const param = url_safe_b64_encode(JSON.stringify(obj));
    resultUrl = `${resultUrl}?param=${param}&tb_uuid=${traceBackUuid}`;
    orderInfo.callbackUrl = resultUrl;

    let failUrl = `${window.location.href.substring(0, index)}/ZYBX/TK23/PayAgain`;
    failUrl = `${failUrl}?channel=${channel}&mTel=${mTel}&premium=${totalPremium}&tb_uuid=${traceBackUuid}`;
    params.extendParams = {
        frontendNotifyUrl: resultUrl,
        failUrl: failUrl,
        school,
        autoRenewalType,
        platform: isInWx() ? 'WX' : 'WAP',
    };

    if (idCardType1 == '08') {
        const obj = {
            holderIdCardType: idCardType1,
            holderBirthday: birthday1,
            holderSex: gender1,
        }
        Object.assign(params.extendParams, obj);
    }

    if (icTypeInsured == '08') {
        const obj = {
            insuredIdCardType: icTypeInsured,
            insuredBirthday: birthInsured,
            insuredSex: genderInsured,
        }
        Object.assign(params.extendParams, obj);
    }

    saveOrderInfo(orderInfo);

    if (!checked) {
        return { code: 2, msg: '用户协议未同意', params }; //
    }

    return { code: 0, params };
}

export const submitOrderInfo = (params, orderInfo,) => {
    return new Promise((resolve, reject) => {
        fetchCreateTKZengXianOrder(params).then(result => {
            orderResultHandle(result, orderInfo, resolve, reject);
        }).catch(err => {
            reject({ reason: '接口出错', msg: JSON.stringify(err) });
        });
    });
}

export const submitPromoteOrderInfo = (params, orderInfo) => {
    return new Promise((resolve, reject) => {
        createPromotionOrder(params).then(result => {
            orderResultHandle(result, orderInfo, resolve, reject);
        }).catch(err => {
            reject({ reason: '接口出错', msg: JSON.stringify(err) });
        });
    });
}

const orderResultHandle = (result, orderInfo, resolve, reject) => {
    const { code, msg, data } = result.data || {};
    if (code != 2000) {
        return reject({ reason: '核保失败', msg });
    }

    const { orderNo, success, payforURL, extentMap, msg: err } = data || {};

    if (extentMap && extentMap.proposalId) {
        orderInfo.proposal_id = extentMap.proposalId || '';
    }

    if (orderNo) {
        orderInfo.infoNo = orderNo;
    }
    if (!success) {
        return reject({ reason: '核保失败', msg: err });
    }
    return resolve(payforURL);
}

export const upgradeOrderInfo = (params) => {
    return new Promise((resolve, reject) => {
        fetchTKCubeUpgrade(params).then(res => {
            const { code, data } = res.data || {};
            if (code != 2000) {
                return reject({ reason: '升级失败', msg: data || '保单升级出错，请稍后重试' });
            }
            return resolve();
        }).catch(err => {
            reject({ reason: '接口出错', msg: JSON.stringify(err) });
        });
    });
}

export const calculatePremium = (idCardNo, insurance, repay) => {
    if (!idCardNo || idCardNo.length != 18) {
        return { low: 0, high: 0 };
    }

    const birthday = idCardNo.substr(6, 8);
    const age = moment().add(1, 'days').diff(moment(birthday), 'year'); // 计算年龄
    const premiumObj = PremiumRate.find(item => item.min <= age && item.max >= age);
    const premiumObj1 = PremiumRate1.find(item => item.min <= age && item.max >= age);

    // 年龄超出投保范围
    if (!premiumObj || !premiumObj1) {
        return { low: 0, high: 0 };
    }

    const obj = repay == 0 ? premiumObj.year : premiumObj.month;
    const obj1 = repay == 0 ? premiumObj1.year : premiumObj1.month;

    const premium = insurance == 0 ? obj.data2 : obj.data1;
    const premium1 = insurance == 0 ? obj1.data2 : obj1.data1;

    return { low: premium, high: premium1 };
}

export const getPolicyVersion = (idCard) => {
    if (!idCard || idCard.length != 18) {
        return 'v2';
    }

    const versionList = [
        { min: 0, max: 4, version: 'v6' },
        { min: 5, max: 10, version: 'v7' },
        { min: 11, max: 45, version: 'v8' },
        { min: 46, max: 50, version: 'v6' },
        { min: 51, max: 55, version: 'v5' },
        { min: 56, max: 60, version: 'v4' },
        { min: 61, max: 65, version: 'v3' },
        { min: 66, max: 100, version: 'v2' },
    ]

    const birthday = idCard.substr(6, 8);
    const age = moment().add(1, 'days').diff(moment(birthday), 'year'); // 计算年龄
    const obj = versionList.find(item => item.min <= age && item.max >= age) || { version: 'v2' };

    return obj.version;
}

export const eventTracking = (orderInfo, name, time = 0) => {
    const { action, productKey, mTel, tel, channel, phoneNo, identifier } = orderInfo;
    const map = {
        back: '返回',
        promotion: '促活跳转',
        follow: '结果跳转',
        rFollow: '新版结果跳转',
        direct: '直发',
        forward: '转发',
        result: '结果页',
        promote: '促活'
    };
    const prefix = map[action] ? `${map[action]}-` : '';
    const page = `${prefix}泰康泰超能魔方${identifier}`;
    const phone = mTel || tel || (isPhoneNum(phoneNo) ? phoneNo : '');

    actionTracking({
        page: `${page}(${productKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: productKey,
        time: time,
    }).then(res => {
        const { code, data } = res;
        if (code == 2000) {
            const { mobileId } = data || {};
            mobileId && (orderInfo.mTel = mobileId);
        }
    });
}

export const loadOrderInfo = (key = 'TK23Info') => {
    return bxStorage.getObjItem(key) || null;
}

export const saveOrderInfo = (orderInfo, key = 'TK23Info') => {
    bxStorage.setObjItem(key, orderInfo);
}

export const showToast = (message = '', duration = 2000) => {
    Toast({
        message: message,
        duration: duration, // 弹窗时间毫秒
        position: 'middle',
        forbidClick: true,
    });
}

export const inputEndEditing = () => {
    const inputList = document.getElementsByTagName('input') || [];
    for (const input of inputList) {
        input.blur && input.blur();
    }
}

export const createNoticeList = () => {
    const noticeList = [];
    for (let idx = 0; idx < 100; idx++) {
        const tailNumber = (Math.random() * 8999 + 1000).toFixed(0);
        noticeList.push(tailNumber);
    }
    return noticeList;
}