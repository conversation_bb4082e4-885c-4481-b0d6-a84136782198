<template>
	<van-popup v-model="obj.v1" class="container_2302221730" position="bottom" round>
		<van-tabs v-model="obj.page1">
			<van-tab v-for="page in pageList" :key="page" :name="page" :title="page"></van-tab>
		</van-tabs>
		<div id="id_2302221730" class="content">
			<Policy_List v-if="obj.page1 == '保险条款'" :obj="obj"></Policy_List>
			<HHFileViewer v-else :fileName="obj.page1 + '$' + obj.belongs"></HHFileViewer>
		</div>
		<div class="footer" @click="okAction">
			<div class="button">我已逐页阅读上述内容并同意</div>
		</div>
	</van-popup>
</template>

<script>
import { documentList } from "../src";
import HHFileViewer from "./HHFileViewer";
import Policy_List from "./Policy/Policy_List";

export default {
	name: "HHTabViewer",
	props: { obj: Object },
	components: {
		Policy_List,
		HHFileViewer,
	},
	computed: {
		pageList() {
			return documentList.filter(v => v.belongs.includes(this.obj.belongs)).map(item => item.page);
		},
	},
	watch: {
		'obj.page1': {
			handler() {
				this.scrollToTop();
			},
		},
	},
	methods: {
		okAction() {
			this.obj.v1 = false;
			this.$emit('ok');
		},

		scrollToTop() {
			this.$nextTick(() => {
				const node = document.querySelector('#id_2302221730');
				node && (node.scrollTop = 0);
			});
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302221730 {
		height: 80%;
		width: 3.75rem;
		inset: auto 0 0 0;
		margin: 0 auto;

		display: flex;
		flex-direction: column;

		.content {
			flex: 1;
			overflow: auto;
		}

		.footer {
			background-color: #ffffff;
			border-top: #f2f2f2 1px solid;

			.button {
				margin: 0.05rem 0.25rem 0.1rem;
				padding: 0.16rem 0;
				border-radius: 0.25rem;
				font-size: 0.16rem;
				font-weight: 500;
				text-align: center;
				color: #ffffff;
				background: linear-gradient(
					to right,
					rgba(255, 20, 30, 0.65),
					rgba(255, 0, 0, 0.8)
				);
			}
		}
	}
</style>

