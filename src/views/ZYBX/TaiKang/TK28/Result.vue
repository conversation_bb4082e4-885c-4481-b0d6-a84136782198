<template>
	<div class="container_2302030930">
		<div class="header" v-if="isShow">
			<div class="header-mark">
				<img class="header-mark-icon" src="@/assets/imgs/common/icon_selected.png" />
				<span class="header-mark-text">你已成功加强保障</span>
			</div>
			<div class="header-board">
				<div class="header-board-name">{{ orderDict.name }}</div>
				<div class="header-board-list">
					<div v-for="item in orderDict.list" :key="item.key" class="header-board-list-item">
						<div class="header-board-list-item-key">{{ item.key }}</div>
						<div class="header-board-list-item-value">{{ item.value }}</div>
					</div>
				</div>
				<div class="header-board-img">
					<div class="header-board-img-upgrade">{{ orderDict.upgradeDate }}</div>
					<div class="header-board-img-end">{{ orderDict.endDate }}</div>
				</div>
			</div>
		</div>
		<!--录屏-->
		<TKRecord ref="ref_record" :recordObj="orderInfo"></TKRecord>
	</div>
</template>

<script>
import moment from "moment";
import { Dialog } from "vant";
import TKRecord from "@/views/components/TKRecord";
import { createOrderInfo, eventTracking, loadOrderInfo, upgradeOrderInfo, } from "./function";
import { fetchHTBWOrderStatus, fetchRoundRobinWithPhoneOrderFilter, } from '@/api/insurance-api'
import { TraceLogInfoKeys, isPhoneNum, url_safe_b64_decode, isMaskedAndT1Phone, object2QueryParams } from "@/assets/js/common";
import { domainPathMap } from "@/views/ZYBX/src";

export default {
	components: { TKRecord, },
	data() {
		const orderInfo = createOrderInfo('result');
		return {
			orderInfo: orderInfo,
			fromPromote: 0,
			isShow: false,
			nextObj: { visible: false, path: '' },
			orderDict: {
				name: '',
				list: [
					{ key: '被保险人', value: '' },
					{ key: '保单号', value: '' },
					{ key: '保障期限', value: '' },
				],
				startDate: '',
				upgradeDate: '',
				endDate: '',
			}
		}
	},

	mounted() {
		if (history.scrollRestoration) {
			history.scrollRestoration = 'manual';
		}

		this.init();
	},

	methods: {
		init() {
			const inStore = loadOrderInfo();
			const query = this.$route.query || {};
			Object.assign(this.orderInfo, inStore, query);

			this.orderInfo.identifier = 'TK28Result';
			this.orderInfo.productKey = TraceLogInfoKeys.iyb_tk_391_cn_medical_high_cube_base;
			this.orderInfo.action = 'result';
			this.orderInfo.source = 'result';

			this.fromPromote = query.fromPromote || 0;

			this.decodeUserInfo();

			this._actionTracking('结果页');

			if (query.promote == 'promotion6') {
				this.fetchOrderDetail();
			} else {
				this.upgradeAction();
			}
		},

		async upgradeAction() {
			const { infoNo } = this.orderInfo;
			if (!infoNo) {
				return this.$toast.fail('订单不存在');
			}

			this.$toast.loading({
				message: '订单处理中...',
				forbidClick: true,
				duration: 0,
			});

			for (let idx = 0; idx < 3; idx++) {
				const start = Date.now();
				try {
					const page = this.fromPromote ? 'upgrade:promotion' : '';
					const params = { infoNo: this.orderInfo.infoNo, page };
					await upgradeOrderInfo(params);
					return this.fetchOrderDetail();
				} catch (e) {
					const diff = start + 3000 - Date.now();
					if (diff > 0) {
						await new Promise(resolve => {
							setTimeout(resolve, diff);
						});
					}
				}
			}

			return this.fetchOrderDetail();
		},

		async fetchOrderDetail() {
			this.$toast.loading({
				message: '订单处理中...',
				forbidClick: true,
				duration: 0,
			});

			const { infoNo, channel } = this.orderInfo;

			let start = 0;
			for (let idx = 0; idx < 5; idx++) {
				const diff = start + 2000 - Date.now();
				if (diff > 0) {
					await new Promise(resolve => {
						setTimeout(resolve, diff);
					});
				}
				start = Date.now();

				try {
					const res = await fetchHTBWOrderStatus({ infoNo });
					const { code, data: { effectiveDate, policyNo, insuredName } } = res.data || {};
					if (code == 2000 && policyNo) {
						this.$toast.clear(true);
						this.orderInfo.policyNo = policyNo;
						this.orderInfo.effectDate = effectiveDate;

						this.isShow = true;

						this.orderDict.name = '泰超能·百万医疗险';
						if (effectiveDate) {
							this.orderDict.startDate = effectiveDate;
							this.orderDict.upgradeDate = moment(effectiveDate).add(1, 'months').format('YYYY-MM-DD');
							this.orderDict.endDate = moment(effectiveDate).add(1, 'years').subtract(1, 'days').format('YYYY-MM-DD');
						}

						const list = [
							{ key: '被保险人', value: insuredName || '' },
							{ key: '保单号', value: this.orderInfo.policyNo },
							{ key: '保障期限', value: `${this.orderDict.startDate}至${this.orderDict.endDate}` },
						]
						this.orderDict.list = list;

						if (channel > 1000) {
							this.fetchNextLink();
						}

                        return;
					}
				} catch (e) {

				}
			}

			this.$toast.clear(true);

			Dialog.confirm({
				title: "查询结果",
				message: "订单已支付，处理中...",
				confirmButtonText: "重新查询",
				showCancelButton: false
			}).finally(() => {
				this.fetchOrderDetail();
			});
		},

		decodeUserInfo() {
			const param = url_safe_b64_decode(this.orderInfo.param);
			if (!param) return;

			const obj = JSON.parse(param);
			Object.assign(this.orderInfo, obj);
		},

		fetchNextLink() {
			const { channel, phoneNo, mTel } = this.orderInfo;

			const params = {
				channelId: channel,
				phone: isPhoneNum(phoneNo) ? phoneNo : mTel,
				robinKey: 'tc_tk_mf_result',
			}

			fetchRoundRobinWithPhoneOrderFilter(params).then(result => {
				const { path } = result.data || {};
				this.nextObj.path = path || '';
			}).finally(() => {
				this.pushNextLink();
			});
		},

		pushNextLink() {
			let path = this.nextObj.path || '';
			if (!domainPathMap[path]) {
				path = 'IYBTK10Index2';
			}

			this._actionTracking(`加保跳转(${path})`);

			const { channel, phoneNo, identifier, policyNo, effectDate, } = this.orderInfo;
			const { starPhone, mTel, idCard1, name1, relation } = this.orderInfo;

			const params = {
				channel, mTel, relation, source: identifier, prevDate: effectDate, prevPolicy: policyNo, prevName: '泰超能·百万医疗险',
				name1, idCard1, [`name${relation}`]: this.orderInfo[`name${relation}`], [`idCard${relation}`]: this.orderInfo[`idCard${relation}`]
			}

			if (isPhoneNum(phoneNo)) {
				params.phoneNo = phoneNo;
				params.starPhone = '';
			} else if (isMaskedAndT1Phone(starPhone, mTel)) {
				params.phoneNo = '';
				params.starPhone = starPhone;
			}

			const query = object2QueryParams(params);

            setTimeout(() => {
				this.$refs.ref_record && this.$refs.ref_record.endRecord();
			}, 1500);

			setTimeout(() => {
				const href = domainPathMap[path];
				window.location.href = `${href}?${query}`;
			}, 2000);
		},

		_actionTracking(name, time = 0) {
			eventTracking(this.orderInfo, name, time);
		},
	}
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302030930 {
		font-size: 0.15rem;
		position: relative;
		width: 3.75rem;
		min-height: 100%;
		background-color: #ffffff;

		.header {
			font-family: sans-serif;
			width: 100%;
			height: 3.35rem;
			background: url("~@/assets/imgs/ZhongAn/ZA15/img02.png") no-repeat top;
			background-size: 100%;

			.header-mark {
				padding: 0.2rem 0 0.15rem;
				display: flex;
				align-items: center;
				justify-content: center;

				.header-mark-icon {
					width: 0.3rem;
				}

				.header-mark-text {
					margin-left: 0.1rem;
					font-size: 0.18rem;
					font-weight: 600;
					color: #fe5c00;
				}
			}

			.header-board {
				box-sizing: border-box;
				margin: 0 auto;
				padding: 0 0.15rem 0.05rem;
				width: 3.4rem;
				background-color: #ffffff;
				border-radius: 0.12rem;
				box-shadow: 0 0 0.1rem 0 #ffb596;

				.header-board-name {
					padding: 0.15rem 0 0.1rem;
					font-size: 0.18rem;
					font-weight: 500;
					color: #1d243e;
				}

				.header-board-list {
					.header-board-list-item {
						display: flex;
						justify-content: space-between;
						padding: 0.05rem 0;

						.header-board-list-item-key {
							color: #999999;
						}

						.header-board-list-item-value {
							color: #474e66;
						}
					}
				}

				.header-board-img {
					position: relative;
					margin-top: 0.1rem;
					width: 100%;
					height: 1.05rem;
					background: url("~@/assets/imgs/common/upgrade_result.png")
						no-repeat top;
					background-size: 100%;
					color: #a0a4b3;
					font-size: 0.12rem;

					.header-board-img-upgrade {
						position: absolute;
						left: 0.85rem;
						top: 0.6rem;
					}

					.header-board-img-end {
						position: absolute;
						right: 0.1rem;
						top: 0.6rem;
					}
				}
			}
		}
	}
</style>
