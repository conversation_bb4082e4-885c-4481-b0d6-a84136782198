<template>
	<div class="container_2301031055">
		<img :src="imgUrl" />
	</div>
</template>

<script>

export default {
	name: "HHRateSchedule",
	props: { obj: Object, },
	computed: {
		imgUrl() {
			return this.obj.belongs == 'v1' ? require('@/assets/imgs/TaiKang/TK22/rate1.png') : require('@/assets/imgs/TaiKang/TK19/rate2.png');
		},
	},
}

</script>

<style lang="less" scoped type="text/less">
	.container_2301031055 {
		background-color: #ffffff;

		img {
			display: block;
			max-width: 100%;
		}
	}
</style>
