<template>
	<van-popup v-model="obj.v2" class="container_2212271130" position="bottom" round>
		<div class="header">
			<img src="@/assets/imgs/TaiKang/TK16/img03.png">
			<div class="header-premium">
				次月起只需{{ premiumObj.next }}元/月，无需重复投保<br />
				升级后的保障将于第二期保费扣费/缴费成功后生效
			</div>
		</div>
		<div id="id_2212271130" class="content">
			<Policy_Health></Policy_Health>
		</div>
		<div class="footer">
			<div class="footer-text">
				我同意次月变更保障
				<span class="view-txt" @click.stop="onViewPolicy('健康告知',true)">《健康告知》</span>
				<span class="view-txt" @click.stop="onViewPolicy('投保须知',true)">《投保须知》</span>
				<span class="view-txt" @click.stop="onViewPolicy('责任免除',true)">《责任免除》</span>
				<span class="view-txt" @click.stop="onViewPolicy('特别约定',true)">《特别约定》</span>
				<span class="view-txt" @click.stop="onViewPolicy('产品说明',true)">《产品说明》</span>
				<span class="view-txt" @click.stop="onViewPolicy('保险条款',true)">《保险条款》</span>
				<span class="view-txt" @click.stop="onViewPolicy('百万医疗高危职业表',true)">《百万医疗高危职业表》</span>
				<span class="view-txt" @click.stop="onViewPolicy('费率表',false)">《费率表》</span>。
			</div>
			<div class="button-container">
				<div class="button" @click="onclick(false)">
					不同意
				</div>
				<div class="button color" @click="onclick(true)">
					同意并继续
				</div>
			</div>
		</div>
	</van-popup>
</template>

<script>
import Policy_Health from "./Policy/Policy_Health";
import {getPolicyVersion} from '../function';

export default {
	name: "HHSilentViewer",
	components: { Policy_Health, },
	props: { obj: Object, obj1: Object },
	watch: {
		'obj.v2': {
			handler() {
				this.scrollToTop();
			},
		},
	},
	computed: {
		premiumObj() {
			const { totalPremium, upgradePremium } = this.obj1;
			return { first: totalPremium, next: upgradePremium };
		},
	},
	methods: {
		onclick(ret) {
			this.obj.v2 = false;
			this.$emit('click', ret);
		},
		onViewPolicy(name, isPolicy) {
			this.obj.belongs = getPolicyVersion(this.obj1[`idCard${this.obj1.relation}`]);
			this.obj[isPolicy ? 'v1' : 'v'] = true;
			this.obj[isPolicy ? 'page1' : 'page'] = name;
		},
		scrollToTop() {
			const node = document.getElementById('id_2212271130');
			node && (node.scrollTop = 0);
		}
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2212271130 {
		height: 98%;
		width: 3.75rem;
		inset: auto 0 0 0;
		margin: 0 auto;

		display: flex;
		flex-direction: column;

		img {
			display: block;
			max-width: 100%;
		}

		.view-txt {
			color: #ff8c41;
			font-weight: 500;
		}

		.header {

			.header-premium {
                margin-top: 0.05rem;
				font-size: 0.12rem;
				color: #666666;
				text-align: center;
				line-height: 1.5;
			}
		}

		.content {
			flex: 1;
			overflow: auto;
		}

		.footer {
			background-color: #ffffff;
			border-top: #f2f2f2 1px solid;

			.footer-text {
				padding: 0.05rem 0.1rem 0;
				font-size: 0.13rem;
				line-height: 1.5;
				text-align: justify;
			}

			.button-container {
				display: flex;
				justify-content: space-around;

				.button {
					margin: 0.05rem 0.2rem 0.1rem;
					padding: 0.12rem 0;
					width: 1.5rem;
					border-radius: 999px;
					font-size: 0.16rem;
					font-weight: 500;
					text-align: center;
					color: #666666;
					border: #eeeeee 1px solid;
					background-color: #ffffff;
				}

				.color {
					color: #ffffff;
					background: linear-gradient(to right, #ffcc00, #ff9500);
				}
			}
		}
	}
</style>
