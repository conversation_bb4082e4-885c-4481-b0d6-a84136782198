<template>
	<div class="container_2301031055">
		<img :src="imgUrl" />
	</div>
</template>

<script>

export default {
	name: "HHRateSchedule",
	props: { obj: Object, },
	computed: {
		imgUrl() {
			return require('@/assets/imgs/TaiKang/TK34/rate.png')
		},
	},
}

</script>

<style lang="less" scoped type="text/less">
	.container_2301031055 {
		background-color: #ffffff;

		img {
			display: block;
			max-width: 100%;
		}
	}
</style>
