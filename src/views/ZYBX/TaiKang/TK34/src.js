export const PremiumRate = [
    { min: 0, max: 4, month: { data1: 98.66, data2: 242.74 }, year: { data1: 1076, data2: 2648 } },
    { min: 5, max: 10, month: { data1: 54.04, data2: 120.54 }, year: { data1: 589.4, data2: 1314.8 } },
    { min: 11, max: 15, month: { data1: 39.7, data2: 73.44 }, year: { data1: 433.16, data2: 801.16 } },
    { min: 16, max: 17, month: { data1: 42.07, data2: 78.56 }, year: { data1: 458.96, data2: 856.96 } },
    { min: 18, max: 20, month: { data1: 42.07, data2: 79.16 }, year: { data1: 465.4, data2: 863.4 } },
    { min: 21, max: 25, month: { data1: 51.09, data2: 100.82 }, year: { data1: 557.2, data2: 1099.8 } },
    { min: 26, max: 30, month: { data1: 61.08, data2: 129.03 }, year: { data1: 666.2, data2: 1407.4 } },
    { min: 31, max: 35, month: { data1: 80.14, data2: 172.64 }, year: { data1: 874.2, data2: 1883.2 } },
    { min: 36, max: 40, month: { data1: 100.92, data2: 233.66 }, year: { data1: 1100.8, data2: 2549 } },
    { min: 41, max: 45, month: { data1: 129.44, data2: 341.64 }, year: { data1: 1412, data2: 3726.8 } },
    { min: 46, max: 50, month: { data1: 130.08, data2: 416.82 }, year: { data1: 1418.8, data2: 4547 } },
    { min: 51, max: 55, month: { data1: 155.75, data2: 521.05 }, year: { data1: 1699, data2: 5684 } },
    { min: 56, max: 60, month: { data1: 183.14, data2: 630.13 }, year: { data1: 1998, data2: 6874 } },
    { min: 61, max: 65, month: { data1: 239.45, data2: 905.76 }, year: { data1: 2612, data2: 9881 } },
    { min: 66, max: 70, month: { data1: 266.30, data2: 1090.74 }, year: { data1: 2905, data2: 11899 } },
]

export const documentList = [
    { page: '健康告知', belongs: 'v1v2v3v4v5v6v7' },
    { page: '投保须知', belongs: 'v1v2v3v4v5v6v7' },
    {
        page: '保险条款', belongs: 'v1v2v3v4v5v6v7', list: [
            '住院费用医疗保险T款（互联网专属）条款',
            '互联网医院特定药品费用医疗保险C款（互联网专属）',
            '附加指定疾病扩展特需费用医疗保险（互联网专属）条款',
            '附加扩展门（急）诊医疗保险B款（互联网专属）条款',
            '附加个人住院补偿费用医疗保险条款',
        ],
    },
    { page: '责任免除', belongs: 'v1v2v3v4v5v6v7' },
    { page: '特别约定', belongs: 'v1v2v3v4v5v6v7' },
    { page: '重要信息', belongs: 'v1v2v3v4v5v6v7' },
    { page: '产品说明', belongs: 'v1v2v3v4v5v6v7' },
    { page: '百万医疗高危职业表', belongs: 'v1v2v3v4v5v6v7' },
];

export const pdfFileObj = {
    '健康告知': '/TaiKang/TK34/jkgz.pdf',

    '投保须知$v1': '/TaiKang/TK34/tbxz_v1.pdf',
    '投保须知$v2': '/TaiKang/TK34/tbxz_v2.pdf',
    '投保须知$v3': '/TaiKang/TK34/tbxz_v3.pdf',
    '投保须知$v4': '/TaiKang/TK34/tbxz_v4.pdf',
    '投保须知$v5': '/TaiKang/TK34/tbxz_v5.pdf',
    '投保须知$v6': '/TaiKang/TK34/tbxz_v6.pdf',
    '投保须知$v7': '/TaiKang/TK34/tbxz_v7.pdf',

    '责任免除$v1': '/TaiKang/TK34/zrmc_v1.pdf',
    '责任免除$v2': '/TaiKang/TK34/zrmc_v2.pdf',
    '责任免除$v3': '/TaiKang/TK34/zrmc_v3.pdf',
    '责任免除$v4': '/TaiKang/TK34/zrmc_v4.pdf',
    '责任免除$v5': '/TaiKang/TK34/zrmc_v5.pdf',
    '责任免除$v6': '/TaiKang/TK34/zrmc_v6.pdf',
    '责任免除$v7': '/TaiKang/TK34/zrmc_v7.pdf',

    '特别约定$v1': '/TaiKang/TK34/tbyd_v1.pdf',
    '特别约定$v2': '/TaiKang/TK34/tbyd_v2.pdf',
    '特别约定$v3': '/TaiKang/TK34/tbyd_v3.pdf',
    '特别约定$v4': '/TaiKang/TK34/tbyd_v4.pdf',
    '特别约定$v5': '/TaiKang/TK34/tbyd_v5.pdf',
    '特别约定$v6': '/TaiKang/TK34/tbyd_v6.pdf',
    '特别约定$v7': '/TaiKang/TK34/tbyd_v7.pdf',

    '产品说明$v1': '/TaiKang/TK34/cpsm_v1.pdf',
    '产品说明$v2': '/TaiKang/TK34/cpsm_v2.pdf',
    '产品说明$v3': '/TaiKang/TK34/cpsm_v3.pdf',
    '产品说明$v4': '/TaiKang/TK34/cpsm_v4.pdf',
    '产品说明$v5': '/TaiKang/TK34/cpsm_v5.pdf',
    '产品说明$v6': '/TaiKang/TK34/cpsm_v6.pdf',
    '产品说明$v7': '/TaiKang/TK34/cpsm_v7.pdf',

    '重要信息': '/TaiKang/TK34/zyxx.pdf',
    '百万医疗高危职业表': '/TaiKang/TK16/gwzyb.pdf',

    '住院费用医疗保险T款（互联网专属）条款': '/TaiKang/TK16/dir1.pdf',
    '互联网医院特定药品费用医疗保险C款（互联网专属）': '/TaiKang/TK16/dir2.pdf',
    '附加指定疾病扩展特需费用医疗保险（互联网专属）条款': '/TaiKang/TK16/dir3.pdf',
    '附加扩展门（急）诊医疗保险B款（互联网专属）条款': '/TaiKang/TK16/dir4.pdf',
    '附加个人住院补偿费用医疗保险条款': '/TaiKang/TK16/dir5.pdf',
}

//为谁投保
export const relations = [
    { key: "本人", value: 1 },
    { key: "配偶", value: 2 },
    { key: "儿女", value: 3 },
    { key: "父母", value: 4 },
]

//有无社保
export const insurances = [
    { key: "有医保(含新农合)", value: 1 },
    { key: "无医保", value: 0 },
]

//缴费方式
export const repays = [
    { key: "按月缴费(12期)", value: 1 },
    { key: "全额缴费", value: 0 },
]

export const planSummary = [
    { key: '社保内原发恶性肿瘤医疗保险金', value: '300万保额(共用300万)' },
    { key: '社保外原发恶性肿瘤医疗保险金', value: '300万保额(共用300万)' },
    { key: '社保内特定疾病医疗保险金', value: '600万保额(共用600万)' },
    { key: '社保外特定疾病医疗保险金', value: '600万保额(共用600万)' },
    { key: '社保内一般医疗保险金', value: '300万保额(共用300万)' },
    { key: '社保外一般医疗保险金', value: '300万保额(共用300万)' },
    { key: '质子重离子医疗保险金', value: '600万保额' },
]

export const planFeatures = [
    "保单可验真",
    "报销自费药",
    "住院可垫付"
]

export const planPoints = [
    { key: '投保年龄', value: '30天（含）-70周岁（含）' },
    { key: '等待期', value: '疾病30天，意外无等待期' },
    { key: '犹豫期', value: '15天' },
    { key: '医院范围', value: '二级以上公立普通部及保险人扩展医院的普通部' },
]

export const planPoints1 = [
    { key: '投保年龄', value: '30天（含）-70周岁（含）' },
    { key: '等待期', value: '报销型医疗险30天，给付型重疾90天，意外与续保无等待期' },
    { key: '犹豫期', value: '15天' },
    { key: '医院范围', value: '二级以上公立普通部及保险人扩展医院的普通部' },
]

export const planDetails = [
    {
        key: '社保内原发恶性肿瘤医疗保险金',
        value: '300万保额',
        text: '1.免赔额为5万元；<br>2.赔付比例：<br>（１）若以有社会医疗保险身份投保，且以社会医疗保险身份就诊并结算的，保险人按30%的比例进行赔付；<br>（２）若被保险人以有社会医疗保险身份投保，但未以社会医疗保险身份就诊并结算的，保险人按照18%的比例进行赔付；<br>（３）若被保险人以无社会医疗保险身份投保，保险人按照30%的比例进行赔付。'
    }, {
        key: '社保内特定疾病医疗保险金',
        value: '600万保额',
        text: '1.免赔额为5万元；<br>2.赔付比例：<br>（１）若以有社会医疗保险身份投保，且以社会医疗保险身份就诊并结算的，保险人按30%的比例进行赔付；<br>（２）若被保险人以有社会医疗保险身份投保，但未以社会医疗保险身份就诊并结算的，保险人按照18%的比例进行赔付；<br>（３）若被保险人以无社会医疗保险身份投保，保险人按照30%的比例进行赔付。<br>3.特定疾病:<br>共有128种，包括四类组别：心脏或心血管类特定疾病、脑中风或神经系统类特定疾病、其他类特定疾病、特殊类特定疾病，各组下的特定疾病及定义以本合同释义部分为准。'
    }, {
        key: '齿科服务：家庭健齿套餐',
        value: '免费赠送',
        text: ''
    },
]

export const planDetails1 = [
    {
        key: '社保内原发恶性肿瘤医疗保险金',
        value: '300万保额',
        text: '1.社保内恶性肿瘤医疗保险金、社保外恶性肿瘤医疗保险金共用保险金额600万<br>2.社保内恶性肿瘤医疗保险金的免赔额为0元<br>3.社保内原发恶性肿瘤医疗保险金的赔偿比例为：<br>（１）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例100%进行赔偿；<br>（２）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例60%进行赔偿；<br>（３）被保险人以未参加社会基本医疗保险或公费医疗身份投保，按照本合同约定的赔偿比例100%进行赔偿。'
    }, {
        key: '社保外原发恶性肿瘤医疗保险金',
        value: '300万保额',
        text: '1.社保内恶性肿瘤医疗保险金、社保外恶性肿瘤医疗保险金共用保险金额600万<br>2.社保外恶性肿瘤医疗保险金的免赔额为0元<br>3.社保外原发恶性肿瘤医疗保险金的赔偿比例为100%。'
    }, {
        key: '社保内特定疾病医疗保险金',
        value: '600万保额',
        text: '1.社保内特定疾病医疗保险金、社保外特定疾病医疗保险金共用保险金额600万<br>2.社保内特定疾病医疗保险金的免赔额为0元。<br>3.社保内特定疾病医疗保险金的赔偿比例为：<br>（１）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例100%进行赔偿；<br>（２）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例60%进行赔偿；<br>（３）被保险人以未参加社会基本医疗保险或公费医疗身份投保，按照本合同约定的赔偿比例100%进行赔偿。'
    }, {
        key: '社保外特定疾病医疗保险金',
        value: '600万保额',
        text: '1.社保内特定疾病医疗保险金、社保外特定疾病医疗保险金共用保险金额600万<br>2.社保外特定疾病医疗保险金的免赔额为0元。<br>3.社保外特定疾病医疗保险金的赔偿比例为100%。'
    }, {
        key: '社保内一般医疗保险金',
        value: '300万保额',
        text: '1.社保内一般医疗保险金、社保外一般医疗保险金共用保险金额300万元<br>2.社保内一般医疗保险金、社保外一般医疗保险金共用免赔额为1万元<br>3.社保内一般医疗保险金的赔偿比例为：<br>（１）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例100%进行赔偿；<br>（２）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例60%进行赔偿；<br>（３）被保险人以未参加社会基本医疗保险或公费医疗身份投保，按照本合同约定的赔偿比例100%进行赔偿。'
    }, {
        key: '社保外一般医疗保险金',
        value: '300万保额',
        text: '1.社保内一般医疗保险金、社保外一般医疗保险金共用保险金额300万元<br>2.社保内一般医疗保险金、社保外一般医疗保险金共用免赔额为1万元<br>3.社保外一般医疗保险金的赔偿比例为100%。'
    }, {
        key: '质子重离子医疗保险金',
        value: '600万保额',
        text: '1.质子重离子医疗保险金额为600万<br>2.质子重离子医疗保险金的免赔额为0元。<br>3.质子重离子医疗保险金的赔偿比例为100%。'
    },
]

export const askAnswerList = [
    {
        q: '该产品保证续保吗？',
        a: '本产品保险期限1年，不保证续保。 1）保险期间届满或保险期间届满前30日内，投保人需要重新向保险公司申请投保本产品，并经保险人审核同意，交纳保险费，获得新的保险合同。2）本产品续保费率表不同于首次投保的费率表，且续保费率略高。 3）本产品不保证投保人在续保时享受首次投保时的费率优惠幅度。'
    }, {
        q: '本产品被保险人的年龄范围是多少呢？',
        a: '本产品被保险人年龄范围是0周岁（出生满30天，含第30天）-70周岁（含）。'
    }, {
        q: '本产品有特药服务吗？',
        a: '本产品基础版、升级版A没有有恶性肿瘤院外特种药服务，但升级版B、升级版C、升级版D、升级版E、升级版F、升级版G是有特药服务的（特药目录二），药品目录以销售货架及官网信息披露为准。本产品升级版G包含互联网医院特定药品费用医疗保险金责任，如投保则被保险人拥有互联网医疗及药品配送服务，其约定的特定药品清单一、特定药品清单二和网上药店可通过泰康在线官方网站自助查询；如清单有调整，以官方网站披露信息为准。互联网医院特定药品费用医疗保险金责任的图文问诊服务入口为：泰康在线APP-我的-保单－保单服务权益。 本产品基础版、升级版A、升级版B、升级版C、升级版D、升级版E、升级版F是不包含互联网医院特定药品费用医疗保险金责任的。'
    }, {
        q: '本产品就诊医院是如何规定的呢？',
        a: '基础版保障社保内原发恶性肿瘤医疗保险金和社保内特定疾病医疗保险金，百万医疗责任的免赔额较高赔付比例也较低。升级版在基础版责任的基础上，降低医疗责任免赔额的同时提升了赔付比例，并扩展了其它附加险责任，升级版享有更全面的保障。'
    }, {
        q: '购买该产品后，在哪查询保单？',
        a: '本产品采用电子保单形式承保并提供电子发票，您可以通过下列方式查询保单。途径一：泰康在线官网www.tk.cn→客服中心→保单服务;途径二："泰康在线保险"微信公众号→保单服务→我的保单→点开对应保单→电子保单如您需要纸质保单请拨打泰康在线保险客服电话95522-3，您需要提供寄送地址以方便我司寄送，相应的快递费用将由您承担。'
    }, {
        q: '购买该产品后，在哪可修改我的个人信息或申请退保？',
        a: '您可以选择如下方式申请信息变更或退保：途径一：通过"泰康在线保险"微信公众号、泰康在线保险APP、泰康在线官网（www.tk.cn)，自助办理通信地址、联系电话变更、退保等；;途径二：联系在线客服或电话客服95522-3，由客服代为申请。无论自助申请还是通过客服申请，您都可以在"泰康在线保险"微信公众号→保单服务→我的保单→点开对应保单→批改／退保进度，来查询服务的进度。'
    }
]
