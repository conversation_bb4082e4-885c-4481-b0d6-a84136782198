<template>
	<div class="container_2302222110">
		<div v-for="v in list" :key="v" class="item" @click="onViewPolicy(v)">
			{{ v }}
		</div>
	</div>
</template>

<script>
import { documentList } from "../../src";

export default {
	name: "Policy_List",
	props: { obj: Object },
	computed: {
		list() {
			const { page1, belongs } = this.obj;
			const obj = documentList.find(v => v.page == page1 && v.belongs.includes(belongs)) || {};
			return obj.list || [];
		}
	},
	methods: {
		onViewPolicy(name) {
			if (!this.obj) return;
			this.obj.page = name;
			this.obj.v = true;
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2302222110 {
		.item {
			padding: 0.2rem 0.1rem;
			border-bottom: #eeeeee 1px solid;
			font-size: 0.14rem;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
	}
</style>

