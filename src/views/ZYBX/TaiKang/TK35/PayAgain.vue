<template>
    <div class="container_220816">
        <div v-if="!midPageDefault">
            <div class="header">
                <div>
                    <p>待支付</p>
                    <p>请在保单生效前完成支付，<br/>以免影响您的保障。</p>
                </div>
                <img src="https://upay.tk.cn/payment/v1/mobile/page/img/icon.png">
            </div>
            <div class="board" style="margin-top: -0.30rem">
                <div class="title1">请确认订单金额</div>
                <div class="title2">￥<span class="number">{{ premium }}</span></div>
                <div class="title3">泰超能·百万医疗险</div>
            </div>
            <div class="board">
                <div v-if="payment == 0" :class="payment == 0 ? 'selected' : 'unselected'" class="listItem wx"
                     @click="changePayment(0)">
                    <div class="text">微信支付</div>
                </div>
                <div v-if="payment == 1" :class="payment == 1 ? 'selected' : 'unselected'" class="listItem alipay"
                     @click="changePayment(1)">
                    <div class="text">支付宝支付</div>
                </div>
            </div>
            <div class="button" @click="onSubmit">确认支付</div>
        </div>
        <!--录屏-->
        <TKRecord ref="ref_record" :recordObj="orderInfo"></TKRecord>
    </div>
</template>

<script>
import TKRecord from "@/views/components/TKRecord";
import {TraceLogInfoKeys} from "@/assets/js/common";
import {eventTracking, loadOrderInfo, showToast, submitPromoteOrderInfo} from "./function";

export default {
    name: "PayAgain",
    components: {TKRecord,},
    data() {
        const orderInfo = {
            source: '',
            action: '',
            productKey: '',
            identifier: '',
            channel: '1',
            mTel: '',       // 加密手机号
            infoNo: '',     // 订单号
            premium: 0,
        };

        return {
            orderInfo: orderInfo,
            payment: 0,   // 0:微信；1:支付宝
            midPageDefault: false,
        }
    },
    computed: {
        premium() {
            return this.orderInfo.premium;
        },
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.init();
    },
    methods: {
        //初始化
        init() {
            const query = this.$route.query || {};
            const inStore = loadOrderInfo();
            Object.assign(this.orderInfo, inStore, query);

            this.orderInfo.identifier = 'TK35PayAgain';
            this.orderInfo.productKey = TraceLogInfoKeys.jd_tk_cn_bw_medical_green_cube_base;

            this.midPageDefault = query.midPage == 'default';

            this._uploadPagePerformance();

            this.payment = query.bankCode == 'ALIPAYC' ? 1 : 0;

            if (this.midPageDefault) {
                setTimeout(() => {
                    this.onSubmit();
                }, 250);
            }
        },

        changePayment(method) {
            this.payment = method;
        },

        editOrderInfo(delay, msg) {
            const {channel, mTel, source, action} = this.orderInfo;
            const location = {
                name: 'TK35Index1',
                query: {action, source, channel, mTel},
            };

            if (!delay) {
                return this.$router.replace(location);
            }

            showToast(msg || '该订单无效，正在跳转到首页');

            setTimeout(() => {
                this.$router.replace(location);
            }, 2000);
        },

        onSubmit() {
            const {infoNo,} = this.orderInfo;
            this._actionTracking('点击立即支付按钮');

            this.$toast.loading({
                message: '订单处理中...',
                forbidClick: true,
                duration: 0,
            });

            const params = {infoNo: infoNo,};
            submitPromoteOrderInfo(params, this.orderInfo).then(url => {
                window.location.href = url;
            }).catch(err => {
                const {reason, msg} = err;
                const message = msg || '';
                this.editOrderInfo(true, message);
            }).finally(() => {
                this.$toast.clear(true);
            });
        },

        _uploadPagePerformance() {
            const {timing} = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const {domContentLoadedEventEnd, fetchStart} = timing || {};
            this._actionTracking('支付挽回页', domContentLoadedEventEnd - fetchStart);
        },

        _actionTracking(name, time = 0) {
            eventTracking(this.orderInfo, name, time);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_220816 {
    width: 3.75rem;
    min-height: 100%;
    font-size: 0.16rem;
    background-color: #F2F2F2;

    .header {
        padding: 0.30rem 0.30rem 0.50rem 0.30rem;
        color: #FFFFFF;
        font-size: 0.16rem;
        font-weight: bold;
        background-color: #32ae45;
        display: flex;
        align-items: center;
        justify-content: space-between;

        p:last-child {
            margin-top: 0.15rem;
            font-size: 0.12rem;
            line-height: 0.18rem;
        }

        img {
            width: 0.64rem;
        }
    }

    .board {
        margin: 0.15rem;
        text-align: center;
        color: #333333;
        border-radius: 0.08rem;
        background-color: #FFFFFF;

        .title1 {
            padding-top: 0.25rem;
            font-weight: 500;
        }

        .title2 {
            line-height: 0.70rem;
            font-size: 0.20rem;
            font-weight: bold;
            font-family: PingFangSC-Semibold, sans-serif;

            .number {
                font-size: 0.35rem;
            }
        }

        .title3 {
            padding-bottom: 0.15rem;
            font-size: 0.14rem;
            color: #666666;
        }

        .listItem {
            position: relative;
            padding-left: 0.60rem;
            line-height: 0.60rem;

            .text {
                font-size: 0.17rem;
                text-align: left;
            }

            &::before {
                content: "";
                position: absolute;
                top: 0.14rem;
                left: 0.15rem;
                height: 0.32rem;
                width: 0.32rem;
            }

            &.wx {
                &::before {
                    background: url("../../../../assets/imgs/common/sprites.png") no-repeat 0 -0.96rem;
                    background-size: 3.2rem 6.4rem;
                }
            }

            &.alipay {
                &::before {
                    background: url("../../../../assets/imgs/common/sprites.png") no-repeat -0.32rem -0.96rem;
                    background-size: 3.2rem 6.4rem;
                }
            }

            &::after {
                content: "";
                position: absolute;
                top: 0.18rem;
                right: 0.15rem;
                height: 0.24rem;
                width: 0.24rem;
            }

            &.selected {
                &::after {
                    background: url("../../../../assets/imgs/common/sprites.png") no-repeat -0.48rem -0.48rem;
                    background-size: 2.4rem 4.8rem;
                }
            }

            &.unselected {
                &::after {
                    background: url("../../../../assets/imgs/common/sprites.png") no-repeat -0.72rem -0.48rem;
                    background-size: 2.4rem 4.8rem;
                }
            }
        }
    }

    .button {
        margin: 0.50rem auto;
        width: 3.2rem;
        height: 0.48rem;
        line-height: 0.48rem;
        color: #FFFFFF;
        font-size: 0.18rem;
        font-weight: bold;
        text-align: center;
        box-shadow: 0px 4px 8px 0px rgba(50, 174, 69, 0.3);
        border-radius: 0.24rem;
        background-color: #32ae45;
    }
}
</style>
