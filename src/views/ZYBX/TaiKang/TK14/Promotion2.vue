<template>
    <div class="container_2306150950">
        <div class="container">
            <div class="content">
                <div class="header">
                    <div class="header-count-down">
                        <van-count-down :time="countDownTime" format="mm:ss" @finish="refreshExpireTime">
                            <template #default="timeData">
                                <span class="block">{{ (timeData.minutes + '').padStart(2, '0') }}</span>
                                <span class="colon">：</span>
                                <span class="block">{{ (timeData.seconds + '').padStart(2, '0') }}</span>
                            </template>
                        </van-count-down>
                        &nbsp;后订单失效，请尽快完成支付
                    </div>
                </div>
                <img class="image" src="@/assets/imgs/TaiKang/TK12/img05.png">
                <div v-for="obj in sectionObj" :key="obj.title" class="section">
                    <div class="section-title">{{ obj.title }}</div>
                    <div v-for="item in obj.content" :key="item.key" class="section-item">
                        <div class="section-item-key" v-html="item.key"></div>
                        <div class="section-item-value">{{ item.value }}</div>
                    </div>
                </div>
                <div class="policy-box">
                    请仔细阅读
                    <span class="read" @click.stop="onReadPolicy('健康告知',true)">《健康告知》</span>
                    <span class="read" @click.stop="onReadPolicy('投保须知',true)">《投保须知》</span>
                    <span class="read" @click.stop="onReadPolicy('重要信息',true)">《重要信息》</span>
                    <span class="read" @click.stop="onReadPolicy('特别约定',true)">《特别约定》</span>
                    <span class="read" @click.stop="onReadPolicy('保险条款',true)">《保险条款》</span>
                    <span class="read" @click.stop="onReadPolicy('隐私协议',true)">《隐私协议》</span>
                </div>
            </div>
            <div class="bottom-button" @click="onSubmit">{{ premium }}元/月</div>
        </div>
        <UUPolicyPopup :obj="policyObj"></UUPolicyPopup>
        <UUTabPopup :obj="policyObj" @confirm="onSubmit" @seePolicy="seePolicy"></UUTabPopup>
        <!--录屏-->
        <TKRecord ref="ref_record" :recordObj="orderInfo"></TKRecord>
    </div>
</template>

<script>
import {fetchPromoteOrderInfo} from "@/api/insurance-api";
import TKRecord from "@/views/components/TKRecord";
import UUTabPopup from "./components/UUTabPopup";
import {TraceLogInfoKeys} from "@/assets/js/common";
import UUPolicyPopup from "./components/UUPolicyPopup";
import {
    calculatePremium,
    eventTracking,
    loadOrderInfo,
    saveOrderInfo,
    showToast,
    submitPromoteOrderInfo
} from "./function";

export default {
    name: "Promotion",
    components: {TKRecord, UUTabPopup, UUPolicyPopup},
    data() {
        const orderInfo = {
            source: 'promote',
            action: 'promote',
            productKey: TraceLogInfoKeys.iyb_tk_xxl_medical_health_cube_base,
            identifier: 'TK14Promotion2',
            insurance: 1,   // 有无社保
            repay: 1,       // 缴费方式
            channel: '1',
            mTel: '',       // 加密手机号
            infoNo: '',     // 订单号
            premium: 0,
            holderName: '',
            holderIdCard: '',
            holderPhone: '',
            insuredName: '',
            insuredIdCard: '',
            expirationTime: 0,
            // 回溯相关
            fromId: '77255',
            productCode: 'N20230026',
            productName: '泰医享·全民医疗险',
            proposal_id: '',
            isRecordEnd: false,
        };

        return {
            countDownTime: 0,
            orderInfo: orderInfo,
            policyObj: {visible: false, page: '', visible1: false, page1: '', type: 'high'},
        }
    },
    computed: {
        premium() {
            return (this.orderInfo.premium).toFixed(2);
        },
        sectionObj() {
            return [
                {
                    title: '被保人信息',
                    content: [
                        {key: '姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名', value: this.orderInfo.insuredName},
                        {key: '身份证号', value: this.orderInfo.insuredIdCard},
                    ],
                },
                {
                    title: '投保人信息',
                    content: [
                        {key: '姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名', value: this.orderInfo.holderName},
                        {key: '身份证号', value: this.orderInfo.holderIdCard},
                        {key: '手机号码', value: this.orderInfo.holderPhone}
                    ],
                },
            ];
        },
        orderPage() {
            const {productKey} = this.orderInfo;
            return `infoKey:${productKey}&page:promotion`;
        },
    },
    mounted() {
        if (history.scrollRestoration) {
            history.scrollRestoration = 'manual';
        }

        this.init();
    },
    methods: {
        saveExpirationTime(time) {
            saveOrderInfo(time, 'TK14ExpireTime');
        },

        loadExpirationTime() {
            return loadOrderInfo('TK14ExpireTime') || 0;
        },

        refreshExpireTime() {
            const diff = this.orderInfo.expirationTime - Date.now();

            if (diff <= 0 || diff > 60 * 60 * 1000) {
                this.orderInfo.expirationTime = Date.now() + 60 * 60 * 1000;
                this.saveExpirationTime(this.orderInfo.expirationTime);
            }

            this.countDownTime = this.orderInfo.expirationTime - Date.now();
        },

        //初始化
        init() {
            const inQry = this.$route.query || {}; // 必须：加密的infoNo
            const {channel, infoNo} = inQry;
            this.orderInfo.channel = channel || '1';
            this.orderInfo.expirationTime = this.loadExpirationTime();

            this.fetchOrderInfo(infoNo);
        },

        fetchOrderInfo(infoNo) {
            if (!infoNo) {
                this._contentHomeView();
                return this.editOrderInfo();
            }

            this.$toast.loading({
                message: '正在请求数据\n请稍候',
                forbidClick: true,
                duration: 0,
            });

            fetchPromoteOrderInfo(infoNo).then(res => {
                const data = res.data || {};
                this.orderInfoHandle(data);
            }).catch(err => {
                this.editOrderInfo();
            }).finally(() => {
                this._contentHomeView();

                this.$toast.clear(true);
            });
        },

        orderInfoHandle(data) {
            Object.assign(this.orderInfo, data);

            this.orderInfo.repay = 1;
            const {operatorPhone, insuredIdCard, insurance, repay,} = this.orderInfo;
            this.orderInfo.mTel = operatorPhone;
            this.orderInfo.premium = calculatePremium(insuredIdCard, insurance, repay).high;

            this.refreshExpireTime();
        },

        seePolicy(policy) {
            this.policyObj = {...this.policyObj, ...policy, visible: true};
        },

        onReadPolicy(page, isPolicy) {
            if (isPolicy) {
                return this.policyObj = {...this.policyObj, page1: page, visible1: true};
            }

            this.policyObj = {...this.policyObj, page, visible: true};
        },

        editOrderInfo(msg) {
            const {channel, mTel} = this.orderInfo;
            const location = {
                name: 'TK14Index1',
                query: {action: 'promotion', source: 'promotion', channel, mTel},
            };

            showToast(msg || '该订单无效，正在跳转到首页');

            setTimeout(() => {
                this.$router.replace(location);
            }, 2000);
        },

        onSubmit() {
            const {infoNo, channel,} = this.orderInfo;

            this._actionTracking('点击促活立即签约按钮');

            this.$toast.loading({
                message: '订单处理中...',
                forbidClick: true,
                duration: 0,
            });

            const params = {infoNo: infoNo, page: this.orderPage, channelId: channel};
            submitPromoteOrderInfo(params, this.orderInfo).then(url => {
                this.$refs.ref_record && this.$refs.ref_record.uploadVideo();
                setTimeout(() => {
                    window.location.href = url;
                }, 250);
            }).catch(err => {
                const message = err.msg || '';
                this.editOrderInfo(message);
            }).finally(() => {
                this.$toast.clear(true);
            });
        },

        pushToResult() {
            this.$refs.ref_record && this.$refs.ref_record.uploadVideo();
            setTimeout(() => {
                const {channel, mTel, infoNo,} = this.orderInfo;
                this.$router.replace({name: 'TK14Result', query: {channel, mTel, infoNo,}});
            }, 250);
        },

        _contentHomeView() {
            const {timing} = window.performance || {};
            // console.log('页面加载性能 => ' + JSON.stringify(timing));
            const {domContentLoadedEventEnd, fetchStart} = timing || {};
            this._actionTracking('签约促活页', domContentLoadedEventEnd - fetchStart);
        },

        _actionTracking(name, time = 0) {
            eventTracking(this.orderInfo, name, time);
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.container_2306150950 {
    width: 3.75rem;
    height: 100%;
    font-size: 0.15rem;
    background-color: #F2F2F2;

    .container {
        height: 100%;
        display: flex;
        flex-direction: column;

        .content {
            flex: 1;
            overflow: auto;

            .header {
                position: relative;
                height: 2.28rem;
                background: url("~@/assets/imgs/TaiKang/TK12/img04.png") no-repeat center;
                background-size: 100%;

                .header-count-down {
                    position: absolute;
                    left: 0.15rem;
                    top: 0.75rem;

                    display: flex;
                    align-items: center;
                    color: #FFFFFF;
                    font-size: 0.14rem;
                    font-weight: 500;

                    /deep/ .van-count-down {
                        display: flex;
                        align-items: center;
                    }

                    .colon {
                        display: inline-block;
                        margin-left: 0.05rem;
                        color: #FFFFFF;
                        font-weight: 700;
                    }

                    .block {
                        display: inline-block;
                        width: 0.35rem;
                        padding: 0.02rem 0 0.01rem;
                        color: #F88D6D;
                        font-size: 0.18rem;
                        font-weight: 700;
                        text-align: center;
                        border-radius: 0.04rem;
                        background-color: #FFFFFF;
                    }
                }
            }

            .image {
                display: block;
                width: 100%;
            }

            .section {
                margin: 0.10rem 0;
                padding: 0.15rem 0.15rem 0.10rem;
                background-color: #FFFFFF;

                .section-title {
                    padding-bottom: 0.10rem;
                    font-size: 0.17rem;
                    font-weight: 700;
                }

                .section-item {
                    padding: 0.05rem 0;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .section-item-key {
                        color: #333333;
                        font-size: 0.14rem;
                        font-weight: 500;
                    }

                    .section-item-value {
                        font-size: 0.16rem;
                        font-weight: 700;
                    }
                }
            }

            .policy-box {
                margin: 0.20rem 0.10rem;
                font-size: 0.13rem;
                color: #333333;
                line-height: 1.60;
                text-align: justify;

                .read {
                    color: #FE1703;
                    font-weight: 500;
                }
            }
        }

        .bottom-button {
            padding: 0.20rem 0;
            color: #FFFFFF;
            font-size: 0.18rem;
            font-weight: 700;
            text-align: center;
            background-color: #FE1703;
        }
    }
}

</style>
