export const PremiumRate = [
    { min: 0, max: 60, month: { data1: 0.6, data2: 2 }, year: { data1: 6.1, data2: 21.4 } },
    { min: 61, max: 70, month: { data1: 0.6, data2: 2.2 }, year: { data1: 6.9, data2: 24.1 } },
]

export const PremiumRate1 = [
    { min: 0, max: 4, month: { data1: 108.79, data2: 205.17 }, year: { data1: 998, data2: 2486 } },
    { min: 5, max: 10, month: { data1: 66.28, data2: 107.27 }, year: { data1: 511.4, data2: 1236.8 } },
    { min: 11, max: 15, month: { data1: 48.92, data2: 72.65 }, year: { data1: 315.4, data2: 683.4 } },
    { min: 16, max: 17, month: { data1: 45.71, data2: 66.78 }, year: { data1: 341.2, data2: 739.2 } },
    { min: 18, max: 20, month: { data1: 48, data2: 69.07 }, year: { data1: 341.2, data2: 739.2 } },
    { min: 21, max: 25, month: { data1: 69.33, data2: 103.75 }, year: { data1: 557.2, data2: 1099.8 } },
    { min: 26, max: 30, month: { data1: 72.77, data2: 106.59 }, year: { data1: 666.2, data2: 1407.4 } },
    { min: 31, max: 35, month: { data1: 96.74, data2: 149.97 }, year: { data1: 874.2, data2: 1883.2 } },
    { min: 36, max: 40, month: { data1: 103.94, data2: 172.96 }, year: { data1: 1100.8, data2: 2549 } },
    { min: 41, max: 45, month: { data1: 129.45, data2: 248.56 }, year: { data1: 1287.8, data2: 3602.6 } },
    { min: 46, max: 50, month: { data1: 141.26, data2: 331.70 }, year: { data1: 1418.8, data2: 4547 } },
    { min: 51, max: 55, month: { data1: 167.38, data2: 481.16 }, year: { data1: 1533, data2: 5269 } },
    { min: 56, max: 60, month: { data1: 186.55, data2: 574.88 }, year: { data1: 1998, data2: 6874 } },
    { min: 61, max: 65, month: { data1: 247.12, data2: 814.72 }, year: { data1: 2612, data2: 9881 } },
    { min: 66, max: 70, month: { data1: 267.75, data2: 937.29 }, year: { data1: 2905, data2: 11899 } },
]

export const documentList = [
    { page: '健康告知', belongs: 'v1v5v7v8v9v10v11v12' },
    { page: '投保须知', belongs: 'v1v5v7v8v9v10v11v12' },
    {
        page: '保险条款', belongs: 'v1', list: [
            '个人医疗保险V款（互联网专属）',
        ],
    },
    {
        page: '保险条款', belongs: 'v5v7v8v9v10v11v12', list: [
            '个人医疗保险V款（互联网专属）',
            '互联网医院特定药品费用医疗保险C款（互联网专属）',
            '附加个人住院补偿费用医疗保险（互联网专属）',
            '附加癌症院外特定药品医疗保险H款（互联网专属）',
            '附加扩展门（急）诊医疗保险B款（互联网专属）',
            '附加个人重大疾病住院津贴保险C款（互联网专属）',
            '附加指定疾病扩展特需费用医疗保险（互联网专属）',
            '附加个人重大疾病保险（互联网专属）',
        ],
    },
    { page: '责任免除', belongs: 'v1v5v7v8v9v10v11v12' },
    { page: '特别约定', belongs: 'v1v5v7v8v9v10v11v12' },
    { page: '重要信息', belongs: 'v1' },
    { page: '产品说明', belongs: 'v1v5v7v8v9v10v11v12' },
    { page: '泰康在线高危职业表', belongs: 'v5v7v8v9v10v11v12' },
];

export const pdfFileObj = {
    '健康告知': '/TaiKang/TK16/jkgz.pdf',
    '投保须知$v1': '/TaiKang/TK29/tbxz_v1.pdf',
    '投保须知$v5': '/TaiKang/TK29/tbxz_v5.pdf',
    '投保须知$v7': '/TaiKang/TK29/tbxz_v7.pdf',
    '投保须知$v8': '/TaiKang/TK29/tbxz_v8.pdf',
    '投保须知$v9': '/TaiKang/TK29/tbxz_v9.pdf',
    '投保须知$v10': '/TaiKang/TK29/tbxz_v10.pdf',
    '投保须知$v11': '/TaiKang/TK29/tbxz_v11.pdf',
    '投保须知$v12': '/TaiKang/TK29/tbxz_v12.pdf',
    '责任免除$v1': '/TaiKang/TK29/zrmc_v1.pdf',
    '责任免除$v5': '/TaiKang/TK29/zrmc_v5.pdf',
    '责任免除$v7': '/TaiKang/TK29/zrmc_v7.pdf',
    '责任免除$v8': '/TaiKang/TK29/zrmc_v8.pdf',
    '责任免除$v9': '/TaiKang/TK29/zrmc_v9.pdf',
    '责任免除$v10': '/TaiKang/TK29/zrmc_v10.pdf',
    '责任免除$v11': '/TaiKang/TK29/zrmc_v11.pdf',
    '责任免除$v12': '/TaiKang/TK29/zrmc_v12.pdf',
    '特别约定$v1': '/TaiKang/TK29/tbyd_v1.pdf',
    '特别约定$v5': '/TaiKang/TK29/tbyd_v5.pdf',
    '特别约定$v7': '/TaiKang/TK29/tbyd_v7.pdf',
    '特别约定$v8': '/TaiKang/TK29/tbyd_v8.pdf',
    '特别约定$v9': '/TaiKang/TK29/tbyd_v9.pdf',
    '特别约定$v10': '/TaiKang/TK29/tbyd_v10.pdf',
    '特别约定$v11': '/TaiKang/TK29/tbyd_v11.pdf',
    '特别约定$v12': '/TaiKang/TK29/tbyd_v12.pdf',
    '重要信息$v1': '/TaiKang/TK29/zyxx_v1.pdf',
    '产品说明$v1': '/TaiKang/TK29/cpsm_v1.pdf',
    '产品说明$v5': '/TaiKang/TK29/cpsm_v5.pdf',
    '产品说明$v7': '/TaiKang/TK29/cpsm_v7.pdf',
    '产品说明$v8': '/TaiKang/TK29/cpsm_v8.pdf',
    '产品说明$v9': '/TaiKang/TK29/cpsm_v9.pdf',
    '产品说明$v10': '/TaiKang/TK29/cpsm_v10.pdf',
    '产品说明$v11': '/TaiKang/TK29/cpsm_v11.pdf',
    '产品说明$v12': '/TaiKang/TK29/cpsm_v12.pdf',
    '泰康在线高危职业表': '/TaiKang/TK29/gwzyb.pdf',
    '个人医疗保险V款（互联网专属）': '/TaiKang/TK29/dir1.pdf',
    '互联网医院特定药品费用医疗保险C款（互联网专属）': '/TaiKang/TK29/dir2.pdf',
    '附加个人住院补偿费用医疗保险（互联网专属）': '/TaiKang/TK29/dir3.pdf',
    '附加癌症院外特定药品医疗保险H款（互联网专属）': '/TaiKang/TK29/dir4.pdf',
    '附加扩展门（急）诊医疗保险B款（互联网专属）': '/TaiKang/TK29/dir5.pdf',
    '附加个人重大疾病住院津贴保险C款（互联网专属）': '/TaiKang/TK29/dir6.pdf',
    '附加指定疾病扩展特需费用医疗保险（互联网专属）': '/TaiKang/TK29/dir7.pdf',
    '附加个人重大疾病保险（互联网专属）': '/TaiKang/TK29/dir8.pdf',
}

//为谁投保
export const relations = [
    { key: "本人", value: 1 },
    { key: "配偶", value: 2 },
    { key: "儿女", value: 3 },
    { key: "父母", value: 4 },
]

//有无社保
export const insurances = [
    { key: "有医保(含新农合)", value: 1 },
    { key: "无医保", value: 0 },
]

//缴费方式
export const repays = [
    { key: "按月缴费(12期)", value: 1 },
    { key: "全额缴费", value: 0 },
]

export const planSummary = [
    { key: '原发恶性肿瘤医疗保险金', value: '300万元' },
    { key: '特定疾病医疗保险金', value: '600万元' },
    { key: '投保年龄', value: '满30天-70周岁' },
    { key: '健康服务', value: '齿科服务' },
    { key: '等待期', value: '疾病30天意外无等待期' },
    { key: '生效日期', value: '投保成功后次日凌晨生效' },
]

export const planSummary1 = [
    { key: '社保内原发恶性肿瘤医疗保险金', value: '300万保额(共用300万)' },
    { key: '社保外原发恶性肿瘤医疗保险金', value: '300万保额(共用300万)' },
    { key: '社保内特定疾病医疗保险金', value: '600万保额(共用600万)' },
    { key: '社保外特定疾病医疗保险金', value: '600万保额(共用600万)' },
    { key: '社保内一般医疗保险金', value: '300万保额(共用300万)' },
    { key: '社保外一般医疗保险金', value: '300万保额(共用300万)' },
    { key: '质子重离子医疗保险金', value: '600万保额' },
]

export const planFeatures = [
    "保单可验真",
    "报销自费药",
    "住院可垫付"
]

export const planPoints = [
    { key: '投保年龄', value: '30天（含）-70周岁（含）' },
    { key: '等待期', value: '疾病30天，意外无等待期' },
    { key: '犹豫期', value: '15天' },
    { key: '医院范围', value: '二级以上公立普通部及保险人扩展医院的普通部' },
]

export const planPoints1 = [
    { key: '投保年龄', value: '30天（含）-70周岁（含）' },
    { key: '等待期', value: '报销型医疗险30天，给付型重疾90天，意外与续保无等待期' },
    { key: '犹豫期', value: '15天' },
    { key: '医院范围', value: '二级以上公立普通部及保险人扩展医院的普通部' },
]

export const planDetails = [
    {
        key: '社保内原发恶性肿瘤医疗保险金',
        value: '300万保额',
        text: '1.免赔额为5万元；<br>2.赔付比例：<br>（１）若以有社会医疗保险身份投保，且以社会医疗保险身份就诊并结算的，保险人按30%的比例进行赔付；<br>（２）若被保险人以有社会医疗保险身份投保，但未以社会医疗保险身份就诊并结算的，保险人按照18%的比例进行赔付；<br>（３）若被保险人以无社会医疗保险身份投保，保险人按照30%的比例进行赔付。'
    }, {
        key: '社保内特定疾病医疗保险金',
        value: '600万保额',
        text: '1.免赔额为5万元；<br>2.赔付比例：<br>（１）若以有社会医疗保险身份投保，且以社会医疗保险身份就诊并结算的，保险人按30%的比例进行赔付；<br>（２）若被保险人以有社会医疗保险身份投保，但未以社会医疗保险身份就诊并结算的，保险人按照18%的比例进行赔付；<br>（３）若被保险人以无社会医疗保险身份投保，保险人按照30%的比例进行赔付。<br>3.特定疾病:<br>共有128种，包括四类组别：心脏或心血管类特定疾病、脑中风或神经系统类特定疾病、其他类特定疾病、特殊类特定疾病，各组下的特定疾病及定义以本合同释义部分为准。'
    }, {
        key: '齿科服务：家庭健齿套餐',
        value: '免费赠送',
        text: ''
    },
]

export const planDetails1 = [
    {
        key: '社保内原发恶性肿瘤医疗保险金',
        value: '300万保额',
        text: '1.社保内恶性肿瘤医疗保险金、社保外恶性肿瘤医疗保险金共用保险金额600万<br>2.社保内恶性肿瘤医疗保险金的免赔额为0元<br>3.社保内原发恶性肿瘤医疗保险金的赔偿比例为：<br>（１）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例100%进行赔偿；<br>（２）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例60%进行赔偿；<br>（３）被保险人以未参加社会基本医疗保险或公费医疗身份投保，按照本合同约定的赔偿比例100%进行赔偿。'
    }, {
        key: '社保外原发恶性肿瘤医疗保险金',
        value: '300万保额',
        text: '1.社保内恶性肿瘤医疗保险金、社保外恶性肿瘤医疗保险金共用保险金额600万<br>2.社保外恶性肿瘤医疗保险金的免赔额为0元<br>3.社保外原发恶性肿瘤医疗保险金的赔偿比例为100%。'
    }, {
        key: '社保内特定疾病医疗保险金',
        value: '600万保额',
        text: '1.社保内特定疾病医疗保险金、社保外特定疾病医疗保险金共用保险金额600万<br>2.社保内特定疾病医疗保险金的免赔额为0元。<br>3.社保内特定疾病医疗保险金的赔偿比例为：<br>（１）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例100%进行赔偿；<br>（２）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例60%进行赔偿；<br>（３）被保险人以未参加社会基本医疗保险或公费医疗身份投保，按照本合同约定的赔偿比例100%进行赔偿。'
    }, {
        key: '社保外特定疾病医疗保险金',
        value: '600万保额',
        text: '1.社保内特定疾病医疗保险金、社保外特定疾病医疗保险金共用保险金额600万<br>2.社保外特定疾病医疗保险金的免赔额为0元。<br>3.社保外特定疾病医疗保险金的赔偿比例为100%。'
    }, {
        key: '社保内一般医疗保险金',
        value: '300万保额',
        text: '1.社保内一般医疗保险金、社保外一般医疗保险金共用保险金额300万元<br>2.社保内一般医疗保险金、社保外一般医疗保险金共用免赔额为1万元<br>3.社保内一般医疗保险金的赔偿比例为：<br>（１）被保险人以参加社会基本医疗保险或者公费医疗身份投保，并以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例100%进行赔偿；<br>（２）被保险人以参加社会基本医疗保险或者公费医疗身份投保，但未以参加社会基本医疗保险或者公费医疗身份就诊并结算的，按照本合同约定的赔偿比例60%进行赔偿；<br>（３）被保险人以未参加社会基本医疗保险或公费医疗身份投保，按照本合同约定的赔偿比例100%进行赔偿。'
    }, {
        key: '社保外一般医疗保险金',
        value: '300万保额',
        text: '1.社保内一般医疗保险金、社保外一般医疗保险金共用保险金额300万元<br>2.社保内一般医疗保险金、社保外一般医疗保险金共用免赔额为1万元<br>3.社保外一般医疗保险金的赔偿比例为100%。'
    }, {
        key: '质子重离子医疗保险金',
        value: '600万保额',
        text: '1.质子重离子医疗保险金额为600万<br>2.质子重离子医疗保险金的免赔额为0元。<br>3.质子重离子医疗保险金的赔偿比例为100%。'
    },
]

export const askAnswerList = [
    {
        q: '该产品保证续保吗？',
        a: '本产品保险期限1年，不保证续保。<br>1）保险期间届满或保险期间届满前30日内，投保人需要重新向保险公司申请投保本产品，并经保险人审核同意，交纳保险费，获得新的保险合同。<br>2）本产品续保费率表不同于首次投保的费率表，且续保费率略高。<br>3）本产品不保证投保人在续保时享受首次投保时的费率优惠幅度。'
    }, {
        q: '本产品被保险人的年龄范围是多少呢？',
        a: '本产品被保险人年龄范围是0周岁（出生满30天，含第30天）-70周岁（含）。'
    }, {
        q: '本产品就诊医院是如何规定的呢？',
        a: '本产品医院指中华人民共和国境内合法经营的二级以上（含二级）公立医院的普通部以及保险人扩展承保医院的普通部（均不含国际医疗及特需部)。我司保留新增扩展承保医院的权利；对于新增后的扩展承保医院名单，我司将会在泰康在线官方渠道（包括但不限于官网、官微）公示https://www.tk.cn/service/yljgypqd/yljg/。<br>本产品互联网医院特定药品费用医疗保险金为选购责任，如选购则其约定的特定药品清单一、特定药品清单二和网上药店可通过泰康在线官方网站自助查询，以上内容如有调整，以官方网站披露信息为准。该责任的图文问诊服务入口为保险人的官方APP-我的-保单服务权益。'
    }, {
        q: '购买该产品后，在哪查询保单？',
        a: '本产品采用电子保单形式承保并提供电子发票，您可以通过下列方式查询保单。<br> 途径一：泰康在线官网www.tk.cn→客服中心→保单服务<br> 途径二："泰康在线保险"微信公众号→保单服务→我的保单→点开对应保单→电子保单<br> 如您需要纸质保单请拨打泰康在线保险客服电话95522-3，您需要提供寄送地址以方便我司寄送，相应的快递费用将由您承担。'
    }, {
        q: '购买该产品后，在哪可修改我的个人信息或申请退保？',
        a: '您可以选择如下方式申请信息变更或退保：<b>途径一：通过"泰康在线保险"微信公众号、泰康在线保险 APP 、泰康在线官网（www.tk.cn)，自助办理通信地址、联系电话变更、退保等；<b>途径二：联系在线客服或电话客服95522-3，由客服代为申请。<b>无论自助申请还是通过客服申请，您都可以在"泰康在线保险"微信公众号→保单服务→我的保单→点开对应保单→批改／退保进度，来查询服务的进度。'
    },
]
