<template>
    <van-popup v-model="obj.v1" :close-on-click-overlay="false" class="container_2302231345" round>
        <div class="content" @click="okAction">
            <img src="@/assets/imgs/common/img_yebz.png">
            <div class="button">更换银行卡（{{ countdown }}s）</div>
        </div>
    </van-popup>
</template>

<script>

export default {
    name: "HHRepayPopup",
    props: { obj: Object },
    data() {
        return {
            timerId: null,
            countdown: 5,
        }
    },
    watch: {
        'obj.v1': {
            handler() {
                if (this.obj.v1) {
                    this.startTimer();
                }
            },
        },
    },
    beforeDestroy() {
        this.clearTimer();
    },
    methods: {
        startTimer() {
            this.clearTimer();
            this.countdown = 5;
            this.timerId = setInterval(() => {
                this.countdown--;
                if (this.countdown <= 0) {
                    this.okAction();
                }
            }, 1000);
        },
        clearTimer() {
            this.timerId && clearInterval(this.timerId);
        },
        okAction() {
            this.clearTimer();
            this.obj.v1 = false;
            this.$emit('click');
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2302231345 {
    width: 85%;
    background-color: unset;

    .content {
        position: relative;

        img {
            display: block;
            max-width: 100%;
        }

        .button {
            position: absolute;
            width: 2.50rem;
            height: 0.50rem;
            line-height: 0.50rem;
            border-radius: 999px;
            text-align: center;
            background: linear-gradient(to right, #FF9C53, #FF661A);
            font-size: 0.20rem;
            font-weight: 700;
            color: #FFFFFF;
            bottom: 0.25rem;
            left: 50%;
            transform: translateX(-50%);
        }
    }
}
</style>
