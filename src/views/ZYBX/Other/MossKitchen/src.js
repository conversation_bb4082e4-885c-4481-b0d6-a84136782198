import { isPhoneNum, TraceLogInfoKeys } from "@/assets/js/common";
import { actionTracking } from "@/assets/js/api";
import { bxStorage } from "@/utils/store_util";

export const createOrderInfo = () => {
    const orderInfo = {
        infoKey: 'mosskitchen',
        m: '',
        tel: '',
        mTel: '',
        channel: '1',
        phoneNo: '',
        starPhone: '',
        identifier: '',
    }

    return orderInfo;
}

export const loadOrderInfo = () => {
    return bxStorage.getObjItem('MossKitchen01') || {};
}

export const saveOrderInfo = (orderInfo) => {
    bxStorage.setObjItem('MossKitchen01', orderInfo);
}

export const eventTracking = (orderInfo, name, time = 0,) => {
    const { infoKey, mTel, tel, channel, phoneNo, identifier } = orderInfo;
    const phone = mTel || tel || (isPhoneNum(phoneNo) ? phoneNo : '');

    actionTracking({
        page: `莫士厨房${identifier}(${infoKey})-${name}`,
        mobileId: phone,
        channel: channel,
        infoKey: infoKey,
        time: time,
    }).then(res => {
        const { code, data } = res;
        if (code == 2000) {
            const { mobileId } = data || {};
            mobileId && (orderInfo.mTel = mobileId);
        }
    });
}