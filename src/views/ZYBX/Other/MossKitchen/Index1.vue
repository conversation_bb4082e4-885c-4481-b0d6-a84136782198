<template>
	<div class="container_22101215220" @click="onClick">
		<img src="@/assets/imgs/Other/MossKitchen/img01.png">
	</div>
</template>
<script>

import { fetchStarPhoneV4 } from "@/api/insurance-api"
import { isMaskedAndT1Phone, isPhoneNum } from "@/assets/js/common"
import { eventTracking, createOrderInfo, loadOrderInfo, saveOrderInfo } from "./src"

export default {
	name: "Index1",
	data() {
		const orderInfo = createOrderInfo();

		return {
			orderInfo,
		}
	},
	mounted() {
		const inQry = this.$route.query || {};
		const orderInfo = loadOrderInfo();
		inQry.channel = inQry.channel || orderInfo.channel || "1";

		Object.assign(this.orderInfo, orderInfo, inQry)

		this.orderInfo.identifier = 'MossKitchenIndex1';

		this.fetchPhoneNumber();

		setTimeout(() => {
			this.launchApplet();
		}, 2000);
	},
	methods: {
		fetchPhoneNumber() {
			const { m, phoneNo, starPhone, mTel } = this.orderInfo
			if (!m || isPhoneNum(phoneNo) || isMaskedAndT1Phone(starPhone, mTel)) {
				return this._entryReport()
			}

			const params = { encryptContent: m }
			fetchStarPhoneV4(params).then(res => {
				const { encryptPhone, showPhone } = res.data
				this.orderInfo.mTel = encryptPhone
				this.orderInfo.starPhone = showPhone
				this.orderInfo.phoneNo = showPhone

				saveOrderInfo(this.orderInfo)
			}).finally(() => {
				return this._entryReport()
			});
		},

		onClick() {
			this.launchApplet();
		},

		launchApplet() {
			window.location.href = 'https://d.weimob.com/d/1_11_ponqou';
		},

		_entryReport() {
			const { timing } = window.performance || {}
			const { domContentLoadedEventEnd, fetchStart } = timing || {}
			this._actionTracking("首页", domContentLoadedEventEnd - fetchStart)
		},

		_actionTracking(name, time) {
			eventTracking(this.orderInfo, name, time)
		},
	}
}
</script>

<style lang="less" scoped type="text/less">
	.container_22101215220 {
		background-color: #ffffff;

		img {
			display: block;
			max-width: 100%;
		}
	}
</style>


