<template>
    <div class="box">
        <!-- <div class="peopleNum">累计已有<span class="number">{{ peopleNumber }}</span>人领取</div> -->
        <div v-if="isPhoneAuto" class="box-item">
            <label class="label">手机号码</label>
            <input v-model="phoneFormat" class="input" readonly>
        </div>
        <div v-else class="box-item">
            <label class="label">手机号码</label>
            <input
                v-model="orderInfo.phoneNo"
                placeholder="输入手机号，为您严格保密"
                class="input"
                maxlength="11"
                onkeyup="value=value.replace(/\D/g,'')"
                type="tel"
            >
        </div>
        <slot name="policy"></slot>
        <div id="id_action_button" class="submitButton" @click="submit">
            一键添加微信 免费获得保险方案
            <img alt="" class="hand" src="@/assets/imgs/common/icon_hand1.png">
        </div>
        <div class="notice-box">
            <img alt="" class="laba" src="@/assets/imgs/Other/DBH/laba.png">
            <van-notice-bar :scrollable="false" class="noticeBar">
                <van-swipe :autoplay="2000" :show-indicators="false" vertical class="noticeSwipe">
                    <van-swipe-item v-for="item in noticeList" :key="item">{{ item }}</van-swipe-item>
                </van-swipe>
            </van-notice-bar>
        </div>
    </div>
</template>

<script>
import moment from "moment";
import {isMaskedAndT1Phone,} from "@/assets/js/common";

export default {
    name: "OrderInputBox",
    props: {
        orderInfo: Object
    },
    data() {
        return {
            noticeList: [
                '上海 周* 领取保额200万！',
                '苏州 刘** 领取保额150万！',
                '青岛 马** 领取保额180万！',
                '北京 吴** 领取保额280万！',
                '上海 郑* 领取保额190万！',
                '成都 赵** 领取保额50万！',
                '厦门 曹** 领取保额150万！',
                '杭州 罗** 领取保额230万！',
                '上海 王** 领取保额200万！',
                '重庆 李* 领取保额80万！',
            ],
        };
    },
    mounted() {

    },
    computed: {
        peopleNumber() {
            const date = moment().format('MMDD');
            const seconds = moment().diff(moment().startOf('day'), 'seconds');
            return ((parseInt(date) + seconds) / 20).toFixed(0);
        },
        title() {
            return {text1: '凭手机号', text2: '领取权益'};
        },
        phoneFormat() {
            return this.orderInfo.starPhone;
        },
        isPhoneAuto() {
            const {starPhone, mTel} = this.orderInfo;
            return isMaskedAndT1Phone(starPhone, mTel);
        },
    },
    methods: {
        submit() {
            this.$emit('submit');
        },
    },
}
</script>

<style lang="less" scoped type="text/less">

.box {
    padding: 0.8rem 0.3rem 0.1rem;
    font-size: 0.16rem;
    width: 100%;
    height: 3.81rem;
    position: relative;
    box-sizing: border-box;
    background: url("~@/assets/imgs/Other/DBH/form1.png") no-repeat;
    background-size: contain;

    .box-item {
        margin: 0.50rem auto 0.30rem;
        display: flex;
        align-items: center;
        border-radius: 0.2rem;
        background-color: #f1f3f6;

        .label, .input {
            height: 0.5rem;
            line-height: 0.5rem;
            font-size: 0.16rem;
        }

        .label {
            width: 1rem;
            text-align: center;
        }

        .input {
            flex: 1;
            border: 0;
            outline: none;
            background-color: unset;
        }

        .input-width {
            width: 120px;
        }
    }

    .submitButton {
        position: relative;
        margin: 0.2rem auto 0;
        height: 0.50rem;
        width: 100%;
        line-height: 0.50rem;
        color: #FFFFFF;
        font-size: 0.20rem;
        font-weight: 700;
        text-align: center;
        border-radius: 0.25rem;
        background: linear-gradient(180deg, #FF5B33, #FF2900);
        animation: banner_btn 1.35s linear infinite;

        .hand {
            position: absolute;
            top: 0.30rem;
            left: 75%;
            width: 18%;
            animation: banner_hand 1.00s linear infinite;
        }

        @keyframes banner_btn {
            0% {
                -webkit-transform: scale(1);
                transform: scale(1);
            }
            40% {
                -webkit-transform: scale(1);
                transform: scale(1);
            }
            70% {
                -webkit-transform: scale(.95);
                transform: scale(.95);
            }
            100% {
                -webkit-transform: scale(1);
                transform: scale(1);
            }
        }

        @keyframes banner_hand {
            0% {
                top: 0.10rem;
                left: 70%;
            }
            45% {
                top: 0.25rem;
                left: 75%;
            }
            70% {
                top: 0.25rem;
                left: 75%;
            }
            100% {
                top: 0.10rem;
                left: 70%;
            }
        }

    }

    .notice-box {
        position: absolute;
        bottom: 0.30rem;
        left: 0.30rem;
        display: flex;
        align-items: center;

        .laba {
            height: 0.24rem;
            margin-right: 0.05rem;
        }
    }

    .noticeBar {
        background: transparent;

        width: 1.70rem;
        padding: 0;
        opacity: 0.90;

        .noticeSwipe {
            font-size: 0.13rem;
            height: 0.30rem;
            line-height: 0.30rem;
        }
    }
}

.read-box {
    display: flex;
    margin-left: 0.12rem;
    margin-top: 0.25rem;

    .text {
        font-size: 0.15rem;
        color: #464646;
        line-height: 0.20rem;
        text-align: justify;
    }

    .read-text {
        color: #246DE2;
        font-weight: 500;
    }

    .svg-icon {
        font-size: 0.18rem;
        line-height: 0.20rem;
        margin-right: 0.05rem;
        stroke-width: 100;
        fill: rgb(255, 69, 9);
    }
}
</style>
