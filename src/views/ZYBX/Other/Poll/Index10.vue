<template>
	<div class="container_2210191500">
		<!-- 平安赠险按城市轮询 -->
	</div>
</template>

<script>

import { bxStorage, } from "@/utils/store_util";

export default {
	name: "SendIndex10",
	created() {
		this.init();
	},
	methods: {
		init() {
			const { sfdm = '', csdm = '', } = this.$route.query || {};
			const PATH_DEFAULT = {
				1: [{
					path: 'https://channel.rrbxw.cn/mktprod/rp/ZYBX/IYBPAZX03/Index14',
					prob: 100,
					forbidden_city: [],
					forbidden_province: [],
				}],
			} // 默认产品路径
			// let path = bxStorage.getRawItem('IYBPASendIndex5') || '';
			let path = ''
			const script = document.createElement('script');
			script.type = 'text/javascript';
			script.src = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/config/IYBPASendByCity_4.js';
			document.getElementsByTagName('head')[0].appendChild(script);
			script.onload = () => {
				try {
					const productObj = send_random || PATH_DEFAULT;
					let levels
					if (!sfdm && !csdm && productObj.default) {
						levels = ['default']
					} else {
						levels = Object.keys(productObj).filter(item => item != 'default').sort((a, b) => a - b)
					}
					for (let i = 0; i < levels.length; i++) {
						const list = productObj[levels[i]]
						if (Object.prototype.toString.call(list) != '[object Array]') {
							continue;
						}
						let sum = 0
						for (let k = 0; k < list.length; k++) {
							const item = list[k]
							const cities = item.forbidden_city.map(item => item.code)
							const provinces = item.forbidden_province.map(item => item.code)
							if (!(csdm && cities.includes(csdm)) && !(sfdm && provinces.includes(sfdm))) {
								if (path && item.path == path) {
									return this.pushToTarget(path)
								}
								sum += (item.prob || 0)
								list[k].prob = sum
							} else {
								list[k].prob = -1
							}
						}
						const randomVal = Math.random() * sum;
						for (let j = 0; j < list.length; j++) {
							if (randomVal < list[j].prob) {
								return this.pushToTarget(list[j].path);
							}
						}
					}

					const obj = productObj.default || PATH_DEFAULT[1];
					this.pushToTarget(obj[0].path);
				} catch (err) {
					this.pushToTarget(PATH_DEFAULT[1][0].path);
				}
			}
			script.onerror = () => {
				this.pushToTarget(PATH_DEFAULT[1][0].path);
			}
		},
		pushToTarget(path) {
			const query = window.location.href.split('?')[1] || '';
            const totalPath = path.indexOf('?') > 0 ? `${path}&${query}` : `${path}?${query}`;
			bxStorage.setRawItem('IYBPASendIndex5', path);
			window.location.href = totalPath;
		},
	}
}
</script>

<style lang="less" scoped type="text/less">
	.container_2210191500 {
		width: 100%;
		height: 100%;
		background-color: #ffffff;
	}
</style>


