<template>
  <div class="test-link">
    <div class="header">
      <h3>域名测试列表</h3>
      <!-- <span class="edit" @click="showEdit(-1)">新增</span> -->
    </div>
    <div class="video-box">
      <video id="video" :autoplay="true" class="player" playsinline :muted="true" webkit-playsinline x5-playsinline preload
        x5-video-player-fullscreen="false" x5-video-player-type="h5">
        <source
          src="https://cdns.bountech.com/marketfront/file/insurance/cdn/picture/TaiKang/TK01/video.mp4"
          type="video/mp4" />
      </video>
    </div>
    <div class="desc">
      <li><span class="testing dot"></span>&nbsp;<span>测试中</span></li>
      <li><span class="tested dot"></span>&nbsp;<span>测试完成</span></li>
      <li><span class="not-test dot"></span>&nbsp;<span>待测试</span></li>
    </div>
    <van-cell-group>
      <van-cell
        v-for="(item, index) in links"
        :key="item.url"
        :title="item.name"
      >
        <template slot="label">
          <div class="label"><span class="link">{{item.url}}</span></div>
        </template>
        <span v-if="index == curIndex" class="testing dot"></span>
        <span v-else-if="index < curIndex" class="tested dot"></span>
        <span v-else class="not-test dot"></span>
      </van-cell>
    </van-cell-group>
    <van-button type="primary" @click="closeTest" :disabled="closeStatus">{{closeStatus ? '已关闭' : '关闭监控'}}</van-button>
  </div>
</template>

<script>
import '@/assets/icons/edit.svg';
import {fetchMonitorPageList, startMonitor, updateMonitor} from '@/api/insurance-api'
// let environment = process.env.VUE_APP_TITLE
// const ksPrefix = ['prod','production','iybprod'].includes(environment)?'https://store.kaisenbaoxian.com/marketfront/insurancecdn':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
// // const iybPrefix = ['prod','production','iybprod'].includes(environment)?'https://szrp.baoinsurance.com/marketfront/insurancecdn':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
// const iybPrefix = ['prod','production'].includes(environment)?'https://channel.zhelibao.com/mktprod/rp': ['iybprod'].includes(environment)?'https://channel.zhelibao.com/mktprod/rp':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
// const zaxdPrefix = 'http://**************:8080/marketfront/finance'
// const iybPrefix1 = ['prod','production'].includes(environment)?'https://channel.zhelibao.com/mktprod/rp':['iybprod'].includes(environment)?'https://channel.zhelibao.com/mktprod/rp':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
// const nwPrefix = ['prod','production','iybprod',].includes(environment)?'https://sr0lLef8bo_5zb19brqip8t-3rz_7c0.aibangbaoxian.net/marketfront/insurancecdn':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
// const zaybPrefix = ['prod','production','iybprod',].includes(environment)?'https://oiisf.zabxib.com/marketfront/insurancecdn':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
// const zyPrefix = ['prod','production','iybprod',].includes(environment)?'https://zy.bountech.com/marketfront/insurancecdn':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
// const tcjjPrefix = ['prod','production','iybprod',].includes(environment)?'https://js-bprod.tiancaibaoxian.com/marketfront/insurancecdn':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
// const bbjPrefix = ['prod','production','iybprod',].includes(environment)?'https://tf.bbinsure.cn/marketfront/insurancecdn':(['alpha'].includes(environment)?'http://uat.bountech.com/marketfront/insurance':`${window.location.protocol}//${window.location.host}/marketfront/insurance`)
export default {
    name: 'TestLInk',
    components: {},
    data() {
        return {
            id: 0,
            links: [],
            curIndex: 0,
            closeStatus: false,
        }
    },
    mounted() {
      this.init()
    },
    computed: {
      percent() {
        return (this.curIndex + (this.curTime -1) * (this.result.length ||1))/this.allTimes/(this.result.length ||1)
      }
    },
    methods: {
      init() {
        const query = this.$route.query || {};
        this.curIndex = parseInt(query && query.curIndex) || 0;
        this.getPageList()
      },
      getPageList() {
        fetchMonitorPageList().then(res => {
          const {data, code} = res.data;
          if (code =='2000') {
            this.links = data
            if (this.curIndex == this.links.length) {
              this.curIndex = 0
            }
            this.monitor(this.links[this.curIndex].url)
          } else {
            this.$toast('监控列表获取错误')
          }
        }).catch(() => {
          this.$toast('监控列表获取错误')
        })
      },
      monitor(url) {
        startMonitor({url}).then(res => {
          const {data, code} = res.data;
          if (code =='2000') {
            this.id = data
            setTimeout(() => {
              if (!this.closeStatus) {
                window.location.href = this.links[this.curIndex].url + `?id=${this.id}&curIndex=${this.curIndex}&t=${Date.now()}`
              }
            }, 60000)
          } else {
            this.$toast('开始测试接口错误')
          }
        }).catch(() => {
          this.$toast('开始测试接口错误')
          this.$toast('开始测试接口错误')
        })
      },
      closeTest() {
        if (this.id) {
          const params = {
            id: this.id,
            url: this.links[this.curIndex].url,
            status: '成功'
          }
          updateMonitor(params).then(res => {
              if (res.data.code != '2000') {
                this.$toast('接口出错')
              }
          }).catch(() => {
            this.$toast('接口出错')
          });
        }
        this.closeStatus = true
        this.$toast('关闭成功')
      }
    },
}
</script>

<style lang="less" scoped type="text/less">

.test-link {
    width: 100%;
    height: 100%;
    padding: 0.4rem 0;
    background-color: #FFFFFF;
    .video-box {
      width: 100%;
      height: 2.2rem;
      #video {
        width: 100%;
      }
    }
    .header {
      width: 100%;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      padding: 0.25rem 0.2rem;
      h3 {
        font-size: 0.22rem;
        height: 0.25rem;
        line-height: 0.25rem;
      }
    }
    .label {
      width: 3rem;
      display: flex;
      .link {
        max-width: 2.7rem;
        font-size: 0.16rem;
        height: 0.16rem;
        line-height: 0.16rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .desc {
      margin: 0.2rem 0;
      font-size: 0.16rem;
      display: flex;
      justify-content: space-around;
      list-style: none;
    }
    .dot {
      display: inline-block;
      width: 0.12rem;
      height: 0.12rem;
      border-radius: 50%;
    }
    .testing {
      background-color: #00CCFF
    }
    .tested {
      background-color: #00FF33;
    }
    .not-test {
      background-color: #e0e0e0;
    }
    .van-button {
      width: 80%;
      margin: auto 10%;
      position: fixed;
      bottom: 0.4rem;
      border-radius: 1rem;
    }
}
</style>
