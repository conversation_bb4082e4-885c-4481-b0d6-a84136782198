<template>
    <div class="container_2212281550"></div>
</template>

<script>
import {Dialog, Toast} from "vant";
import {isHuaWeiBrowser} from "@/assets/js/common";
import {createPayOrder, fetchOrderPayStatus} from "@/api/insurance-api";

export default {
    name: "Index1",
    data() {
        return {
            orderNo: '',
            fromPay: 0,
            toastNode: null,
        }
    },

    mounted() {
        const {orderNo, scene} = this.$route.query || {};
        this.orderNo = orderNo || '';
        this.fromPay = scene == 'fromPay';

        if (this.fromPay) {
            this.toastNode = this.$toast.loading({
                message: '结果查询中\n请稍等...',
                forbidClick: true,
                duration: 0,
            });
            this.fetchPayStatus(3, 0).then(res => {
                this.toastNode.clear();

                if (res.status != 1) {
                    this.dialogShow();
                }
            });
        } else {
            this.fetchPayStatus(1, 0).then(res => {
                if (res.status != 1) {
                    if (isHuaWeiBrowser()) {
                        this.dialogShow({
                            title: '订单支付',
                            message: '请及时完成支付',
                            confirmButtonText: '去支付',
                            hideCancelButton: true
                        });
                    } else {
                        this.createPayOrder();
                    }
                }
            });
        }
    },

    methods: {
        fetchPayStatus(max, n) {
            return this.fetchAction().then(res => {
                if (res.status == 1) {
                    return Promise.resolve(res);
                }

                n++;

                if (n >= max) {
                    return Promise.resolve(res);
                }

                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve(this.fetchPayStatus(max, n));
                    }, 2000);
                });
            });
        },

        fetchAction() {
            return fetchOrderPayStatus(this.orderNo).then(res => {
                const {code, data} = res.data;
                if (code != 2000) {
                    return Promise.resolve({status: 0});
                }

                const {status, failureUrl, successUrl} = data || {};
                if (status == 1) {
                    window.location.href = successUrl;
                }
                return Promise.resolve({status: status || 0});
            }).catch(err => {
                return Promise.resolve({status: 0});
            });
        },

        createPayOrder() {
            const params = {
                orderNo: this.orderNo,
            }
            createPayOrder(params).then(res => {
                const {code, data, msg} = res.data;
                if (code != 2000) {
                    return this.toastShow(msg || '生成支付订单失败');
                }

                const payUrl = data.h5url;
                window.location.href = payUrl;
            });
        },

        dialogShow(obj = {}) {
            Dialog.confirm({
                title: obj.title || '支付结果',
                message: obj.message || '请确认是否已完成支付',
                confirmButtonText: obj.confirmButtonText || '已完成支付',
                cancelButtonText: obj.cancelButtonText || '去支付',
                showCancelButton: !obj.hideCancelButton,
            }).then(() => {
                this.createPayOrder();
            }).catch(() => {
                this.createPayOrder();
            });
        },

        toastShow(message, duration) {
            Toast({
                forbidClick: true,
                message: message || '',
                duration: duration || 2000, // 弹窗时间毫秒
            });
        }
    },
}
</script>

<style lang="less" scoped>

.container_2212281550 {
    min-height: 100%;
    font-size: 0.15rem;
    background-color: #F2F2F2;

}

</style>
