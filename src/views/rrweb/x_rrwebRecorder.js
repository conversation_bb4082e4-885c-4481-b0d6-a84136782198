// https://github.com/rrweb-io/rrweb
const base_url_dev = 'http://uat.bountech.com/market/';
const base_url_pro = 'http://prod.bountech.com/marketprod/';

let sdkpromise = null;
let uuidpromise = null;
let allEvents = [];
let timestamp = 0;
let base_url = base_url_dev;

const webStorage = {
    save(key, value) {
        try {
            window.localStorage.setItem(key, value);
        } catch (error) {

        }
    },

    load(key) {
        let value = null;
        try {
            value = window.localStorage.getItem(key);
        } catch (error) {

        }
        return value;
    },
}

const createUUID = () => {
    if (uuidpromise) {
        return uuidpromise;
    }

    return uuidpromise = new Promise((resolve) => {
        const rrweb_uuid = webStorage.load('rrweb_uuid');
        if (rrweb_uuid) {
            window.rrweb_uuid = rrweb_uuid;
            return resolve(rrweb_uuid);
        }

        const timestamp = Date.now().toString(16);
        const randomcode = 'xxxyyxxx-yyxyy-xxxxxyyxxxxx'.replace(/[xy]/g, (c) => {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });

        const uuid = timestamp + '-' + randomcode;
        window.rrweb_uuid = uuid;
        webStorage.save('rrweb_uuid', uuid);

        return resolve(uuid);
    });
}

const injectSDK = () => {
    if (sdkpromise) {
        return sdkpromise;
    }

    return sdkpromise = new Promise((resolve, reject) => {
        const isExist = document.querySelector('#id_2311161000');
        if (isExist) {
            return resolve('回溯SDK加载成功');
        }

        try {
            const scriptURL = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/sdk/rrweb/rrweb-record.min.js';
            const jsNode = document.createElement('script');
            jsNode.id = 'id_2311161000';
            jsNode.src = scriptURL;
            jsNode.onload = () => {
                return resolve('回溯SDK加载成功');
            };

            jsNode.onerror = () => {
                return reject('回溯SDK加载失败');
            };

            document.body.appendChild(jsNode);
        } catch (e) {
            return reject('回溯SDK加载失败');
        }
    });
}

const startRecord = (immediate) => {
    if (!window.rrwebRecord) {
        console.log('回溯SDK未初始化');
        return;
    }

    window.rrwebRecord({
        emit(event) {
            // status: 0-待传/传失败 1-正在传 2-传成功
            event.status = 0;
            allEvents.push(event);
        },
    });

    immediate && uploadRecordVideo();
}

const uploadRecordVideo = () => {
    requestAnimationFrame(() => {
        if (Date.now() - timestamp < 3000 || allEvents.length <= 0) {
            return uploadRecordVideo();
        }

        allEvents = allEvents.filter(v => v.status != 2); // 删除已上传成功的事件
        const events = allEvents.filter(v => {
            const isWait = v.status == 0;
            isWait && (v.status = 1);
            return isWait;
        }); // 待上传事件

        if (events.length <= 0) {
            return uploadRecordVideo();
        }

        const param = {
            mobileId: window.rrweb_mobile,
            uuid: window.rrweb_uuid,
            events: JSON.stringify(events),
        }

        const xhr = new XMLHttpRequest();
        xhr.open('POST', base_url + 'Insurance/trace-back/data-upload', true);
        xhr.setRequestHeader('Content-Type', 'application/json');  // 设置请求头
        xhr.onload = () => {
            const success = xhr.response.success;
            if (success) {
                return events.forEach(v => v.status = 2);
            }
            return events.forEach(v => v.status = 0);
        }

        xhr.onerror = (err) => {
            events.forEach(v => v.status = 0);
        }

        xhr.send(JSON.stringify(param));

        timestamp = Date.now();

        uploadRecordVideo();
    });
}

const init = (isProduction, immediate) => {
    base_url = isProduction ? base_url_pro : base_url_dev;

    return Promise.all([createUUID(), injectSDK()]).then(r => {
        startRecord(immediate);
        return r[0];
    });
}

const setMobile = (mobile) => {
    window.rrweb_mobile = mobile;
}

export default {
    init,
    setMobile,
    uploadRecordVideo,
}
