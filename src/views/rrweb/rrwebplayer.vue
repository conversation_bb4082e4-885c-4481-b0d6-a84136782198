<template>
    <div class="container_2311171030">
        <div class="header">
            <p class="title">前端页面回溯播放</p>
            <div class="btn-wrapper">
                <div v-for="(v, idx) in timeEventList" :key="v.time" class="btn" :class="{ active: activeIdx == idx }"
                    @click="onPlayerClick(idx)">{{ v.time }}</div>
            </div>
        </div>
        <div id="id_2311171345" class="player"></div>
    </div>
</template>
<script>

import moment from 'moment'
import { traceBackGetByUuid } from '@/api/rrweb_api'

export default {
    name: "RRWebPlayer",
    data() {
        return {
            query: {
                uuid: '',
                mobileId: '',
            },
            rrwebPlayer: null,
            timeEventList: [],
            activeIdx: -1,
        }
    },
    created() {
        const query = this.$route.query;   // uuid,mobileId
        this.query.uuid = query.uuid;
        this.query.mobileId = query.mobileId;

        this.fetchEventData()
    },
    mounted() {
        !function () {
            const isExist = document.querySelector('#id_2311161618');
            if (!isExist) {
                const scriptURL = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/sdk/rrweb/rrweb-player.min.js';
                const jsNode = document.createElement('script');
                jsNode.id = 'id_2311161618';
                jsNode.src = scriptURL;

                document.body.appendChild(jsNode);
            }
        }();

        !function () {
            const isExist = document.querySelector('#id_2311161620');
            if (!isExist) {
                const scriptURL = 'http://html2canvas.hertzen.com/dist/html2canvas.js';
                const jsNode = document.createElement('script');
                jsNode.id = 'id_2311161620';
                jsNode.src = scriptURL;

                document.body.appendChild(jsNode);
            }
        }();

        !function () {
            const isExist = document.querySelector('#id_2311161636');
            if (!isExist) {
                const linkURL = 'https://cdns.bountech.com/marketfront/file/insurance/cdn/sdk/rrweb/rrweb-player.min.css';
                const linkNode = document.createElement('link');
                linkNode.id = 'id_2311161636';
                linkNode.rel = 'stylesheet';
                linkNode.href = linkURL;

                document.head.appendChild(linkNode);
            }
        }();
    },
    methods: {
        onPlayerClick(playIdx) {
            this.activeIdx = playIdx;

            const rr_players = document.querySelectorAll('.rr-player');
            for (const v of rr_players) {
                v.remove();
            }

            const player_node = document.querySelector('#id_2311171345');

            this.rrwebPlayer = new rrwebPlayer({
                target: player_node, // 可以自定义 DOM 元素
                props: {
                    events: this.timeEventList[playIdx].events,
                },
            });

            this.$nextTick(() => {
                const rr_player = document.querySelector('.rr-player');
                rr_player.style.width = '100%';

                const rr_player__frame = document.querySelector('.rr-player__frame');
                rr_player__frame.style.width = '100%';
            });
        },

        fetchEventData() {
            const { uuid, mobileId } = this.query;
            if (!mobileId && !uuid) {
                return this.$toast({
                    message: '链接缺少参数，请调整后重试',
                    duration: 2000,
                });
            }

            this.$toast.loading({
                message: '数据请求中\n请稍候',
                forbidClick: true,
                duration: 0,
            });

            traceBackGetByUuid(uuid).then(r => {
                this.$toast.clear(true);

                this.eventDataHandle(r);
            }).catch(err => {
                this.$toast({
                    message: '查询出错',
                    duration: 2000,
                });
            });
        },

        eventDataHandle(r) {
            this.timeEventList = [];


            const events = r.data || [];
            if (events.length <= 0) {
                return this.$toast({
                    message: '查无数据',
                    duration: 2000,
                });
            }

            const eventList = events.reduce((prev, v) => {
                prev = prev.concat(JSON.parse(v.events));
                return prev;
            }, []);

            eventList.sort((a, b) => {
                return a.timestamp - b.timestamp;
            });

            this.timeEventList = eventList.reduce((events, v) => {
                if (v.type == 4) {
                    const obj = { time: moment(v.timestamp).format('YYYY-MM-DD HH:mm:ss'), events: [v] };
                    events.push(obj);
                } else if (events.length > 0) {
                    const lastItem = events[events.length - 1];
                    lastItem.events.push(v);
                }
                return events;
            }, []);

            this.timeEventList.unshift({ time: '播放全部', events: eventList });
        },
    },
}
</script>

<style lang="less" type="text/less">
.container_2311171030 {
    min-height: 100vh;

    .header {
        .title {
            padding: 0.2rem 0 0;
            font-size: ~"clamp(15px,0.15rem,25px)";
            font-weight: 700;
            text-align: center;
        }

        .btn-wrapper {
            padding: 0.2rem 0.1rem 0.15rem;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-row-gap: 0.1rem;
            grid-column-gap: 0.1rem;

            .btn {
                padding: 0.05rem 0;
                font-size: ~"clamp(12px,0.10rem,16px)";
                text-align: center;
                cursor: pointer;
                border-radius: 0.05rem;
                border: 1px solid #0b78ff;
                background-color: #fff;

                &.active {
                    background-color: #0b78ff;
                    color: #fff;
                }
            }
        }
    }

    .rr-player {
        font-size: 0.1rem;

        .svelte-19ke1iv {
            font-size: 0.1rem;
        }

        .svelte-9brlez {
            font-size: 0.1rem;
        }
    }
}
</style>
