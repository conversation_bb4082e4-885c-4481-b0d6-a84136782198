<template>
    <div class="contianer_2311170900">
        <p>滚滚长江东逝水，浪花淘尽英雄。是非成败转头空。青山依旧在，几度夕阳红。白发渔樵江渚上，惯看秋月春风。一壶浊酒喜相逢。古今多少事，都付笑谈中。</p>
        <p>滚滚长江向东流，不再回头，多少英雄像翻飞的浪花般消逝。争什么是与非、成功与失败，都是短暂不长久。只有青山依然存在，依然的日升日落。江上白发渔翁，早已习惯于四时的变化。和朋友难得见了面，痛快的畅饮一杯酒。古往今来的纷纷扰扰，都成为下酒闲谈的材料。
        </p>
        <div class="item">
            <label>输入框</label>
            <input />
        </div>
        <button class="button" @click="onUploadClick">上传</button>
    </div>
</template>

<script>
import x_rrweb from './x_rrwebRecorder1';
export default {
    mounted() {
        x_rrweb.init(false).then(uuid => {
            console.log(uuid);
        });

        x_rrweb.setMobile('19999999999')
    },

    methods: {
        onUploadClick() {
            x_rrweb.uploadRecordVideo();
        },
    }
}
</script>

<style lang="less" scoped>
.contianer_2311170900 {
    font-size: 0.15rem;
    line-height: 1.5;

    p {
        text-align: justify;
        text-indent: 2em;
    }

    img {
        display: block;
        max-width: 100%;
        margin: 0 auto;
    }

    input {
        border: 1px solid #888;
        inset: none;
        outline: none;
        border-radius: 0.05rem;
        line-height: 2;
    }

    input:focus {
        border: 2px solid blue;
    }

    label {
        padding: 0 0.1rem;
    }

    .item {
        margin: 0.2rem 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    button {
        margin: 0.15rem auto 0;
        padding: 0.1rem 0;
        display: block;
        width: 2rem;
        border: none;
        border-radius: 0.06rem;
        background-color: lightblue;
    }
}
</style>