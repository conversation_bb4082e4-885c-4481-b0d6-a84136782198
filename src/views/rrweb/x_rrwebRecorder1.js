// https://github.com/rrweb-io/rrweb
import { pack, record } from 'rrweb';
import { compressToBase64 } from 'lz-string';
import { traceBackUpload } from '@/api/rrweb_api';

// 全局变量
let recordedEvents = [];
let lastUploadTimestamp = 0;

/**
 * 本地存储工具类
 */
const localStorageManager = {
    /**
     * 设置本地存储值
     * @param {string} key 存储键
     * @param {string} value 存储值
     */
    setItem(key, value) {
        try {
            window.localStorage.setItem(key, value);
        } catch (error) {
            console.warn('localStorage setItem failed:', error);
        }
    },

    /**
     * 获取本地存储值
     * @param {string} key 存储键
     * @returns {string|null} 存储值
     */
    getItem(key) {
        let value = null;
        try {
            value = window.localStorage.getItem(key);
        } catch (error) {
            console.warn('localStorage getItem failed:', error);
        }
        return value;
    },
}

/**
 * 生成或获取录屏会话UUID
 * @returns {Promise<string>} UUID字符串
 */
const generateOrGetSessionUUID = () => {
    const existingUUID = localStorageManager.getItem('rrweb_uuid');
    if (existingUUID) {
        window.rrweb_uuid = existingUUID;
        return Promise.resolve(existingUUID);
    }

    return new Promise((resolve) => {
        const timestampHex = Date.now().toString(16);
        const randomPattern = 'xxxyyxxx-yyxyy-xxxxxyyxxxxx'.replace(/[xy]/g, (char) => {
            const randomValue = Math.random() * 16 | 0;
            const hexValue = char === 'x' ? randomValue : (randomValue & 0x3 | 0x8);
            return hexValue.toString(16);
        });

        const sessionUUID = `${timestampHex}-${randomPattern}`;
        window.rrweb_uuid = sessionUUID;
        localStorageManager.setItem('rrweb_uuid', sessionUUID);

        resolve(sessionUUID);
    });
}

/**
 * 开始录制屏幕操作
 * @param {Object} options 录制选项
 * @param {boolean} options.immediate 是否立即上传
 * @param {string} options.page 页面标识
 */
const startScreenRecording = ({ immediate = false, page = '' }) => {
    record({
        emit(event) {
            // 事件状态: 0-待上传/上传失败, 1-正在上传, 2-上传成功
            event.status = 0;
            event.page = page;
            recordedEvents.push(event);
        },
    });

    // 如果需要立即上传，则启动上传流程
    if (immediate) {
        scheduleEventUpload();
    }
}

/**
 * 调度事件上传任务
 * 使用requestAnimationFrame确保不阻塞主线程
 */
const scheduleEventUpload = () => {
    requestAnimationFrame(() => {
        // 检查上传间隔和事件数量
        const currentTime = Date.now();
        const timeSinceLastUpload = currentTime - lastUploadTimestamp;
        const hasEventsToUpload = recordedEvents.length > 0;

        if (timeSinceLastUpload < 6000 || !hasEventsToUpload) {
            return scheduleEventUpload();
        }

        // 清理已成功上传的事件
        recordedEvents = recordedEvents.filter(event => event.status !== 2);

        // 获取待上传的事件并标记为上传中
        const pendingEvents = recordedEvents.filter(event => {
            const isPending = event.status === 0;
            if (isPending) {
                event.status = 1; // 标记为上传中
            }
            return isPending;
        });

        if (pendingEvents.length === 0) {
            return scheduleEventUpload();
        }

        // 准备上传数据
        const eventsData = JSON.stringify(pendingEvents);
        const uploadParams = {
            // mobileId: '13333333333', // TODO: 使用实际的手机号
            uuid: window.rrweb_uuid,
            events: eventsData,
        };

        // 更新上传时间戳
        lastUploadTimestamp = currentTime;

        // 执行上传
        performEventUpload(uploadParams, pendingEvents);

        // 继续调度下一次上传
        scheduleEventUpload();
    });
}

/**
 * 执行事件上传
 * @param {Object} uploadParams 上传参数
 * @param {Array} events 待上传的事件列表
 */
const performEventUpload = (uploadParams, events) => {
    traceBackUpload(uploadParams)
        .then(response => {
            const isSuccess = response.data.success;
            // 根据上传结果更新事件状态
            events.forEach(event => {
                event.status = isSuccess ? 2 : 0; // 2-成功, 0-失败重试
            });
        })
        .catch(error => {
            console.error('事件上传失败:', error);
            // 上传失败，重置状态以便重试
            events.forEach(event => {
                event.status = 0;
            });
        });
}

/**
 * 初始化录屏功能
 * @param {Object} options 初始化选项
 * @param {boolean} options.immediate 是否立即开始上传
 * @param {string} options.page 页面标识
 * @returns {Promise<string>} 返回会话UUID
 */
const initializeRecorder = async (options) => {
    try {
        const sessionUUID = await generateOrGetSessionUUID();
        startScreenRecording(options);
        return sessionUUID;
    } catch (error) {
        console.error('录屏初始化失败:', error);
        throw error;
    }
}

/**
 * 手动触发事件上传
 * 提供给外部调用的接口
 */
const triggerEventUpload = () => {
    scheduleEventUpload();
}

// 导出公共接口
export default {
    // 初始化录屏功能
    initialize: initializeRecorder,
    // 手动触发上传
    upload: triggerEventUpload,
}
