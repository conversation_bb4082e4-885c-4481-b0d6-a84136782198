// https://github.com/rrweb-io/rrweb
import { pack, record } from 'rrweb';
import { compressToBase64 } from 'lz-string';
import { traceBackUpload } from '@/api/rrweb_api';

let allEvents = [];
let timestamp = 0;

const webStorage = {
    set(key, value) {
        try {
            window.localStorage.setItem(key, value);
        } catch (error) {

        }
    },

    get(key) {
        let value = null;
        try {
            value = window.localStorage.getItem(key);
        } catch (error) {

        }
        return value;
    },
}

const createUUID = () => {
    const rrweb_uuid = webStorage.get('rrweb_uuid');
    if (rrweb_uuid) {
        window.rrweb_uuid = rrweb_uuid;
        return Promise.resolve(rrweb_uuid);
    }

    return new Promise((resolve) => {
        const timestamp = Date.now().toString(16);
        const randomcode = 'xxxyyxxx-yyxyy-xxxxxyyxxxxx'.replace(/[xy]/g, (c) => {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
        });

        const uuid = timestamp + '-' + randomcode;
        window.rrweb_uuid = uuid;
        webStorage.set('rrweb_uuid', uuid);

        return resolve(uuid);
    });
}

const startRecord = (immediate) => {
    record({
        emit(event) {
            // status: 0-待传/传失败 1-正在传 2-传成功
            event.status = 0;
            allEvents.push(event);
        },
    });

    immediate && uploadRecordVideo();
}

const uploadRecordVideo = () => {
    requestAnimationFrame(() => {
        if (Date.now() - timestamp < 3000 || allEvents.length <= 0) {
            return uploadRecordVideo();
        }

        allEvents = allEvents.filter(v => v.status != 2); // 删除已上传成功的事件
        const events = allEvents.filter(v => {
            const isWait = v.status == 0;
            isWait && (v.status = 1);
            return isWait;
        }); // 待上传事件

        if (events.length <= 0) {
            return uploadRecordVideo();
        }

        const compressedEvents = compressToBase64(JSON.stringify(events));
        const params = {
            mobileId: window.rrweb_mobile,
            uuid: window.rrweb_uuid,
            events: compressedEvents,
        }

        timestamp = Date.now();
        traceBackUpload(params).then(res => {
            const success = res.data.success;
            if (success) {
                return events.forEach(v => v.status = 2);
            }
            return events.forEach(v => v.status = 0);
        }).catch(err => {
            events.forEach(v => v.status = 0);
        });

        uploadRecordVideo();
    });
}

const init = async (immediate) => {
    const r = await createUUID();
    startRecord(immediate);
    return r;
}

const setMobile = (mobile) => {
    window.rrweb_mobile = mobile;
}

export default {
    init,
    setMobile,
    uploadRecordVideo,
}
