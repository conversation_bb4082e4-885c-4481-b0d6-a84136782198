<template>
    <div class="box">
        <!--标题栏-->
        <div class="header">
            <div class="title">
                检测麦克风
            </div>
            <div class="subtitle">
                {{ version }} - {{ datetime }}
            </div>
        </div>

        <!--按钮栏-->
        <div class="button-box">
            <span>关闭麦克风</span>
            <van-switch
                size="24px"
                :value="enabled"
                :loading="status===statusEnum.pending"
                @input="onClickSwitch"
            />
            <span>打开麦克风</span>
        </div>

        <!--状态栏-->
        <div class="status-box">
            <div class="status-item">
                <div class="status-title">
                    麦克风状态
                </div>
                <div class="status-content">
                    <van-tag v-if="status===statusEnum.closed" size="large" type="warning">已关闭</van-tag>
                    <van-tag v-else-if="status===statusEnum.open" size="large" type="success">已打开</van-tag>
                    <van-tag v-else-if="status===statusEnum.pending" size="large">加载中</van-tag>
                    <van-tag v-else size="large">{{ status }}</van-tag>
                </div>
            </div>
            <div class="status-item">
                <div class="status-title">
                    是否支持WebRTC（RTCPeerConnection）
                </div>
                <div class="status-content">
                    <van-tag v-if="isSupportWebRTC" size="large" type="success">支持</van-tag>
                    <van-tag v-else size="large" type="danger">不支持</van-tag>
                </div>
            </div>
            <div class="status-item">
                <div class="status-title">
                    媒体设备列表
                </div>
                <div class="status-content">
                    <div v-if="deviceList.length">
                        <div v-for="(deviceItem, deviceIndex) in deviceList" :key="deviceIndex">
                            {{ (deviceIndex + 1) + '. ' + (deviceItem.label || '未知') }}
                        </div>
                    </div>
                    <div v-else>（空）</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
const statusEnum = {
    pending: '加载中',
    open: '已打开',
    closed: '已关闭',
};

export default {
    name: "CheckMicrophone",
    data() {
        return {
            version: 'v1.2.0', // 版本号
            datetime: '20250710', // 日期时间
            enabled: false, // 麦克风启用状态（开关状态）
            statusEnum, // 麦克风状态枚举
            status: statusEnum.closed, // 麦克风当前实时状态
            deviceList: [], // 媒体设备列表
            stream: null, // 麦克风媒体流
        };
    },
    computed: {
        // 是否支持WebRTC
        isSupportWebRTC() {
            return !!window.RTCPeerConnection;
        },
    },
    methods: {
        /**
         * 获取媒体设备列表
         */
        async enumMediaDevices() {
            try {
                const list = await navigator.mediaDevices.enumerateDevices();
                this.deviceList = Array.isArray(list) ? list : [];
            } catch (e) {
                // alert('获取媒体设备列表时出错，请重试。');
            }
        },

        async action(func, enabled = false) {
            try {
                if (func && typeof func === 'function') {
                    await func();
                    this.enabled = enabled;
                    this.status = enabled ? statusEnum.open : statusEnum.closed;
                }
            } catch (e) {
                this.enabled = !enabled;
                this.status = enabled ? statusEnum.closed : statusEnum.open;
                if (e.name === 'NotAllowedError') {
                    alert('请授予麦克风访问权限。');
                } else if (e.name === 'NotFoundError') {
                    alert('未找到麦克风设备，请检查设备连接。');
                } else {
                    alert('操作出错，请重试。' + e.toString());
                }
            }
        },

        /**
         * 关闭麦克风
         */
        closeMicrophone() {
            this.action(() => {
                this.stream.getTracks().forEach((track) => {
                    track.stop && track.stop();
                });
            }, false);
        },

        /**
         * 打开麦克风
         */
        openMicrophone() {
            this.action(async () => {
                this.stream = await navigator.mediaDevices.getUserMedia({
                    audio: true,
                    video: false,
                });
                await this.enumMediaDevices();
            }, true);
        },

        /**
         * 切换开关
         * @param enabled 切换后的启用状态
         */
        async onClickSwitch(enabled) {
            this.status = statusEnum.pending;
            await new Promise(resolve => {
                setTimeout(() => {
                    resolve(true);
                }, 200);
            });
            enabled ? this.openMicrophone() : this.closeMicrophone();
        },
    }
};
</script>

<style scoped lang="less">
.box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    font-size: 16px;
}

/*标题栏*/
.header {
    font-size: 18px;
    font-weight: bold;
    text-align: center;

    .title {
        font-weight: bold;
    }

    .subtitle {
        margin-top: 8px;
        font-size: 14px;
    }
}

/*按钮栏*/
.button-box {
    display: flex;
    align-items: center;
    margin-top: 16px;
    gap: 8px;
}

/*状态栏*/
.status-box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 16px;
    gap: 8px;

    .status-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
    }

    .status-title {
        font-weight: bold;
    }

    .status-content {
        padding: 0 16px;
    }
}
</style>
