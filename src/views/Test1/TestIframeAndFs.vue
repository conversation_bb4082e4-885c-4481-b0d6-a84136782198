<template>
    <div class="container_2209051450">
        <div v-show="debug">
            <div class="input-box">
                <span>{{ h5Call ? '已连接' : '未连接' }}</span>
                <van-button size="small" @click="initH5Call">连接</van-button>
                <van-button size="small" @click="onClickLaunchCall">呼叫</van-button>
                <van-button size="small" @click="onClickHangup">挂断 & 断开连接</van-button>
            </div>

            <div class="info">当前iframe地址：{{ iframeUrl || '（空）' }}
                <span
                    style="color: red; cursor: pointer;"
                    @click="onClickChangeUrl('about:blank')">
                清空
            </span>
            </div>
            <div class="input-box">
                泰康测试产品：
                <van-button
                    size="small"
                    plain
                    @click="onClickChangeUrl('http://localhost/marketfront/insurance/ZYBX/IYBTK08/IndexTest')">
                    本地
                </van-button>
                <van-button
                    size="small"
                    plain
                    @click="onClickChangeUrl('http://uat.bountech.com/marketfront/insurance/ZYBX/IYBTK08/IndexTest')">
                    测试
                </van-button>

                <van-button size="small"
                    @click="onClickChangeUrl('https://channel.rrbxw.com/mktprod/rp/ZYBX/IYBTK08/IndexTest')">
                    生产
                </van-button>
            </div>
            <div class="input-box">
                优保华瑞赠险：
                <van-button
                    size="small"
                    plain
                    @click="onClickChangeUrl('http://localhost/marketfront/insurance/ZYBX/YBHNZX03/Index16')">
                    本地
                </van-button>
                <van-button
                    size="small"
                    plain
                    @click="onClickChangeUrl('http://uat.bountech.com/marketfront/insurance/ZYBX/YBHNZX03/Index16')">
                    测试
                </van-button>

                <van-button size="small"
                    @click="onClickChangeUrl('https://zhelaibao.sknii.cn/marketfront/insurancecdn/ZYBX/YBHNZX03/Index16')">
                    生产
                </van-button>
            </div>
            <div class="input-box">
                <input class="info" style="width: 80%;" v-model.trim="customUrl"/>
                <van-button size="small" @click="onClickChangeUrl(customUrl)">更改</van-button>
            </div>
        </div>

        <iframe class="iframe" :src="iframeUrl"/>
    </div>
</template>

<script>
import { H5Call } from '@/utils/h5-call';

export default {
    name: "TestIframe",
    data() {
        return {
            iframeUrl: 'about:blank',
            customUrl: '',
            h5Call: null,
            debug: false,
        };
    },
    created() {
        const { isDebug } = this.$route.query || {}
        this.debug = isDebug === '1'
        console.log('debug', this.debug)

        // this.iframeUrl = 'http://uat.bountech.com/marketfront/insurance/ZYBX/YBHNZX03/Index16'
        this.iframeUrl = 'https://zhelaibao.sknii.cn/marketfront/insurancecdn/ZYBX/YBHNZX03/Index16'
    },
    async mounted() {
        // 检查音频权限
        await this.checkAudioCondition()
        // 监听iframe页面消息
        window.addEventListener('message', (event) => {
            const data = event.data;

            if (data && data.type === 'OPEN_PAYMENT') {
                const payUrl = data.url;
                // 方案 A：新窗口打开
                window.open(payUrl, '_blank');
                // 方案 B：当前窗口跳转（不推荐，会丢失当前页面状态）
                // window.location.href = payUrl;
            } else if (data && data.type === 'CLICK_EVENT') {
                // 子页面触发点击事件，开始通话并播放音频
                this.handleFirstClick();
            }
        })
    },
    methods: {
        handleFirstClick() {
            console.log('handleFirstClick')
            if (!!this.h5Call) {
                this.h5Call.destroyJanus()
                this.h5Call = null
            } else {
                // 不是调试模式，自动注册账号
                if (!this.debug) {
                    this.initH5Call()
                }
            }
        },
        initH5Call() {
            if (!!this.h5Call) {
                this.h5Call.endCall()
                this.h5Call = null
            }
            const { channel } = this.$route.query || {}
            this.h5Call = new H5Call({ channel })
            // 将URL参数的调试模式更新到H5Call实例中
            this.h5Call.debug = this.debug
            if (this.h5Call) {
                this.h5Call.initInstance()
            }
        },
        onClickChangeUrl(url = '') {
            this.iframeUrl = url;
        },
        checkAudioCondition() {
            return new Promise(async (resolve, reject) => {
                try {
                    // 检测音频输入设备（麦克风）
                    // 尝试申请麦克风权限
                    await navigator.mediaDevices.getUserMedia({
                        audio: true,
                        video: false
                    })
                    resolve(true)
                } catch (e) {
                    console.error('没有麦克风权限')
                    alert('请检查浏览器麦克风权限')
                    reject(false)
                }
            })
        },
        onClickLaunchCall() {
            if (!this.h5Call) {
                this.initH5Call()
            }
            this.h5Call && this.h5Call.startCall()
        },
        onClickHangup() {
            this.h5Call && this.h5Call.endCall()
        }
    }
};
</script>

<style lang="less" scoped>
.container_2209051450 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    font-size: 12px;

    .input-box {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding: 4px;
        gap: 4px;
    }

    .info {
        padding: 4px;
        line-height: 1;
        word-break: break-all;
    }

    .iframe {
        overflow: hidden;
        // width: 90%;
        // height: 90%;
        // margin: 4px;
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
    }
}
</style>
