<template>
	<div class="container_2209051130">
		<div class="item">
			<span>缓存信息：</span>
			<input v-model="inputText" class="input" maxlength="20" placeholder="请输入要缓存的信息" type="text">
		</div>
		<div class="item">
			<span>缓存key：</span>
			<input v-model="inputKey" class="input" maxlength="20" placeholder="请输入缓存Key" type="text">
		</div>
		<div class="button" @click="onButtonClick">
			保存
		</div>
	</div>
</template>

<script>

import { Toast } from "vant";
import * as iframeTool from "./test";

export default {
	name: 'Test',
	data() {
		return {
			inputText: '',
			inputKey: '',
		};
	},
	mounted() {

		iframeTool.createIframe();

	},
	methods: {
		onButtonClick() {
			if (!this.inputText || !this.inputKey) {
				return Toast.fail('请输入缓存信息或key');
			}

			iframeTool.saveData(JSON.stringify({ key: this.inputKey, value: this.inputText }));

			Toast.success('调用缓存方法');
		},
		action() {
			// window.location.href = 'https://www.douban.com/doubanapp/dispatch?uri=%2Fmine%2Fmovie';

			// window.location.href = 'https://v.douyin.com/jEdUqFw/';
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2209051130 {
		width: 3.75rem;
		min-height: 100%;
		font-size: 0.18rem;
		background-color: #ffffff;

		iframe {
			display: block;
			width: 100%;
			height: 1.5rem;
		}

		.item {
			height: 0.5rem;
			line-height: 0.5rem;
			background-color: #f2f2f2;

			span {
				display: inline-block;
				width: 1rem;
				color: #333333;
				font-size: 0.16rem;
				text-align: center;
			}

			input {
				width: 2.7rem;
				border: none;
				outline: none;
				background-color: unset;
			}
		}

		.item + .item {
			margin-top: 0.1rem;
		}

		.button {
			margin: 0.2rem auto 0;
			width: 3rem;
			color: #ffffff;
			height: 0.5rem;
			line-height: 0.5rem;
			border-radius: 0.1rem;
			text-align: center;
			background-color: #0b78ff;
		}
	}
</style>
