<template>
	<div class="container_2209051420">
		<div class="item">
			<span>缓存key：</span>
			<input v-model="inputKey" class="input" maxlength="20" placeholder="请输入要读取信息的key" type="text">
		</div>
		<div class="item">
			<span>缓存信息：</span>
			<input v-model="inputText" class="input" maxlength="20" disabled placeholder="无缓存信息" type="text">
		</div>
		<div class="button" @click="onButtonClick">
			读取
		</div>
	</div>
</template>

<script>
import { Toast } from "vant";
import * as iframeTool from "./test";

export default {
	name: 'Test2',
	data() {
		return {
			inputKey: '',
			inputText: '',
		};
	},
	mounted() {
		iframeTool.createIframe();

		window.addEventListener("message", (event) => {
			const eventData = event.data || {};
			const { action, data } = eventData;
			if (action == 'GET_INFO') {
				try {
					this.inputText = JSON.parse(data);
				} catch (e) {
					this.inputText = data;
				}
				return Toast.success('读取缓存信息成功');
			}
		});
	},
	methods: {
		onButtonClick() {
			if (!this.inputKey) {
				return Toast.fail('请输入要读取信息的key');
			}

			iframeTool.loadData(this.inputKey)
		},
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2209051420 {
		width: 3.75rem;
		min-height: 100%;
		font-size: 0.18rem;
		background-color: #ffffff;

		iframe {
			display: block;
			width: 100%;
			height: 1.5rem;
		}

		.item {
			height: 0.5rem;
			line-height: 0.5rem;
			background-color: #f2f2f2;

			span {
				display: inline-block;
				width: 1rem;
				color: #333333;
				font-size: 0.16rem;
				text-align: center;
			}

			input {
				width: 2.7rem;
				border: none;
				outline: none;
				background-color: unset;
			}
		}

		.item + .item {
			margin-top: 0.1rem;
		}

		.button {
			margin: 0.2rem auto 0;
			width: 3rem;
			color: #ffffff;
			height: 0.5rem;
			line-height: 0.5rem;
			border-radius: 0.1rem;
			text-align: center;
			background-color: #0b78ff;
		}
	}
</style>
