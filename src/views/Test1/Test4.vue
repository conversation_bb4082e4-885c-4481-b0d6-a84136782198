<template>
    <div class="container_2209051450">
        <div class="btn-box">
            <van-button type="primary" @click="action(0)">测试环境</van-button>
            <van-button type="primary" @click="action(1)">生产环境</van-button>
        </div>
        <div class="list">
            <div class="item" v-for="v in list" :key="v.link" @click="handleClick(v.link)">
                {{ v.name }}:{{ v.link }}
            </div>
        </div>
        <iframe class="iframe" :src="iframeUrl" />
    </div>
</template>

<script>
export default {
    name: "Test4",
    data() {
        return {
            testLinks: [
                { name: '暖哇', link: 'http://uat.bountech.com/marketfront/insurance/ZYBX/NWZA12/Index1' },
                { name: '暖哇', link: 'http://uat.bountech.com/marketfront/insurance/ZYBX/NWTK03/Index1' },
                { name: '优保', link: 'http://uat.bountech.com/marketfront/insurance/ZYBX/YBGR02/Index1' },
                { name: '保通', link: 'http://uat.bountech.com/marketfront/insurance/ZYBX/IYBGR04/Index1' },
                { name: '保通', link: 'http://uat.bountech.com/marketfront/insurance/ZYBX/IYBTK10/Index1' },
                { name: '泰康', link: 'http://uat.bountech.com/marketfront/insurance/ZYBX/TK36/Index1' },
                { name: '泰康', link: 'http://uat.bountech.com/marketfront/insurance/ZYBX/TK40/Index1' },
            ],
            prodLinks: [
                { name: '暖哇', link: 'https://sr0llef8bo_5zb19brqip8t-3rz_7c0.aibangbaoxian.net/marketfront/insurancecdn/ZYBX/NWZA12/Index1' },
                { name: '暖哇', link: 'https://loveuserlovebaoxian.anbxyx.cn/marketfront/insurancecdn/ZYBX/NWTK03/Index1' },
                { name: '暖哇', link: 'https://loveuserlovebaoxian.anbxyx.cn/marketfront/insurancecdn/ZYBX/NWZA12/Index1' },
                { name: '优保', link: 'https://oiisf.zabxib.com/marketfront/insurancecdn/ZYBX/YBGR02/Index1' },
                { name: '优保', link: 'https://zhelaibao.sknii.cn/marketfront/insurancecdn/ZYBX/YBGR02/Index1' },
                { name: '保通', link: 'https://channel.zhelibao.com/mktprod/rp/ZYBX/IYBGR04/Index1' },
                { name: '保通', link: 'https://channel.rrbxw.com/mktprod/rp/ZYBX/IYBTK10/Index1' },
                { name: '泰康', link: 'https://channel.rrbxw.com/mktprod/rp/ZYBX/TK36/Index1' },
                { name: '泰康', link: 'https://ins.cminsurances.com/marketfront/insurancecdn/ZYBX/TK40/Index2' },
            ],
            list: [],
            iframeUrl: '',
        }
    },
    mounted() {
        this.list = this.testLinks;

    },
    methods: {
        action(value) {
            this.list = value == 0 ? this.testLinks : this.prodLinks;
        },

        handleClick(link) {
            this.iframeUrl = link;
        },
    },
}
</script>

<style lang="less" scoped type="text/less">
.container_2209051450 {
    font-size: 0.16rem;

    .btn-box {
        display: flex;
        align-items: center;
        justify-content: space-around;
    }

    .list {
        margin: 10px 0;

        .item {
            padding: 5px 0;
            word-break: break-all;
            text-decoration: underline;
            cursor: pointer;
        }
    }

    .iframe {
        width: 100%;
        height: 600px;
        border: none;
    }
}
</style>
