const iframe_id = 'id_202405221148';
const timestamp = Date.now();
const iframeLink = `https://cdns.bountech.com/marketfront/insurancecdn/ZYBX/Test1?time=${timestamp}`;
// const iframeLink = `http://192.168.23.5:1024/marketfront/insurance/ZYBX/Test1?time=${timestamp}`;


export const createIframe = () => {
    try {
        if (document.getElementById(iframe_id)) {
            return;
        }

        const iframe = document.createElement('iframe');
        iframe.src = iframeLink;
        iframe.id = iframe_id;
        iframe.style.display = 'none';
        document.body.appendChild(iframe);

        iframe.onload = function () {
            console.log('iframe loaded');
        }

        iframe.onerror = function () {
            console.log('Error loading iframe');
        }

    } catch (error) {

    }
}

export const saveData = (data = '') => {
    try {
        const iframeNode = document.getElementById(iframe_id);

        if (!iframeNode) {
            return;
        }

        iframeNode.contentWindow.postMessage({
            action: "SAVE_DATA",
            data: data,
        }, iframeLink);

    } catch (error) {

    }
}

export const loadData = (key = '') => {
    try {
        const iframeNode = document.getElementById(iframe_id);

        if (!iframeNode) {
            return;
        }

        iframeNode.contentWindow.postMessage({
            action: "LOAD_DATA",
            data: JSON.stringify({ key }),
        }, iframeLink);

    } catch (error) {

    }
}
