<template>
    <div class="container-404">
        <img class="icon_exception" :src="iconException" alt="">
        <p class="exception_msg">{{msg}}</p>
    </div>
</template>

<script>
    import iconException from '@/assets/imgs/common/icon_exception.png'
    import {actionTracking} from '@/assets/js/api'

    const messages = {
        'channelException': '渠道号无效 ~'
    }
    export default {
        name: "Exception404",
        data() {
            return {
                messages,
                iconException,
                msg: '',
                channel: 1,
                infoKey: '',
            }
        },
        created() {
        },
        mounted() {
            this.init()
        },
        methods: {
            init() {
                let inQry = this.$route.query || {} // msg,channel,infoKey
                let {channel,infoKey,msg} = inQry
                this.channel = channel || 1
                this.infoKey = infoKey || 'exception'
                this.msg = this.messages[msg] || '页面开小差了 ~'

                this._actionTracking('首页')
            },
            _actionTracking(name) {
                actionTracking({
                    page: `异常页面(${this.infoKey})-${name}`,
                    channel: this.channel,
                    infoKey: this.infoKey,
                }).finally(() => {

                });
            },
        }
    }
</script>

<style scoped lang="less" type="text/less">
    .container-404 {
        background-color: #f7f7f7;
        position: fixed;
        width: 100%;
        height: 100%;
        .icon_exception {
            display: block;
            width: 0.6rem;
            height: 0.6rem;
            position: absolute;
            top: 30%;
            left: 50%;
            transform: translateX(-50%);
        }
        .exception_msg {
            width: 80%;
            text-align: center;
            font-size: 0.18rem;
            position: absolute;
            top: calc(30% + 0.85rem);
            left: 50%;
            transform: translateX(-50%);
            color: #666;
        }
    }
</style>