<template>
    <div class="container_220711">

        <div @click="onButtonClick">点击跳转</div>
    </div>
</template>

<script>

export default {
    name: 'Location',
    data() {
        return {

        }
    },
    mounted() {

        setTimeout(() => {
            this.jumpToAppMarket();
        }, 2000);

    },
    methods: {
        onButtonClick() {

            this.jumpToAppMarket();

        },

        jumpToAppMarket() {
            const brand = this.judgeBrand();
            if (brand === 'iphone') {    //跳转到苹果商店
                window.location.href = 'https://apps.apple.com/cn/app/id1483763034';
            } else if (brand === 'huawei') {    //跳转到华为商店    
                window.location.href = 'appmarket://details?id=com.pingan.lifecircle';
                // window.location.href = 'https://appmarket-h5.cloud.honor.com/h5/share/latest/index.html?shareId=1849373471531016192&shareTo=copyLink';
            } else if (brand === 'oppo') {    //跳转到oppo商店
                window.location.href = 'market://details?id=com.pingan.lifecircle';
            } else if (brand === 'vivo') {    //跳转到vivo商店
                window.location.href = 'vivomarket://details?id=com.pingan.lifecircle';
            } else if (brand === 'xiaomi') {    //跳转到小米商店
                window.location.href = 'https://app.mi.com/details?id=com.pingan.lifecircle';
            } else {
                window.location.href = 'https://sj.qq.com/appdetail/com.pingan.lifecircle';
            }
        },

        judgeBrand() {
            const userAgent = navigator.userAgent || "";
            const platform = navigator.platform || "";
            const maxTouchPoints = navigator.maxTouchPoints || 1;

            if (/iPad|iPhone|iPod/i.test(userAgent) || platform === "MacIntel" && maxTouchPoints > 1) {
                return 'iphone';
            }

            if (/huawei|honor/i.test(userAgent)) {
                return 'huawei';
            }

            if (/oppo|HeyTap/i.test(userAgent)) {
                return 'oppo';
            }

            if (/vivo/i.test(userAgent)) {
                return 'vivo';
            }

            if (/xiaomi|mi\s|redmi/i.test(userAgent)) {
                return 'xiaomi';
            }

            return 'other';
        },
    }
}
</script>

<style lang="less" scoped type="text/less">
.container_220711 {
    min-height: 100%;
    font-size: 0.15rem;
    background-color: #ffffff;

    .loader {
        width: fit-content;
        font-weight: bold;
        font-family: monospace;
        font-size: 30px;
        clip-path: inset(0 3ch 0 0);
        animation: l4 1s steps(4) infinite;
    }

    .loader:before {
        content: "Loading...";
    }

    @keyframes l4 {
        to {
            clip-path: inset(0 -1ch 0 0);
        }
    }

    .button {
        padding: 0.1rem 0;
        text-align: center;
        font-size: 0.16rem;
    }

    .circle {
        margin: 0 0.2rem;
        text-align: justify;
        height: 0;
        overflow: hidden;
        transition: 0.5s;
        background-color: darkblue;
        // transform-origin: center top;
        // transform: scaleY(0);
    }

    // .button:hover + .circle {
    // 	transform: scaleY(1);
    // }
}
</style>
