// https://github.com/rrweb-io/rrweb
import { compressToBase64 } from 'lz-string';
import { pack, record } from 'rrweb';
import { uploadYXWebVideo } from '@/api/rrweb_api';

let allEvents = [];

const startRecord = () => {
    record({
        packFn: pack,
        collectFonts: true,
        emit(event) {
            if (allEvents.length <= 0) {
                console.log('页面回溯开始录制');
            }
            allEvents.push(event);
        },
    });
}

const uploadWebVideo = (options) => {
    if (!options.insMallOrderNo || allEvents.length <= 0) {
        return;
    }

    const compressedEvents = compressToBase64(JSON.stringify(allEvents));
    uploadYXWebVideo({
        infoKey: options.planKey || options.infoKey || options.productKey,
        orderId: options.insMallOrderNo,
        orderType: '个险',
        requestUrl: window.location.href.split('?')[0],
        pageName: options.identifier,
        eventData: compressedEvents,
    });
}

export default {
    startRecord,
    uploadWebVideo,
}
