<template>
	<div class="container_2311091053"></div>
</template>

<script>
export default {
	name: "ZALYRecord",
	props: {
		page: {
			type: String,
			default: '',
		}
	},
	mounted() {
		this.loadIseeScript();
		this.loadMonitorScript();
	},
	methods: {
		loadMonitorScript() {
			if (!this.page) {
				return;
			}

			const isExist = document.querySelector('#id_2406271351');
			if (isExist) {
				return;
			}

			try {
				const scriptURL = 'https://seraph-cdn.zhongan.io/js/monitor.2.4.0.js';
				const jsNode = document.createElement('script');
				jsNode.id = 'id_2406271351';
				jsNode.src = scriptURL;
				jsNode.onload = () => {
					window.MonitorJS && window.MonitorJS({
						seraphId: 'c8b013103ea6084d',    // 必填, 站点ID，接入管理中申请的站点ID
						env: ['prod', 'production', 'iybprod', 'kdprod'].includes(process.env.VUE_APP_TITLE) ? 'prd' : 'test',    // 必填, 环境, 可取值 test、pre、prd
						H5Version: '1.0.0',    // 不需要区分版本可以填 1.0.0,
						extendsInfo: {
							pageInfo: this.page,
						}
					});
				};

				jsNode.onerror = () => {
					console.log('监控SDK加载失败');
				};

				document.body.appendChild(jsNode);
			} catch (e) {
				console.log(e);
			}
		},

		loadIseeScript() {
			const isExist = document.querySelector('#id_2311091053');
			if (isExist) {
				return this.getIseeBiz();
			}

			try {
				const scriptURL = ['prod', 'production', 'iybprod', 'kdprod'].includes(process.env.VUE_APP_TITLE) ? 'https://cdnzaib.zhonganib.com/eye.js' : 'https://cdnzaib-test.zhonganib.com/eye.js';
				const jsNode = document.createElement('script');
				jsNode.id = 'id_2311091053';
				jsNode.src = scriptURL;
				jsNode.onload = () => {
					this.getIseeBiz();
				};

				jsNode.onerror = () => {
					console.log('回溯SDK加载失败');
				};

				document.body.appendChild(jsNode);
			} catch (e) {
				console.log(e);
			}
		},

		getIseeBiz() {
			requestAnimationFrame(() => {
				const iseeBiz = window.iseeBiz;
				if (!iseeBiz) {
					return this.getIseeBiz();
				}
				console.log(iseeBiz);
				this.$emit('SetIseeBiz', iseeBiz);
			});

		},
	}
}
</script>

<style scoped lang="less" type="text/less">
	.container_2311091053 {
		max-height: 1px;
		max-width: 1px;
	}
</style>
