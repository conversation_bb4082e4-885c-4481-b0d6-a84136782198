import CryptoJS from "crypto-js";

window.CryptoJS = CryptoJS;

export const oneClickLoginJG12 = (data, loadCallback, resultCallback, eventCallback) => {
    const SDK_CONFIG = {
        maxRetries: 6, // 最大重试次数
    };

    const createScript = (retryCount = 0) => {
        const script = document.createElement('script');
        const sdkUrl = window.location.href.indexOf('jgsdk') > 0 ? 'https://jverification.jiguang.cn/scripts/jverification-web.5.2.1.min.js' : 'https://jverification.jiguang.cn/scripts/jverification-web.5.3.0.min.js';
        script.src = sdkUrl;
        const head = document.getElementsByTagName('head')[0];
        head.appendChild(script);
        // ie 支持 onreadystatechange
        script.onload = script.onreadystatechange = () => {
            if ((!script.readyState || /loaded|complete/.test(script.readyState))) {
                script.onload = script.onreadystatechange = null;

                window.JVerificationInterface.addEventListen({
                    event: function (data) {
                        console.log('事件监听', JSON.stringify(data));
                        const { code, message } = data;
                        eventCallback && eventCallback(code, message);
                    }
                });

                window.JVerificationInterface.init({
                    appkey: "5d0ae9dd6f6ecc05c378cc12", // 极光官网中创建应用后分配的 appkey，必填
                    debugMode: true, // 设置是否开启 debug 模式。true 则会打印更多的日志信息。设置 false 则只会输出 w、e 级别的日志。
                    domainName: window.location.origin, //since5.0.0 开发者域名 以极光的DEMO域名为例
                    success: function (data) {
                        //TODO 初始化成功回调
                        console.log('初始化成功', data);

                        window.JVerificationInterface.loginAuth({
                            operater: 'CT', // 运营商类型（移动/联通/电信）
                            type: "full", // 全屏授权页
                            success: function (data) {
                                console.log('登录成功', data);
                                const { code, content, message } = data;
                                if (code == 0) {
                                    return resultCallback && resultCallback(true, content);
                                }
                                return resultCallback && resultCallback(false, message);
                            },
                            fail: function (err) {
                                loadCallback && loadCallback(false);
                            }
                        });
                    },
                    fail: function (data) {
                        //TODO 初始化失败回调
                        console.log('初始化失败', data);
                        loadCallback && loadCallback(false);
                    },
                });
            }
        };
        // 错误处理
        script.onerror = () => {
            head.removeChild(script);

            if (retryCount < SDK_CONFIG.maxRetries) {
                retryCount++;
                console.log(`第 ${retryCount} 次重试`);
                setTimeout(() => {
                    createScript(retryCount);
                }, 1500);
            }
        };
    }

    createScript();
}