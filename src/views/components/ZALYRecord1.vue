<template>
    <div class="container_2504011700"></div>
</template>

<script>
export default {
    name: "ZALYRecord",
    props: {
        page: {
            type: String,
            default: '',
        }
    },
    mounted() {
        this.loadIseeScript();
        this.loadMonitorScript();
    },
    methods: {
        loadMonitorScript() {
            if (!this.page) {
                return;
            }

            const SDK_CONFIG = {
                maxRetries: 6, // 最大重试次数
            };

            const createScript = (retryCount = 0) => {
                const script = document.createElement('script');
                script.src = 'https://seraph-cdn.zhongan.io/js/monitor.2.4.0.js';
                const head = document.getElementsByTagName('head')[0];
                head.appendChild(script);
                // ie 支持 onreadystatechange
                script.onload = script.onreadystatechange = () => {
                    if ((!script.readyState || /loaded|complete/.test(script.readyState))) {
                        script.onload = script.onreadystatechange = null;
                        window.MonitorJS && window.MonitorJS({
                            seraphId: 'c8b013103ea6084d',    // 必填, 站点ID，接入管理中申请的站点ID
                            env: ['prod', 'production', 'iybprod', 'kdprod'].includes(process.env.VUE_APP_TITLE) ? 'prd' : 'test',    // 必填, 环境, 可取值 test、pre、prd
                            H5Version: '1.0.0',    // 不需要区分版本可以填 1.0.0,
                            extendsInfo: {
                                pageInfo: this.page,
                            }
                        });
                    }
                };
                // 错误处理
                script.onerror = () => {
                    head.removeChild(script);

                    if (retryCount < SDK_CONFIG.maxRetries) {
                        retryCount++;
                        console.log(`第 ${retryCount} 次重试`);
                        setTimeout(() => {
                            createScript(retryCount);
                        }, 1500);
                    }
                };
            }

            createScript();
        },

        loadIseeScript() {
            const SDK_CONFIG = {
                maxRetries: 6, // 最大重试次数
            };

            const createScript = (retryCount = 0) => {
                const script = document.createElement('script');
                script.src = ['prod', 'production', 'iybprod', 'kdprod'].includes(process.env.VUE_APP_TITLE) ? 'https://cdnzaib.zhonganib.com/eye.js' : 'https://cdnzaib-test.zhonganib.com/eye.js';
                const head = document.getElementsByTagName('head')[0];
                head.appendChild(script);
                // ie 支持 onreadystatechange
                script.onload = script.onreadystatechange = () => {
                    if ((!script.readyState || /loaded|complete/.test(script.readyState))) {
                        script.onload = script.onreadystatechange = null;
                        this.getIseeBiz();
                    }
                };
                // 错误处理
                script.onerror = () => {
                    head.removeChild(script);

                    if (retryCount < SDK_CONFIG.maxRetries) {
                        retryCount++;
                        console.log(`第 ${retryCount} 次重试`);
                        setTimeout(() => {
                            createScript(retryCount);
                        }, 1500);
                    }
                };
            }

            createScript();
        },

        getIseeBiz() {
            requestAnimationFrame(() => {
                const iseeBiz = window.iseeBiz;
                if (!iseeBiz) {
                    return this.getIseeBiz();
                }
                console.log(iseeBiz);
                this.$emit('SetIseeBiz', iseeBiz);
            });

        },
    }
}
</script>

<style scoped lang="less" type="text/less">
.container_2504011700 {
    max-height: 1px;
    max-width: 1px;
}
</style>
