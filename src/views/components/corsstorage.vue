<template>
	<div class="container_2209051450">
		缓存中间页
	</div>
</template>

<script>
export default {
	name: "Test1",
	mounted() {
		window.addEventListener("message", (event) => {
			console.log('iframe页面收到的信息 =>', event);

			try {
				if (window.parent != event.source) {
					return;
				}

				const eventData = event.data || {};
				const { action, data } = eventData;
				if (action == 'SAVE_DATA') {
					const obj = JSON.parse(data);
					localStorage.setItem(obj.key, obj.value);
				} else if (action == 'LOAD_DATA') {
					const obj = JSON.parse(data);
					const value = localStorage.getItem(obj.key);
					event.source.postMessage({
						action: "GET_INFO",
						data: value,
					}, event.origin);
				}
			} catch (error) {

			}
		});
	},
}
</script>

<style lang="less" scoped type="text/less">
	.container_2209051450 {
		font-size: 0.16rem;
	}
</style>
