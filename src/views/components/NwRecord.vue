<template>
	<div class="container_2210241100">

	</div>
</template>

<script>
export default {
	name: "NwRecord",
	computed: {
		scriptURL() {
			const environment = process.env.VUE_APP_TITLE || 'prod';

			return ['prod', 'production', 'iybprod', 'kdprod'].includes(environment) ? 'https://isee-plugins.aibangbaoxian.net/eye.js' : 'https://isee-plugins-test.aibangbaoxian.net/eye.js';
		}
	},
	mounted() {
		window.getIseeBizLog = () => {
			try {
				if (window.iseeBiz) {
					return window.iseeBiz;
				}
				return window.getIseeCheckInfos ? JSON.stringify(window.getIseeCheckInfos()) : 'eye.js没有加载到';
			} catch (error) {


			}

			return '';
		}

		const getBusinessId = () => {
			requestAnimationFrame(() => {
				const iseeBiz = window.iseeBiz;
				if (!iseeBiz) {
					return getBusinessId();
				}

				console.log(`暖哇回溯视频Id => `, iseeBiz);
				this.$emit('SetIseeBiz', iseeBiz)
			});
		}

		if (window.iseeHasInit) {
			return getBusinessId();
		}

		const jsNode = document.createElement('script');
		jsNode.src = this.scriptURL;
		document.body.appendChild(jsNode);
		jsNode.onload = () => {
			return getBusinessId();
		}
		jsNode.onerror = () => {
			console.log('暖哇回溯SDK加载失败');
		};
	},
}
</script>

<style scoped lang="less" type="text/less">
	.container_2210241100 {
		height: 1px;
		width: 1px;
	}
</style>
