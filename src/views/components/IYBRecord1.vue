<template>
    <div></div>
</template>

<script>
export default {
    name: "IybRecord1",
    props: {
        obj: {
            type: Object,
            default: () => ({})
        },
    },
    data() {
        return {

        }
    },
    computed: {
        scriptUrl() {
            const environment = process.env.VUE_APP_TITLE;
            return ['prod', 'production', 'iybprod', 'kdprod'].includes(environment) ? 'https://static.iyb.tm/web/lib/recorder/channel/recorder.js' : 'https://static.iyb.tm/web/lib/recorder/channel/recorder-uat.js';
        }
    },
    mounted() {
        // 默认会监听，从结果页回退不重新创建可回溯码
        window.recordOpts = {
            isPopstate: false,
        }

        const SDK_CONFIG = {
            maxRetries: 6, // 最大重试次数
        };

        const createScript = (retryCount = 0) => {
            const script = document.createElement('script');
            script.src = this.scriptUrl;
            const head = document.getElementsByTagName('head')[0];
            head.appendChild(script);
            // ie 支持 onreadystatechange
            script.onload = script.onreadystatechange = () => {
                if ((!script.readyState || /loaded|complete/.test(script.readyState))) {
                    script.onload = script.onreadystatechange = null;
                    this.getSetId();
                }
            };
            // 错误处理
            script.onerror = () => {
                head.removeChild(script);

                if (retryCount < SDK_CONFIG.maxRetries) {
                    retryCount++;
                    console.log(`第 ${retryCount} 次重试`);
                    setTimeout(() => {
                        createScript(retryCount);
                    }, 1500);
                }
            };
        }

        createScript();
    },

    methods: {
        getSetId() {
            const getBusinessId = () => {
                // console.log('外层-获取业务Id');
                requestAnimationFrame(() => {
                    // console.log('内层-获取业务Id');
                    if (window.airWalker && window.airWalker.getSetId) {
                        window.airWalker.getSetId().then(setId => {
                            this.obj.recordSetId = setId;
                            this.obj.traceBackUuid = setId;
                            console.log(`保通回溯视频Id(新) => `, setId);
                        }).catch(() => {
                            console.error('获取 setId 失败');
                        });
                    } else {
                        getBusinessId();
                    }
                });
            }

            const getVisitorId = () => {
                // console.log('外层-获取业务Id');
                requestAnimationFrame(() => {
                    // console.log('内层-获取业务Id');
                    if (window.airWalker && window.airWalker.getVisitorId) {
                        window.airWalker.getVisitorId().then(visitorId => {
                            this.obj.visitorId = visitorId;
                            console.log(`保通回溯视频设备指纹 => `, visitorId);
                        }).catch(() => {
                            console.error('获取 VisitorId 失败');
                        });
                    } else {
                        getVisitorId();
                    }
                });
            }

            getBusinessId();
            getVisitorId();
        },
    }
}
</script>

<style scoped lang="less" type="text/less"></style>
