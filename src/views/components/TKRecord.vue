<template>
	<div class="container_2210241100"></div>
</template>

<script>
import { isCardNo, isPersonName } from "@/assets/js/common";

export default {
	name: "TKRecord",
	props: { recordObj: Object },
	computed: {
		recordScript() {
			const environment = process.env.VUE_APP_TITLE;
			return ['prod', 'production', 'iybprod', 'kdprod'].includes(environment) ? 'https://tbonline.tk.cn/SDK/BestWatcher.min.js' : 'https://traceback.tk.cn/SDK/BestWatcher.min.js';
		},

		recordHost() {
			const environment = process.env.VUE_APP_TITLE;
			return ['prod', 'production', 'iybprod', 'kdprod'].includes(environment) ? 'https://tbonline.tk.cn' : 'http://traceback.tk.cn';
		},
	},
	mounted() {
        const SDK_CONFIG = {
            maxRetries: 10, // 最大重试次数
        };

        const createScript = (retryCount = 0) => {
            const script = document.createElement('script');
            script.src = this.recordScript;
            const head = document.getElementsByTagName('head')[0];
            head.appendChild(script);
            // ie 支持 onreadystatechange
            script.onload = script.onreadystatechange = () => {
                if ((!script.readyState || /loaded|complete/.test(script.readyState))) {
                    script.onload = script.onreadystatechange = null;
                    this.startRecord();
                }
            };
            // 错误处理
            script.onerror = () => {
                head.removeChild(script);

                if (retryCount < SDK_CONFIG.maxRetries) {
                    retryCount++;
                    console.log(`第 ${retryCount} 次重试`);
                    setTimeout(() => {
                        createScript(retryCount);
                    }, 1500);
                }
            };
        }

        createScript();
	},

	methods: {
		startRecord() {
            if (!window.webRecord) {
                return setTimeout(() => {
                    this.startRecord();
                }, 500);
            }

			webRecord.startRecord({
				host: this.recordHost, // 回溯数据上传路径
				version: 1,
				debug: false,
				source: '直营事业部',
				cache: false,
				extras: {},
				currentPart: 1,
				exclude: [new RegExp('\\S*/marketprod/\\S*', 'g')],
				packageSize: 5000,
				saveInterval: 200,
				inlineStylesheet: true,
			});

			const getBusinessId = () => {
				requestAnimationFrame(() => {
					const tb_uuid = webRecord.businessId;
					if (!tb_uuid) {
						return getBusinessId();
					}

					console.log(`回溯视频Id => `, tb_uuid);
					this.recordObj.traceBackUuid = tb_uuid;

                    this.uploadVideo();
				});
			}

			getBusinessId();
		},

		uploadVideo() {
			const { fromId, productCode, productName, proposal_id, name1, idCard1, policyNo } = this.recordObj;

			try {
				webRecord.collectBusInfo({
					fromId: fromId || '74899',
					productCode: productCode || 'N20210025',
					productName: productName || '泰康全民保2021',
					source: '直营事业部',
					orderNo: proposal_id ? (proposal_id.indexOf('TK000008') < 0 ? `TK000008_${proposal_id}` : proposal_id) : '',
					applyName: isPersonName(name1) ? name1 : '',
					certifyCode: isCardNo(idCard1) ? idCard1 : '',
					policyNo: policyNo,
					reservation1: '1.0.1',
				});
			} catch (e) {

			}
		},
		endRecord() {
			try {
				webRecord.finish(true, res => {
					console.log(`回溯录制结果 =>`, res);
				});
			} catch (e) {

			}
		},
	}
}
</script>

<style lang="less" scoped type="text/less">
	.container_2210241100 {
		height: 1px;
		width: 1px;
	}
</style>
