export const oneClickLoginForYB = (data, loadCallback, resultCallback) => {
    const SDK_CONFIG = {
        maxRetries: 6, // 最大重试次数
    };

    const iseeBiz = (data || {}).iseeBiz;


    // loadCallback && loadCallback(true);

    // setTimeout(() => {
    //     resultCallback(true, 'de8494e2ff709bb20a0df1ee960bea45')
    //     // resultCallback(false, '运营商授权失败')

    // }, 2000);

    // return;

    const createScript = (retryCount = 0) => {
        const script = document.createElement('script');
        script.src = 'https://cdnfin.zhonganib.com/jrfed-zaab-marketing/product/lingtong0.1.0.js';
        const head = document.getElementsByTagName('head')[0];
        head.appendChild(script);
        // ie 支持 onreadystatechange
        script.onload = script.onreadystatechange = () => {
            if ((!script.readyState || /loaded|complete/.test(script.readyState))) {
                script.onload = script.onreadystatechange = null;

                const lt = new window.__LingTong();
                lt.init({
                    channelType: 'rptf',
                    channelNo: '385003',
                    tenantCode: '300012965698',
                    appId: '300012965698',
                    appKey: '39B9F10D22771BB90915563872FCAD27',
                    iseeBiz: iseeBiz,
                    mountedNode: document.querySelector('#wrap123'),
                    onLoad: (data) => {
                        console.log('onload data ===>', data);
                        const { status } = data;
                        if (status == 'fail' || status == 'success') {
                            loadCallback && loadCallback(status == 'success');
                        }
                    },
                    resultCb: (res) => {
                        console.log('resultCb data ===>', res);
                        const { status, reason, data } = res;
                        if (status == 'fail') {
                            return resultCallback && resultCallback(false, reason);
                        }
                        if (status == 'success') {
                            resultCallback && resultCallback(true, data.result.mobile);
                        }
                    },
                    contractClick: (data) => {
                        console.log('contractClick data ===> ', data);
                    }
                });
            }
        };
        // 错误处理
        script.onerror = () => {
            head.removeChild(script);

            if (retryCount < SDK_CONFIG.maxRetries) {
                retryCount++;
                console.log(`第 ${retryCount} 次重试`);
                setTimeout(() => {
                    createScript(retryCount);
                }, 1500);
            }
        };
    }

    createScript();
}